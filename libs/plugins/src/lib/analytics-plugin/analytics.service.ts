import { inject, Injectable } from '@angular/core';
import { WINDOW } from '@libs/core';

@Injectable({
  providedIn: 'root',
})
export class AnalyticsService {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private window: any = inject(WINDOW);

  logEvent(event: string, category: string, payload: object = {}) {
    this.window.gtag('event', event, {
      category,
      ...payload,
    });
  }

  log(event: string, payload: object = {}) {
    console.debug('analytics.event', event);
    this.window.gtag('event', event, payload);
  }
}
