import { Action, State, StateContext, StateToken } from '@ngxs/store';
import { inject, Injectable } from '@angular/core';
import { AnalyticsAction } from './analytics.action';
import { AnalyticsType } from './analytics.model';
import { AnalyticsService } from '@libs/plugins';

export const AnalyticsStateToken = new StateToken<AnalyticsType>('AnalyticsState');

@Injectable()
@State<AnalyticsType>({
  name: AnalyticsStateToken,
  defaults: {
    events: [],
  },
})
export class AnalyticsState {
  analyticsService = inject(AnalyticsService);

  @Action(AnalyticsAction)
  overrideConfig(ctx: StateContext<AnalyticsType>, { event, payload }: AnalyticsAction) {
    this.analyticsService.log(event, payload);
  }
}
