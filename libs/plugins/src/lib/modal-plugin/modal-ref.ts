import { Subject } from 'rxjs';
import { OverlayRef } from '@angular/cdk/overlay';
import { TemplateRef, Type } from '@angular/core';

import { ModalComponent } from './modal.component';
import { ModalCloseEvent, ModalCLoseEventType, ModalData } from './modal';

export class ModalRef<ModalReturnDataType = unknown> {
  onClose$ = new Subject<ModalCloseEvent<ModalReturnDataType>>();
  modalComponentInstance: ModalComponent;
  modalId: string;

  constructor(
    private id: string,
    private overlay: OverlayRef,
    public content: string | TemplateRef<unknown> | Type<unknown>,
    public options: ModalData<unknown>,
  ) {
    this.modalId = this.id;
    this.listenForCloseEvents();
  }

  close(data?: ModalReturnDataType) {
    this._close('close', data ?? null);
  }

  private listenForCloseEvents() {
    this.overlay.keydownEvents().subscribe((event) => {
      if (event.key === 'Escape') {
        this._close('escape', null);
      }
    });

    this.overlay.backdropClick().subscribe(() => {
      this._close('backdropClick', null);
    });
  }

  private _close(type: ModalCLoseEventType, data: ModalReturnDataType) {
    this.onClose$.next({
      type,
      data,
    });
    this.onClose$.complete();
    this.overlay.dispose();
    this.modalComponentInstance.startExitAnimation();
  }
}
