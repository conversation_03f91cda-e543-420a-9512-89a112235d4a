import { inject, Injector, isDevMode, runInInjectionContext, signal } from '@angular/core';
import { assertInjector, coerceBooleanProperty, KnownLocalStorageKeys, LocalStorageService } from '@libs/core';
import { Environment } from '@libs/types';

let logged = false;

export function injectDebugMode(injector?: Injector) {
  injector = assertInjector(injectDebugMode, injector);

  return runInInjectionContext(injector, () => {
    const isDebugMode = signal(false);
    const localStorageService = inject(LocalStorageService);
    const config = inject(Environment.ENVIRONMENT);
    const environment = config.environment;
    const app = config.app;
    const allowedEnvironments = ['dev', 'test', 'testaws', 'local'];
    const debugModeValue = localStorageService.get(KnownLocalStorageKeys.DEBUG_MODE);
    const debugModeActive = coerceBooleanProperty(debugModeValue);
    const isAllowed = debugModeActive && allowedEnvironments.includes(environment);

    isDebugMode.set(isDevMode() || isAllowed);

    if (isDebugMode() && !logged) {
      console.log(app + ' is running in debug mode.');
      logged = true;
    }

    return isDebugMode;
  });
}
