import { inject, provideAppInitializer, Provider } from '@angular/core';
import { Router, NavigationError, Event } from '@angular/router';
import { filter } from 'rxjs';
import { IframeHandlerService } from './iframe-handler.service';

export function provideIframe(): Provider {
  return [provideAppInitializer(() => iframeInitializer())];
}

function iframeInitializer() {
  const router = inject(Router);
  const iframeHandlerService = inject(IframeHandlerService);

  if (iframeHandlerService.isInIframe()) {
    router.events
      .pipe(filter((event: Event) => event instanceof NavigationError))
      .subscribe((event: NavigationError) => {
        if (event.error?.message.includes('Cannot match any routes') || event.error?.message.includes('NG04002:')) {
          iframeHandlerService.sendMessageToParent({
            type: 'PAGE_NOT_FOUND',
            url: event.url,
          });
        }
      });
  }

  return;
}
