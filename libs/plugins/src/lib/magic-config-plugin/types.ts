/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injector, ViewContainerRef } from '@angular/core';
import { ActivatedRouteSnapshot, ResolveData } from '@angular/router';

export type MagicResolver = ResolveData | MagicBaseResolver;

export interface MagicConfigOptions {
  resolve?: MagicResolver[];
}

export interface MagicResolverModel {
  selector: any;
  action: () => any;
  next?: MagicResolverModel[];
}

export abstract class MagicBaseResolver {
  abstract resolve(route: ActivatedRouteSnapshot): MagicResolverModel[];
}

export interface MagicResolverRender {
  container: ViewContainerRef;
  injector: Injector;
  projectableNodes?: Node[][];
}
