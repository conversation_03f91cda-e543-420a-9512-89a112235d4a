import { ComponentRef, Injector, runInInjectionContext, Type } from '@angular/core';
import { catchError, forkJoin, map, Observable, of, Subject, switchMap } from 'rxjs';
import { flattenRouteTree, getResolver, getRootComponentSelectors, MagicResolverToObservable } from './utils';
import { Router } from '@angular/router';
import { MagicResolverService } from './magic-resolver.service';
import { MagicResolverRender } from './types';

export class MagicResolverDetect {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private defaults: Type<any>[];

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  constructor(component: Type<any> | Type<any>[]) {
    this.defaults = Array.isArray(component) ? component : [component];
  }

  get selectors() {
    return this.defaults.flatMap(getRootComponentSelectors).filter(Boolean);
  }

  get resolvers() {
    return MagicResolverService.getResolvers(this.selectors).filter(Boolean);
  }

  resolveComponent<T>(injector: Injector): Observable<T> {
    const targetSnapshot = injector.get(Router).routerState.snapshot;
    const magicResolvers = this.resolvers;

    if (magicResolvers && magicResolvers.length > 0) {
      return forkJoin(
        magicResolvers.map((resolver) =>
          getResolver(resolver, flattenRouteTree(targetSnapshot.root).at(-1), targetSnapshot, injector),
        ),
      ).pipe(
        switchMap((resolvers) => {
          return runInInjectionContext(injector, () => MagicResolverToObservable(resolvers.flat()));
        }),
        catchError((error) => {
          console.error('Error in MagicResolverDetect', error);
          return of(null);
        }),
      ) as Observable<T>;
    }

    return of(undefined as T);
  }

  renderComponent<T>(options: MagicResolverRender): Observable<ComponentRef<T>[]> {
    const componentRef = new Subject<ComponentRef<T>[]>();
    if (options.container) {
      const projectableNodes = options.projectableNodes;
      if (projectableNodes) {
        projectableNodes.forEach((nodes) => {
          nodes.forEach((node) => {
            (node as HTMLElement).remove();
          });
        });
      }
      options.container.clear();
      if (this.defaults?.length > 0) {
        this.resolveComponent(options.injector)
          .pipe(
            map(() =>
              this.defaults.map((defaultComponent) =>
                options.container.createComponent(defaultComponent, {
                  projectableNodes,
                }),
              ),
            ),
          )
          .subscribe((refs) => {
            componentRef.next(refs);
            componentRef.complete();
          });
      }
    }

    return componentRef;
  }
}
