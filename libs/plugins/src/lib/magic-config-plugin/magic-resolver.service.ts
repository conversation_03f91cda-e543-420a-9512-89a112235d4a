import { MagicResolver } from './types';

export class MagicResolverService {
  private static _resolvers: Map<string, MagicResolver[]> = new Map();

  static addResolver(componentSelector: string, resolvers: MagicResolver[]) {
    if (!resolvers || resolvers.length === 0) return;
    this._resolvers.set(componentSelector, resolvers);
  }

  static componentSelectors(): string[] {
    return Array.from(this._resolvers.keys());
  }

  static getResolvers(componentSelectors: string[]): MagicResolver[] {
    return componentSelectors.flatMap((selector) => this._resolvers.get(selector));
  }

  static resolverExists(): boolean {
    return this._resolvers.size > 0;
  }
}
