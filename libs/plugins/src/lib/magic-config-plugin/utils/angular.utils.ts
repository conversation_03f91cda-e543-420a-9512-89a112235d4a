/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injector, runInInjectionContext, ɵisInjectable, ɵisPromise } from '@angular/core';
import { ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { from, isObservable, of } from 'rxjs';

export function getRootComponentSelectors(component: any): string[] {
  const selectors = new Set<string>();

  selectors.add(getComponentSelector(component));

  component?.ɵcmp?.directiveDefs?.().forEach((element: any) => {
    if (element) {
      if (element.selectors) {
        selectors.add(getComponentSelector(element.type));
        getRootComponentSelectors(element.type).forEach((selector) => {
          selectors.add(selector);
        });
      } else {
        selectors.add(element);
      }
    }
  });

  return Array.from(selectors);
}

export function getClosestRouteInjector(snapshot: any) {
  if (!snapshot) return null;
  if (snapshot.routeConfig?._injector) {
    return snapshot.routeConfig._injector;
  }
  for (let s = snapshot.parent; s; s = s.parent) {
    const route = s.routeConfig;
    if (route?._loadedInjector) return route._loadedInjector;
    if (route?._injector) return route._injector;
  }
  return null;
}

function getTokenOrFunctionIdentity(tokenOrFunction: any, injector: Injector) {
  const NOT_FOUND = Symbol();
  const result = injector.get(tokenOrFunction, NOT_FOUND);
  if (result === NOT_FOUND) {
    if (typeof tokenOrFunction === 'function' && !ɵisInjectable(tokenOrFunction)) {
      return tokenOrFunction;
    } else {
      return injector.get(tokenOrFunction);
    }
  }
  return result;
}

function wrapIntoObservable(value: any) {
  if (isObservable(value)) {
    return value;
  }
  if (ɵisPromise(value)) {
    return from(Promise.resolve(value));
  }
  return of(value);
}

export function getComponentSelector(component: any) {
  try {
    return component.ɵcmp.selectors[0][0];
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (error) {
    return null;
  }
}

export function flattenRouteTree(route: ActivatedRouteSnapshot): ActivatedRouteSnapshot[] {
  const descendants = route.children.map((child) => flattenRouteTree(child)).flat();
  return [route, ...descendants];
}

export function getMatchingComponentSelectors(root: ActivatedRouteSnapshot, selector: string | string[]) {
  const components: any = flattenRouteTree(root)
    .map((route) => route.component)
    .filter(Boolean);
  const rootComponentSelectors = new Set(components.flatMap(getRootComponentSelectors).filter(Boolean));

  selector = Array.isArray(selector) ? selector : [selector];

  return selector.filter((item) => rootComponentSelectors.has(item));
}

export function getResolver(
  injectionToken: any,
  activatedRouteSnapshot: ActivatedRouteSnapshot,
  routerStateSnapshot: RouterStateSnapshot,
  injector: Injector,
) {
  const closestInjector = getClosestRouteInjector(activatedRouteSnapshot) ?? injector;
  const resolver = getTokenOrFunctionIdentity(injectionToken, closestInjector);
  const resolverValue = resolver.resolve
    ? resolver.resolve(activatedRouteSnapshot, routerStateSnapshot)
    : runInInjectionContext(closestInjector, () => resolver(activatedRouteSnapshot, routerStateSnapshot));
  return wrapIntoObservable(resolverValue);
}
