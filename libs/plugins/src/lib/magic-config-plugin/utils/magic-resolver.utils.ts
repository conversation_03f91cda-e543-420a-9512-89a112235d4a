import { map, switchMap } from 'rxjs/operators';
import { Observable, of } from 'rxjs';
import { Store } from '@ngxs/store';
import { inject } from '@angular/core';
import { MagicResolverModel } from '../types';

export const MagicResolverToObservable = (resolve: MagicResolverModel[]) => {
  if (!Array.isArray(resolve) || resolve.length === 0) {
    return of(null);
  }
  resolve = resolve.filter((r) => r?.action);

  const store = inject(Store);

  function runner(plannedActions: MagicResolverModel[], nextSteps: MagicResolverModel[]): Observable<null> {
    const plannedActionsFlatten = uniqueActions(plannedActions);

    const observable = store.dispatch(Object.values(plannedActionsFlatten));

    if (nextSteps.length > 0) {
      return observable.pipe(
        switchMap(() => {
          const [pl, next] = parse(nextSteps);
          return runner(pl, next);
        }),
      );
    }

    return observable.pipe(map((): null => null));
  }

  function uniqueActions(plannedActions: unknown[]) {
    const plannedActionsFlatten: Record<string, unknown> = {};
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    plannedActions.forEach((planned: any) => {
      if (!planned) {
        return;
      }
      const uniqueKey = planned.constructor?.type + JSON.stringify(Object.entries(planned));
      plannedActionsFlatten[uniqueKey] = planned;
    }, []);

    return plannedActionsFlatten;
  }

  function parse(requiredActions: MagicResolverModel[]) {
    let nextSteps: MagicResolverModel[] = [];
    const plannedActions = requiredActions.reduce((acc, flow) => {
      if (flow.next) {
        nextSteps = nextSteps.concat(flow.next);
      }
      if (typeof flow.selector == 'function') {
        if (!store.selectSnapshot(flow.selector)) {
          const newActions = flow.action();
          return acc.concat(Array.isArray(newActions) ? newActions : [newActions]);
        }
        return acc;
      }

      if (!flow.selector) {
        const newActions = flow.action();
        return acc.concat(Array.isArray(newActions) ? newActions : [newActions]);
      }
      return acc;
    }, []);

    return [plannedActions, nextSteps];
  }

  const plannedActions: MagicResolverModel[] = [];
  const nextSteps: MagicResolverModel[] = [];
  const [pl, next] = parse(resolve || []);

  plannedActions.push(...pl);
  nextSteps.push(...next);

  return runner(plannedActions, nextSteps);
};
