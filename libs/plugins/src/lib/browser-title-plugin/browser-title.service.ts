import { effect, inject, Injectable, signal, computed } from '@angular/core';
import { Title } from '@angular/platform-browser';

@Injectable({
  providedIn: 'root',
})
export class BrowserTitleService {
  private _title = inject(Title);

  readonly titleSuffix = 'Etiya Mobile';

  readonly title = signal<string>('');

  readonly fullTitle = computed(() => {
    const currentTitle = this.title();
    return currentTitle ? `${currentTitle} | ${this.titleSuffix}` : this.titleSuffix;
  });

  constructor() {
    effect(() => {
      this._title.setTitle(this.fullTitle());
    });
  }

  updateTitle(newTitle: string): void {
    this.title.set(newTitle.trim());
  }
}
