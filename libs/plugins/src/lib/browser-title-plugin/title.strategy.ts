import { inject, Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, NavigationEnd, PRIMARY_OUTLET, Router, RouterStateSnapshot } from '@angular/router';
import { filter, map } from 'rxjs/operators';
import { BrowserTitleService } from './browser-title.service';
import { TranslateService } from '../translate-plugin';

@Injectable({
  providedIn: 'root',
})
export class TitleStrategy {
  private router = inject(Router);
  private translateService = inject(TranslateService);
  private browserTitleService = inject(BrowserTitleService);

  constructor() {
    this.listenRouterEventsForTitleChange();
  }

  private listenRouterEventsForTitleChange(): void {
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        map(() => this.router.routerState.snapshot),
      )
      .subscribe((snapshot: RouterStateSnapshot) => {
        this.updateTitle(snapshot);
      });

    this.listenLanguageChange();
  }

  private updateTitle(snapshot: RouterStateSnapshot): void {
    const title = this.buildTitle(snapshot);
    const translatedTitle = this.translateService.translate(title) ?? '';

    if (title == null) {
      return;
    }
    this.browserTitleService.updateTitle(translatedTitle);
  }

  private buildTitle(snapshot: RouterStateSnapshot): string {
    let pageTitle: string = undefined;
    let route: ActivatedRouteSnapshot = snapshot.root;
    while (route) {
      pageTitle = this.getResolvedTitleForRoute(route) ?? pageTitle;
      route = route.children.find((child) => child.outlet === PRIMARY_OUTLET);
    }
    return pageTitle;
  }

  private getResolvedTitleForRoute(snapshot: ActivatedRouteSnapshot) {
    return snapshot.data.title;
  }

  private listenLanguageChange(): void {
    this.translateService.langChanges$.subscribe(() => {
      this.updateTitle(this.router.routerState.snapshot);
    });
  }
}
