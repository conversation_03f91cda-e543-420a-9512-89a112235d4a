export const isPlainObject = function (value: object | null) {
  if (typeof value !== 'object' || value === null || Object.prototype.toString.call(value) !== '[object Object]') {
    return false;
  }

  let proto = Object.getPrototypeOf(value);

  if (proto === null) {
    return true;
  }

  while (Object.getPrototypeOf(proto) !== null) {
    proto = Object.getPrototypeOf(proto);
  }

  return Object.getPrototypeOf(value) === proto;
};
