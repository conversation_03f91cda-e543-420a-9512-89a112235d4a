import { inject, provideAppInitializer, Provider } from '@angular/core';
import { Store } from '@ngxs/store';
import { map, Observable } from 'rxjs';
import { Config, OverrideConfig } from '@libs/plugins';
import { Environment } from '@libs/types';

export function provideConfigState(): Provider {
  return [provideAppInitializer(() => appConfigStateInitializer())];
}

export function appConfigStateInitializer(): Observable<boolean> {
  const environment: Environment.Model = inject(Environment.ENVIRONMENT);
  const store: Store = inject(Store);

  const environmentOptions: Config.State = {
    ...environment,
  };

  return store.dispatch(new OverrideConfig(environmentOptions)).pipe(map(() => true));
}
