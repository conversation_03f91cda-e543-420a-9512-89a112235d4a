import { inject, Injector, isDevMode, provideAppInitializer, Provider } from '@angular/core';
import { provideTransloco, TranslocoService } from '@jsverse/transloco';
import { forkJoin, lastValueFrom, map } from 'rxjs';
import { KnownLocalStorageKeys, LocalStorageService } from '@libs/core';
import { injectDebugMode } from '../debug-plugin/inject-debug-mode.provider';
import { TranslocoHttpLoader } from './transloco-http-loader';
import { Environment } from '@libs/types';
import { TranslateService } from './translate.service';

export function provideTranslate(): Provider {
  return [
    provideTransloco({
      config: {
        reRenderOnLangChange: true,
        prodMode: !isDevMode(),
      },
      loader: TranslocoHttpLoader,
    }),
    provideAppInitializer(() => translateInitializer()),
    {
      provide: TranslocoService,
      useExisting: TranslateService,
    },
  ];
}

function translateInitializer(): Promise<boolean> {
  const translateService: TranslateService = inject(TranslateService);
  const localStorageService: LocalStorageService = inject(LocalStorageService);
  const isDebugMode = injectDebugMode(inject(Injector));
  const config = inject(Environment.ENVIRONMENT);
  const availableLangs = config.languages;
  const browserLang = navigator.language.substring(0, 2);

  const defaultLanguage =
    findLanguage(availableLangs, config.defaultLanguage) ||
    findLanguage(availableLangs, browserLang) ||
    availableLangs.find(Boolean) ||
    'en';

  translateService.setAvailableLangs(availableLangs);

  function getLanguage() {
    const storedLang = localStorageService.get<string>(KnownLocalStorageKeys.LANGUAGE);
    if (findLanguage(availableLangs, storedLang)) {
      return storedLang;
    }
    return defaultLanguage;
  }

  const language = getLanguage();
  translateService.setDefaultLang(language);
  translateService.setActiveLang(language);

  // Load all languages in dev mode
  // In production, we only load the active language
  const languageLoaders = (isDebugMode() ? (translateService.getAvailableLangs() as string[]) : [language]).map(
    (lang: string) => translateService.load(lang),
  );

  return lastValueFrom(forkJoin([...languageLoaders]).pipe(map(() => true)));
}

export function findLanguage(availableLanguages: string[], language: string): string {
  return availableLanguages.find((lang) => lang === language);
}
