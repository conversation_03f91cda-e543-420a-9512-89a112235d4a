import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Translation, TranslocoLoader } from '@jsverse/transloco';
import { Environment } from '@libs/types';

@Injectable({
  providedIn: 'root',
})
export class TranslocoHttpLoader implements TranslocoLoader {
  private http = inject(HttpClient);
  private config = inject(Environment.ENVIRONMENT);

  getTranslation(lang: string) {
    return this.http.get<Translation>(`${this.config.url.i18n}${lang}.json`);
  }
}
