import { Auth } from '@libs/types';

export class LoadKeycloakProfileAction {
  static readonly type = '[Auth] LoadKeycloakProfileAction';
}

export class StartLoginAction {
  static readonly type = '[Auth] StartLoginAction';

  constructor(public payload?: Auth.LoginOptions) {}
}

export class LogoutAction {
  static readonly type = '[Auth] LogoutAction';

  constructor(public redirectTo?: string) {}
}
