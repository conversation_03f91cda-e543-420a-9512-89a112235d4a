import { Action, Selector, State, StateContext, StateToken } from '@ngxs/store';
import { tap } from 'rxjs/operators';
import { inject, Injectable } from '@angular/core';
import { LoadKeycloakProfileAction, LogoutAction, StartLoginAction } from './auth.actions';
import { KeycloakService } from '@libs/plugins';
import { Auth } from '@libs/types';

export const AuthStateToken = new StateToken<Auth.State>('AuthState');

@Injectable()
@State<Auth.State>({
  name: AuthStateToken,
  defaults: {
    profile: null,
  },
})
export class AuthState {
  @Selector()
  static profile({ profile }: Auth.State) {
    return profile;
  }

  @Selector()
  static userName({ profile }: Auth.State) {
    return profile.username;
  }

  private authService = inject(KeycloakService);

  @Action(StartLoginAction)
  startLogin(ctx: StateContext<Auth.State>, { payload }: StartLoginAction) {
    return this.authService.login(payload);
  }

  @Action(LogoutAction)
  logoutAction(ctx: StateContext<Auth.State>, { redirectTo }: LogoutAction) {
    return this.authService.logout(redirectTo);
  }

  @Action(LoadKeycloakProfileAction)
  loadProfile({ patchState }: StateContext<Auth.State>) {
    return this.authService.loadKeycloakProfile$().pipe(
      tap((profile) => {
        patchState({
          profile: profile as Auth.Keycloak.Profile,
        });
      }),
    );
  }
}
