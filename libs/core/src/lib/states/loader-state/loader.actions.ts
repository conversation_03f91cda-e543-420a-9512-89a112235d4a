import { Loader, RestError } from '@libs/types';

export class LoaderAddAction {
  static readonly type = '[Loader] LoaderAddAction';

  constructor(public payload: string[] | string) {}
}

export class LoaderRemoveAction {
  static readonly type = '[Loader] LoaderRemoveAction';

  constructor(public payload: string[] | string) {}
}

export class LoaderProgressAction {
  static readonly type = '[Loader] LoaderProgressAction';

  constructor(public payload: Loader.Progress) {}
}

export class RestErrorAction {
  static readonly type = '[Rest] RestErrorAction';

  constructor(public payload: RestError) {}
}
