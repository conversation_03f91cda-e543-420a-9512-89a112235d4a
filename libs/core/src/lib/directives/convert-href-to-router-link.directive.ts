import { Directive, ElementRef, Renderer2, AfterViewInit, inject } from '@angular/core';
import { Router } from '@angular/router';

@Directive({
  selector: 'a[href]',
})
export class ConvertHrefToRouterLinkDirective implements AfterViewInit {
  private elementRef = inject(ElementRef);
  private renderer2 = inject(Renderer2);
  private router = inject(Router);

  ngAfterViewInit() {
    const element: HTMLAnchorElement = this.elementRef.nativeElement;
    const href = element.getAttribute('href');

    if (href && !href.startsWith('http') && !href.startsWith('#')) {
      this.renderer2.removeAttribute(element, 'href');

      element.style.cursor = 'pointer';
      this.renderer2.listen(element, 'click', () => {
        this.router.navigateByUrl(href);
      });
    }
  }
}
