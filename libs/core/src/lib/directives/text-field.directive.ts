import { Directive, forwardRef } from '@angular/core';
import { NG_VALUE_ACCESSOR } from '@angular/forms';
import { ControlValueDirective } from './control-value.directive';

@Directive({
  selector: 'eds-text-field',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => TextFieldAccessorDirective),
      multi: true,
    },
  ],
})
export class TextFieldAccessorDirective extends ControlValueDirective {}
