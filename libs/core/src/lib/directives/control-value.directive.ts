import { Directive, ElementRef, HostListener } from '@angular/core';
import { ControlValueAccessor } from '@angular/forms';

@Directive({
  selector: '[control-value]',
})
export class ControlValueDirective implements ControlValueAccessor {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  protected onChange = (value: string) => {};
  protected onTouched = () => {};

  constructor(private el: ElementRef) {}

  @HostListener('input', ['$event.target.value'])
  handleInput(value: string) {
    this.onChange(value);
  }

  writeValue(value: string): void {
    this.el.nativeElement.value = value;
  }

  registerOnChange(fn: (value: string) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }
}
