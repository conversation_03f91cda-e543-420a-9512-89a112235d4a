import { Pipe, PipeTransform } from '@angular/core';
import IMask from 'imask';

@Pipe({
  name: 'imask',
})
export class IMaskPipe implements PipeTransform {
  transform(value: string, args?: { mask: string }): string {
    if (!value || !args?.mask) {
      return value;
    }

    const masked = IMask.createMask({
      mask: args.mask,
      overwrite: true,
      lazy: false,
    });

    masked.resolve(value);

    return masked.value;
  }
}
