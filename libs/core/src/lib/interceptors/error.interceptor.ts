import { HttpErrorResponse, HttpInterceptorFn, HttpResponse, HttpStatusCode } from '@angular/common/http';
import { inject } from '@angular/core';
import { catchError } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { ToasterService, TranslateService } from '@libs/plugins';
import { AUTO_ERROR_HANDLING_HTTP_CONTEXT } from '../constants';

export const ErrorInterceptor: HttpInterceptorFn = (request, next) => {
  const toasterService = inject(ToasterService);
  const translateService = inject(TranslateService);

  return next(request).pipe(
    catchError((errorResponse: HttpErrorResponse | HttpResponse<unknown>) => {
      const autoErrorHandling = request.context.get(AUTO_ERROR_HANDLING_HTTP_CONTEXT);

      if (errorResponse instanceof HttpErrorResponse && autoErrorHandling) {
        handleHttpError(errorResponse, toasterService, translateService);
      }

      return throwError(() => errorResponse);
    }),
  );
};

function handleHttpError(
  error: HttpErrorResponse,
  toasterService: ToasterService,
  translateService: TranslateService,
): void {
  switch (error.status) {
    case HttpStatusCode.InternalServerError: {
      toasterService.error({
        title: 'Error',
        description: 'An error occurred while processing your transaction.',
      });
      break;
    }
    case HttpStatusCode.Unauthorized:
      toasterService.error({
        title: 'Unauthorized',
        description: 'You are not authorized to access this page.',
      });
      break;
    case HttpStatusCode.BadRequest: {
      toasterService.error({
        title: 'Error',
        description: translateService.translateWithDefault(
          'error.' + error.error?.message,
          error.error?.message || error.message,
        ),
      });
      break;
    }
    default:
      // Handle other status codes here if needed
      break;
  }
}
