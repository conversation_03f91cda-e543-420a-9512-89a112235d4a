import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivateFn, Router, RouterStateSnapshot } from '@angular/router';
import { of } from 'rxjs';

import { removeFragmentFromUrlTree } from '@libs/core';
import { KeycloakService } from '@libs/plugins';
import { UserGuard } from './user.guard';

export const ProtectedRouteGuard: CanActivateFn = (next: ActivatedRouteSnapshot, state: RouterStateSnapshot) => {
  const keycloakService = inject(KeycloakService);

  if (!keycloakService.authenticated) {
    const router = inject(Router);
    const { path } = removeFragmentFromUrlTree(router, next, state);

    keycloakService.login({
      redirectUri: path,
    });
    return of(false);
  }

  return UserGuard(next, state);
};
