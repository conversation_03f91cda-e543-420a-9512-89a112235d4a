export function getSearchParamsFromUrl(url: string) {
  const params = new URLSearchParams(new URL(url).search);
  const paramsObj: Record<string, unknown> = {};

  for (const [key, value] of params.entries()) {
    let convertedValue: string | boolean;

    if (value.toLowerCase() === 'true') {
      convertedValue = true;
    } else if (value.toLowerCase() === 'false') {
      convertedValue = false;
    } else {
      convertedValue = value;
    }

    if (paramsObj[key]) {
      // If key already exists, make it an array
      if (Array.isArray(paramsObj[key])) {
        paramsObj[key].push(convertedValue);
      } else {
        paramsObj[key] = [paramsObj[key], convertedValue];
      }
    } else {
      paramsObj[key] = convertedValue;
    }
  }

  return paramsObj;
}
