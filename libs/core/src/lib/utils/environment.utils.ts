import { Environment } from '@libs/types';

export const getExternalEnvironment = ({ environment }: Environment.Model) => {
  const CONF = {
    FILE: 'environment.json',
    get PATH() {
      if (environment === 'local' || environment === 'prod-local') {
        return `assets/config`;
      } else {
        let exact;
        const baseNode = document.querySelector('base');
        if (!baseNode?.getAttribute('href').startsWith('.')) {
          exact = baseNode.href;
        } else {
          const location = window.location;
          exact = [
            location.origin,
            ...location.pathname.split('/').filter(Boolean).slice(0, 2),
            '', // must end with /
          ].join('/');
        }

        return `${exact}assets/config`;
      }
    },
    get ENV() {
      return `${this.PATH}/${this.FILE}`;
    },
  };

  return CONF.ENV;
};
