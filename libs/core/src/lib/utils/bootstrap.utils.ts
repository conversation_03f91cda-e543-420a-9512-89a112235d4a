import { ApplicationConfig, enableProdMode, Type } from '@angular/core';
import { Environment } from '@libs/types';
import { bootstrapApplication } from '@angular/platform-browser';

export const bootstrap = <T>(
  externalEnvironment: string,
  internalEnvironment: Environment.Model,
  AppComponent: Type<T>,
  appConfig: ApplicationConfig,
) => {
  fetch(externalEnvironment)
    .then((response) => response.json())
    .then((config: Environment.Model) => {
      if (internalEnvironment.production) {
        enableProdMode();
      }

      const appConfigExtended: ApplicationConfig = {
        ...appConfig,
        providers: [
          ...appConfig.providers,
          {
            provide: Environment.ENVIRONMENT,
            useValue: config,
          },
        ],
      };

      bootstrapApplication(AppComponent, appConfigExtended).catch((err) => console.error(err));
    })
    .catch((reason) => {
      let errorContext = reason?.message || '';

      if (errorContext.includes('Unexpected token <')) {
        errorContext = `No environment or configuration file found at given address: ${externalEnvironment}`;
      } else if (errorContext.includes('Unexpected string')) {
        errorContext = `Syntax corruption detected in configuration file`;
      }

      const errorElement = window.document.createElement('div');
      const errorContent = document.createTextNode(errorContext);
      const errorMessage = document.createTextNode(`check logs for error message`);
      errorElement.style.display = 'flex';
      errorElement.style.height = '100vh';
      errorElement.style.justifyContent = 'center';
      errorElement.style.alignItems = 'center';
      errorElement.style.fontSize = 'large';
      errorElement.appendChild(errorContent);
      errorElement.appendChild(document.createElement('br'));
      errorElement.appendChild(errorMessage);
      console.error(`error message: ${errorContext}`);

      window.document.body.appendChild(errorElement);
    });
};
