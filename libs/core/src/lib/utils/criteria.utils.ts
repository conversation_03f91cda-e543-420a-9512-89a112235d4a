import { Lov } from '@libs/types';

export function buildCriteriaRequest(extra: Lov.PredicatesBuilder = {}): Lov.CriteriaRequest {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const overrides = Object.entries(extra).reduce((acc: { [key: string]: any }, [key, value]) => {
    acc[key] = { value };
    return acc;
  }, {});

  return {
    predicates: {
      isActive: {
        value: 1,
      },
      ...overrides,
    },
  };
}
