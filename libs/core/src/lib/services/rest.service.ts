import { HttpClient, HttpContext, HttpErrorResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Environment, ResponseTypeEnum, RestConfig, RestRequest } from '@libs/types';
import { Store } from '@ngxs/store';
import { Observable, of, throwError } from 'rxjs';
import { catchError, finalize, map, switchMap, take, tap } from 'rxjs/operators';
import { LoaderAddAction, LoaderRemoveAction, RestErrorAction } from '../states';
import { AUTO_ERROR_HANDLING_HTTP_CONTEXT, BRAND_CODE_HTTP_CONTEXT } from '../constants';
import { takeUntilResponse } from '../utils';

interface RestServiceHttpContextOptions {
  withBrandCode: boolean;
  autoErrorHandling: boolean;
}

@Injectable({
  providedIn: 'root',
})
export class RestService {
  private store = inject(Store);
  private http = inject(HttpClient);
  private url$ = of(inject(Environment.ENVIRONMENT).url);

  get<T>(url: string, config?: RestConfig): Observable<T> {
    return this.request<unknown, T>(
      {
        method: 'GET',
        url,
      },
      config,
    );
  }

  post<T, R>(url: string, body: T, config?: RestConfig): Observable<R> {
    return this.request<T, R>(
      {
        method: 'POST',
        url,
        body,
      },
      config,
    );
  }

  put<T, R>(url: string, body: T, config?: RestConfig): Observable<R> {
    return this.request<T, R>(
      {
        method: 'PUT',
        url,
        body,
      },
      config,
    );
  }

  delete<T>(url: string, body: T, config?: RestConfig): Observable<T> {
    return this.request<unknown, T>(
      {
        method: 'DELETE',
        url,
        body,
      },
      config,
    );
  }

  private request<T, R>(request: RestRequest<T>, config: RestConfig): Observable<R> {
    const { baseUrl, observe = 'body', autoErrorHandling = true, service } = config;
    return this.url$.pipe(
      map((url) => {
        const normalizedUrl = baseUrl ? baseUrl : service ? url[service] : url.api;
        return {
          ...request,
          url: normalizedUrl + request.url,
        } as RestRequest<T>;
      }),
      tap(() => {
        this.store.dispatch(new LoaderAddAction(config.desc));
      }),
      switchMap(({ method, url, ...options }) => {
        return this.http.request<T>(method, url, {
          ...options,
          context: this.getHttpContext(request, config),
          headers: config.headers,
          params: config.queryParams,
          responseType: config.responseType ?? ResponseTypeEnum.JSON,
          observe,
        } as unknown) as unknown as Observable<R>;
      }),
      observe === 'body' ? take(1) : takeUntilResponse(),
      catchError((response: HttpErrorResponse) => {
        this.store.dispatch([
          new LoaderRemoveAction(config.desc),
          new RestErrorAction({
            autoErrorHandling,
            response: response.error,
            type: config.desc,
            status: response.status,
          }),
        ]);
        return throwError(() => response);
      }),
      finalize(() => this.store.dispatch(new LoaderRemoveAction(config.desc))),
    );
  }

  private getHttpContext<T>(request: RestRequest<T>, config: RestConfig): HttpContext {
    // Default withBrandCode value is true, so we do send Brand-Code header by default
    const { withBrandCode = true, autoErrorHandling = true } = config;

    return this.setHttpContext({
      autoErrorHandling,
      withBrandCode,
    });
  }

  private setHttpContext({ autoErrorHandling, withBrandCode }: RestServiceHttpContextOptions): HttpContext {
    return new HttpContext()
      .set(BRAND_CODE_HTTP_CONTEXT, withBrandCode)
      .set(AUTO_ERROR_HANDLING_HTTP_CONTEXT, autoErrorHandling);
  }
}
