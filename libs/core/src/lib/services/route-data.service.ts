import { inject, Injectable, signal } from '@angular/core';
import { ActivatedRoute, Data, NavigationEnd, Router } from '@angular/router';
import { filter, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class RouteDataService {
  private router = inject(Router);
  private activatedRoute = inject(ActivatedRoute);

  data = signal<Data>({});

  constructor() {
    this.listenActiveRouteData();
  }

  private listenActiveRouteData() {
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        map(() => this.activatedRoute.snapshot),
        map((route) => {
          while (route.firstChild) {
            route = route.firstChild;
          }

          return route.data;
        }),
      )
      .subscribe((routeData) => this.data.set(routeData));
  }
}
