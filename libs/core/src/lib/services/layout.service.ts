import { inject, Injectable, signal } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { filter, map } from 'rxjs/operators';
import { LayoutHandlerEnum, LayoutTypeEnum } from '@libs/types';

@Injectable({
  providedIn: 'root',
})
export class LayoutService {
  private router = inject(Router);
  private activatedRoute = inject(ActivatedRoute);

  layout = signal<LayoutTypeEnum>(LayoutTypeEnum.DEFAULT);

  /*  stepShortCode$: Observable<WorkflowStateType> = this.router.events.pipe(
    this.getActiveRouteData$(),
    map((data) => data.stepShortCode),
  );*/

  constructor() {
    this.listenActiveRouteData();
  }

  /*  private getActiveRouteData$<T>(): UnaryFunction<Observable<T>, Observable<Data>> {
    return pipe(
      filter((event) => event instanceof NavigationEnd),
      startWith(this.activatedRoute.snapshot),
      map(() => this.activatedRoute.snapshot),
      map((route) => {
        while (route.firstChild) {
          route = route.firstChild;
        }
        return route.data;
      }),
    );
  }*/

  setLayout(layout: LayoutTypeEnum) {
    this.layout.set(layout);
  }

  private listenActiveRouteData() {
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        map(() => this.activatedRoute.snapshot),
        map((route) => {
          while (route.firstChild) {
            route = route.firstChild;
          }

          return route.data;
        }),
        filter((data) => data?.layoutHandler !== LayoutHandlerEnum.SERVICE),
        map((data) => data.layout ?? LayoutTypeEnum.DEFAULT),
      )
      .subscribe((layout) => this.layout.set(layout));
  }
}
