import {
  DeliverInstallationRetrieveConfigResponse,
  DeliveryMethod,
  RadioDeliveryOption,
  ShipmentOffer,
} from '@libs/types';
import { Delivery } from '@libs/widgets';

export class DeliverInstallationRetrieveConfigData {
  private defaults: DeliverInstallationRetrieveConfigResponse[] = [];

  constructor(defaults?: DeliverInstallationRetrieveConfigResponse[]) {
    this.defaults = defaults || [];
  }

  get items(): DeliverInstallationRetrieveConfigResponse[] {
    return this?.defaults;
  }

  getDeliveryOptionsByMethodId(deliveryMethTpId: number): ShipmentOffer[] {
    return (
      this.defaults
        .flatMap((deliverInstallationRetrieve) => deliverInstallationRetrieve.deliverMethods)
        .filter((item) => item.deliveryMethTpId === deliveryMethTpId)
        .find(Boolean)?.deliveryOptions ?? []
    );
  }

  getDeliveryOptionByMethodId(deliveryMethTpId: number, deliveryOfferId: number): ShipmentOffer {
    return this.getDeliveryOptionsByMethodId(deliveryMethTpId).find((item) => item.offerId === deliveryOfferId);
  }

  getDeliveryOptionsByOfferId(): ShipmentOffer[] {
    return (
      this.defaults
        .filter((deliverInstallationRetrieve) => deliverInstallationRetrieve.deliverMethods?.length)
        .flatMap((deliverInstallationRetrieve) => deliverInstallationRetrieve.deliverMethods)
        .filter((item) => item.deliveryOfrId)
        .find(Boolean)?.deliveryOptions ?? []
    );
  }

  getDeliveryOptionByOfferId(deliveryOfferId: number): ShipmentOffer {
    return this.getDeliveryOptionsByOfferId().find((item) => item.offerId === deliveryOfferId);
  }

  estimatedDeliveryInfo(deliveryMethod: DeliveryMethod): Delivery.DeliverOptionSummaryCard {
    const deliveryOption = deliveryMethod?.deliveryMethodType?.id
      ? this.getDeliveryOptionByMethodId(deliveryMethod?.deliveryMethodType?.id, deliveryMethod?.deliveryOfferId)
      : this.getDeliveryOptionByOfferId(deliveryMethod?.deliveryOfferId);

    if (!deliveryOption) {
      return null;
    }
    return this.getDeliveryOptionSummary(deliveryOption);
  }

  deliveryOptions(methodId: number, optionId?: number): RadioDeliveryOption[] {
    const deliveryOptionsByMethod = this.getDeliveryOptionsByMethodId(methodId);
    const deliveryOptionsByDeliveryOffer = this.getDeliveryOptionsByOfferId();

    const deliveryOptions = deliveryOptionsByMethod.length ? deliveryOptionsByMethod : deliveryOptionsByDeliveryOffer;

    return deliveryOptions.map((item) => {
      const { offerName, price, deliveryDays, deliveryTime } = this.getDeliveryOptionSummary(item);

      return {
        ...item,
        id: String(item.offerId),
        name: offerName,
        value: String(item.offerId),
        label: offerName,
        isChecked: optionId ? item.offerId === optionId : item.selected,
        isDisabled: false,
        isInvalid: false,
        isRequired: false,
        price,
        deliveryDays,
        deliveryTime,
      };
    });
  }

  private getDeliveryOptionSummary(deliveryOption: ShipmentOffer): Delivery.DeliverOptionSummaryCard {
    // TODO: when price, deliveryDays and deliveryTime are sent as separate fields in the api, the mapping here will be changed.
    const match = deliveryOption.offerName.match(/(.*?)\s*\(\$(\d+\.?\d*)\)$/);
    const [name, price] = match ? [match[1], match[2]] : [deliveryOption.offerName, '0'];
    const deliveryDate = new Date(deliveryOption.expectedDeliveryDate);
    const deliveryDays = deliveryDate.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
    const deliveryTime = deliveryDate.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });

    return {
      offerName: name,
      deliveryDays,
      deliveryTime,
      price,
    };
  }
}
