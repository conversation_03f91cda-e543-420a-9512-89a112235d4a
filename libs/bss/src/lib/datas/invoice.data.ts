import { eInvoice, Invoice } from '@libs/types';

export class InvoiceData {
  private defaults: Invoice.CustomerInvoiceDetail[] = [];

  constructor(defaults?: Invoice.CustomerInvoiceDetail[]) {
    this.defaults = defaults;
  }

  get invoices(): Invoice.CustomerInvoiceDetail[] {
    return this.defaults;
  }

  get pendingInvoices(): Invoice.CustomerInvoiceDetail[] {
    const pendingStatuses = [eInvoice.PaymentStatus.UNPAID, eInvoice.PaymentStatus.OVERDUE];
    return this.defaults?.filter((invoice) =>
      pendingStatuses.includes(invoice.paymentStatus as eInvoice.PaymentStatus),
    );
  }
}
