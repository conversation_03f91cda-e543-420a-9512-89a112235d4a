import { formatAddress } from '@libs/core';
import { Address, BillingAccount } from '@libs/types';
import { SelectOption } from '@libs/widgets';

export class AccountIntegrationBillingAccountData {
  private defaults: BillingAccount.IntegrationBillingAccountResponse = {};

  constructor(billingAccounts?: BillingAccount.IntegrationBillingAccountResponse) {
    this.defaults = billingAccounts;
  }

  get integrationBillingAccountsContent(): BillingAccount.BillingAccount[] {
    return this?.defaults?.content ?? [];
  }

  get integrationBillingAccounts(): BillingAccount.IntegrationBillingAccountResponse {
    return this?.defaults ?? {};
  }

  getBillingAccountAddressLovOptions(selectedAddress?: Address): SelectOption[] {
    return this.integrationBillingAccountsContent.map((item: BillingAccount.BillingAccount, index: number) => {
      const address = item.billingAddress;
      return {
        name: item.billingAddress?.addressLabel,
        label: formatAddress(address),
        value: item.billingAddress?.id,
        isSelected: selectedAddress?.id ? item.billingAddress?.id === selectedAddress?.id : index === 0,
        isDisabled: false,
      };
    });
  }

  findBillingAccountAddressByAddressId(addressId: number): Address {
    return this.integrationBillingAccountsContent.find((item) => item.billingAddress?.id === addressId)?.billingAddress;
  }
}
