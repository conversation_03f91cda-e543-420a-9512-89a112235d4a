import { PrivacySpecification } from '@libs/types';
import { Checkbox } from '@libs/widgets';

export class PartyPrivacySpecificationData {
  private defaults: PrivacySpecification.PartyPrivacySpecification[] = [];

  constructor(defaults?: PrivacySpecification.PartyPrivacySpecification[]) {
    this.defaults = defaults?.sort((a, b) => a.sortId - b.sortId);
  }

  get partyPrivacySpecification(): PrivacySpecification.PartyPrivacySpecification[] {
    return this.defaults;
  }

  mappedPartyPrivacySpecification(): Checkbox[] {
    return this.defaults?.map((item) => ({
      id: String(item.id),
      name: item.name,
      label: item?.description,
      value: item.shortCode,
      isChecked: false,
      isDisabled: false,
      isInvalid: false,
      isRequired: item.authorizedRequiredToPerformInteraction,
      isClickable: item.hasAdditionalDocument,
    }));
  }

  partyPrivacySpecificationByShortCode(shortCode: string): PrivacySpecification.PartyPrivacySpecification {
    return this.defaults?.find((item) => item.shortCode === shortCode);
  }
}
