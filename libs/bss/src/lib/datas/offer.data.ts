import { deepArrayFilter } from '@libs/core';
import {
  AlternateProductOfferingProposal,
  DeepPartial,
  DEFAULT_CURRENCY_CODE,
  ProdOfferCharTypes,
  ProdSpecCharValueUse,
  ProductOfferingPrice,
} from '@libs/types';

export class OfferData {
  private defaults: AlternateProductOfferingProposal;

  constructor(defaults?: AlternateProductOfferingProposal) {
    this.defaults = defaults;
  }

  get name(): string {
    return this.defaults.alternateProductOffering.name;
  }

  get productOfferingPrice(): ProductOfferingPrice {
    return this.defaults.alternateProductOffering?.productOfferingPrice?.find(Boolean);
  }

  get discountPrice(): number {
    return this.productOfferingPrice?.priceItem.find((item) => item.priceItemType === 'discountAppliedPrice')?.price
      .value;
  }

  get price(): number {
    return this.productOfferingPrice?.price?.value ?? 0;
  }

  get currency(): string {
    return this.productOfferingPrice?.price?.unit ?? DEFAULT_CURRENCY_CODE;
  }

  get expoGroupIds() {
    return this.defaults.alternateProductOffering.expoGroupRef.map((x) => x.id);
  }

  get commitmentDescription() {
    return this.searchProdOfferCharGroup({
      name: ProdOfferCharTypes.COMMITMENT_MARKETING_DESCRIPTION,
    })
      .find(Boolean)
      ?.productSpecCharacteristicValue?.find(Boolean)?.value;
  }

  get smsAmount() {
    return this.searchProdOfferCharGroup({
      name: ProdOfferCharTypes.SMS_AMOUNT,
    })
      .find(Boolean)
      ?.productSpecCharacteristicValue?.find(Boolean)?.value;
  }

  get dataAmount() {
    return this.searchProdOfferCharGroup({
      name: ProdOfferCharTypes.DATA_AMOUNT,
    })
      .find(Boolean)
      ?.productSpecCharacteristicValue?.find(Boolean)?.value;
  }

  get voiceAmount() {
    return this.searchProdOfferCharGroup({
      name: ProdOfferCharTypes.VOICE_AMOUNT,
    })
      .find(Boolean)
      ?.productSpecCharacteristicValue?.find(Boolean)?.value;
  }

  get offerId() {
    return this.defaults.alternateProductOffering.id;
  }

  searchProdOfferCharGroup(search: DeepPartial<ProdSpecCharValueUse>) {
    return deepArrayFilter(this.defaults.alternateProductOffering.prodSpecCharValueUse, search);
  }
}
