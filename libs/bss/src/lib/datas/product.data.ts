import { eCommon, eProduct, OfferInstanceProductTypeEnum, Product } from '@libs/types';
import { ProductDetailData } from '@libs/bss';

export class ProductData {
  private defaults: Product.Product;

  constructor(defaults?: Product.Product) {
    this.defaults = defaults;
  }

  get product() {
    return this.defaults;
  }

  get plan() {
    return this.defaults?.productDetailList.find((item) => item.productType === eCommon.ProductConsts.PLAN_BUNDLE);
  }

  get planBundleSummary() {
    return this.defaults?.planBundleSummary;
  }

  get billingAccountId() {
    return this.defaults?.billingAccountId;
  }

  get endUserName() {
    if (!this.defaults?.endUserInformation) {
      return null;
    }

    return (
      this.defaults.endUserInformation?.fullName ||
      this.defaults.endUserInformation?.firstName + ' ' + this.defaults.endUserInformation?.lastName
    );
  }

  activeProductDetailList(): ProductDetailData {
    return this.productDetailList(eProduct.ProductStatusShortCodes.ACTV);
  }

  productDetailList(status: eProduct.ProductStatusShortCodes = null): ProductDetailData {
    const data = this.defaults?.productDetailList || [];

    if (status) {
      return new ProductDetailData(data.filter((item) => item.status.shortCode === status));
    }
    return new ProductDetailData(data);
  }

  getProductDetailByProductType(productType: OfferInstanceProductTypeEnum) {
    return this.defaults.productDetailList.filter((productDetail) => productDetail.productType === productType);
  }
}
