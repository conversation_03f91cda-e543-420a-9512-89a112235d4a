import { DEFAULT_CURRENCY_CODE, eOffer, Offer, QuotePrice } from '@libs/types';

export class QuotePriceData {
  private defaults: QuotePrice.QuotePriceDetail;
  private defaultPlanCustomerOrderItemId: number;
  private defaultDeviceCustomerOrderItemId: number;

  constructor(
    defaults?: QuotePrice.QuotePriceDetail,
    planCustomerOrderItemId?: number,
    deviceCustomerOrderItemId?: number,
  ) {
    this.defaults = defaults;
    this.defaultPlanCustomerOrderItemId = planCustomerOrderItemId;
    this.defaultDeviceCustomerOrderItemId = deviceCustomerOrderItemId;
  }

  get price(): QuotePrice.QuotePriceDetail {
    return this.defaults;
  }

  get planCustomerOrderItemId(): number {
    return this.defaultPlanCustomerOrderItemId;
  }

  get deviceCustomerOrderItemId(): number {
    return this.defaultDeviceCustomerOrderItemId;
  }

  get planOfferPrice(): QuotePrice.PriceDetail {
    return (
      this.price?.subscriptionDetailList
        ?.filter((item) => item.planPriceDetail.customerOrderItemId === this.planCustomerOrderItemId)
        ?.find(Boolean)?.planPriceDetail || ({} as QuotePrice.PriceDetail)
    );
  }

  get planPrice(): Offer.Price {
    return this.getPrice(this.planOfferPrice?.calculatedOfferPrice, eOffer.OfferPeriod.MONTH);
  }

  get planDiscountPrice(): Offer.Price {
    return this.getDiscountPrice(this.planOfferPrice?.calculatedOfferPrice, this.planPrice?.value);
  }

  get deviceSubscriptionDetail(): QuotePrice.SubscriptionDetail {
    return this.price?.subscriptionDetailList
      ?.filter((item) => item.primaryDevicePriceDetail.customerOrderItemId === this.deviceCustomerOrderItemId)
      ?.find(Boolean);
  }

  get deviceOfferPrice(): QuotePrice.PriceDetail {
    return this.deviceSubscriptionDetail?.primaryDevicePriceDetail || ({} as QuotePrice.PriceDetail);
  }

  get devicePrice(): Offer.Price {
    return this.getPrice(this.deviceOfferPrice?.calculatedOfferPrice);
  }

  get deviceDiscountPrice(): Offer.Price {
    return this.getDiscountPrice(this.deviceOfferPrice?.calculatedOfferPrice, this.devicePrice?.value);
  }

  private getPrice(pricingData: QuotePrice.CalculatedPrices, period?: eOffer.OfferPeriod): Offer.Price {
    const { eipRecurring, recurring, taxOneTime, total, currency } = pricingData || {};
    return {
      value: eipRecurring > 0 ? eipRecurring || recurring : taxOneTime || total,
      code: currency?.currencyCode || DEFAULT_CURRENCY_CODE,
      period: period ?? '',
    };
  }

  private getDiscountPrice(pricingData: QuotePrice.CalculatedPrices, regularPrice: number): Offer.Price {
    const {
      currency,
      discountAppliedTaxOneTime,
      discountAppliedOneTime,
      eipRecurring,
      discountAppliedEipTaxRecurring,
      discountAppliedEipRecurring,
    } = pricingData || {};

    const discount =
      eipRecurring > 0
        ? discountAppliedEipTaxRecurring || discountAppliedEipRecurring
        : discountAppliedTaxOneTime || discountAppliedOneTime;

    return (
      discount > 0 &&
      discount !== regularPrice && {
        value: discount,
        code: currency?.currencyCode || DEFAULT_CURRENCY_CODE,
        period: '',
      }
    );
  }
}
