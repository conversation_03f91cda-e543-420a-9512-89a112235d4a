import { State } from '@libs/types';
import { SelectOption } from '@libs/widgets';

export class LovStateCriteriaData {
  private defaults: State.State[] = [];

  constructor(defaults?: State.State[]) {
    this.defaults = defaults;
  }

  get stateCriteria(): State.State[] {
    return this.defaults;
  }

  stateOptions(): SelectOption[] {
    return this.defaults?.map((item) => {
      return {
        name: item.name,
        label: item.name,
        value: item,
        isSelected: false,
        isDisabled: false,
      };
    });
  }
}
