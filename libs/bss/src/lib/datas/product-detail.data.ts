import { eCommon, eProduct, Product } from '@libs/types';
import { ProductCharListData } from './product-char-list.data';

export class ProductDetailData {
  defaults: Product.ProductDetail[];

  constructor(defaults?: Product.ProductDetail[]) {
    this.defaults = defaults;
  }

  get secondaryProducts() {
    return this.defaults?.filter((item) => item.secondary);
  }

  get secondaryProductChars() {
    return this.defaults?.filter((item) => item.secondary)?.flatMap((product) => product.productCharList);
  }

  get plan() {
    return this.defaults.find((item) => item.productType === eCommon.ProductConsts.PLAN_BUNDLE);
  }

  get device() {
    return this.defaults.find(
      (item) =>
        item.productType === eCommon.ProductConsts.DEVICE &&
        item.familyInfo.familyCategory !== eCommon.CatalogShortCode.SIM_CARD,
    );
  }

  get byodDevice() {
    return this.defaults.find((item) => item.productType === eCommon.ProductConsts.MOBILE_BYOD);
  }

  get identity() {
    return this.defaults.find((item) => item.productType === eCommon.ProductConsts.SUBSCRIPTON_IDENTITY);
  }

  get identityData() {
    return new ProductCharListData(this.identity?.productCharList);
  }

  get msisdnNumber() {
    return this.identityData.find(eProduct.ProductCharTypes.MSISDN)?.productCharValue;
  }

  get deviceName() {
    if (this.device) {
      return this.device.name;
    }

    return this.byodDevice ? 'BYOD' : '';
  }

  get nonPrimarySecondaryProductDetails() {
    return this.defaults.filter((product) => product.primary === false || product.secondary === false);
  }

  get nonDeviceAndSecondaryProductDetails() {
    return this.defaults.filter(
      (product) => product.productType !== eProduct.ProductConsts.DEVICE && product.secondary === false,
    );
  }

  get nonPrimarySecondaryProductChars() {
    return this.nonPrimarySecondaryProductDetails.flatMap((product) => product.productCharList);
  }

  get nonDeviceAndSecondaryProductChars() {
    return this.nonDeviceAndSecondaryProductDetails.flatMap((product) => product.productCharList);
  }

  find(productId: number) {
    return this.defaults.find((value) => value.productId === productId);
  }
}
