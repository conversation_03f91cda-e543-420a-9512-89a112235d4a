import { deepArrayFilter } from '@libs/core';
import { DeepPartial, eProduct, ProductChar } from '@libs/types';

export class ProductCharListData {
  defaults: ProductChar[];

  constructor(defaults: ProductChar[]) {
    this.defaults = defaults;
  }

  get primaryDisplayChars() {
    return this.getByViewTypeShortCode(eProduct.SimpleProductCharShortCode.PRIMARY_DISPLAY_CHARS);
  }

  get secondaryDisplayChars() {
    return this.getByViewTypeShortCode(eProduct.SimpleProductCharShortCode.SECONDARY_DISPLAY_CHARS);
  }
  get subscriptionIdentityChars() {
    return this.getByViewTypeShortCode(eProduct.ProductConsts.SUBSCRIPTION_IDENTITY);
  }

  search(search: DeepPartial<ProductChar>) {
    return deepArrayFilter(this.defaults, search);
  }

  getByViewTypeShortCode(shortCode: eProduct.SimpleProductCharShortCode | eProduct.ProductConsts) {
    return this.search({ viewType: { shortCode } });
  }

  find(shortCode: eProduct.SimpleProductCharShortCode | eProduct.ProductCharTypes | eProduct.ProductConsts) {
    return this.defaults?.find((item) => item.productCharShortCode === shortCode);
  }
}
