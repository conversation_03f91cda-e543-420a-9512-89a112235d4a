import { Country, SelectOption } from '@libs/types';

export class LovCountryTypeData {
  private defaults: Country.Country[] = [];

  constructor(defaults?: Country.Country[]) {
    this.defaults = defaults;
  }

  get countryTypes(): Country.Country[] {
    return this.defaults;
  }

  findIndex(id: number): number {
    return this.defaults?.findIndex((country) => country.id === id);
  }

  countryOptions(type: 'select' | 'phoneNumber' = 'select', defaultSelectedIndex?: number): SelectOption[] {
    return (
      this.defaults?.map((item, index) => {
        const isPhone = type === 'phoneNumber';

        return {
          name: item.name,
          label: isPhone ? `+${item.callCode}` : item.name,
          value: isPhone ? item.countryCode : item,
          isSelected: index === defaultSelectedIndex,
          isDisabled: false,
        };
      }) ?? []
    );
  }
}
