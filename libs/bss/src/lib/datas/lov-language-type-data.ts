import { Language } from '@libs/types';
import { SelectOption } from '@libs/widgets';

export class LovLanguageTypeData {
  private defaults: Language.Language[] = [];

  constructor(defaults?: Language.Language[]) {
    this.defaults = defaults;
  }

  get languageTypes(): Language.Language[] {
    return this.defaults;
  }

  mappedLanguageTypes(): SelectOption[] {
    return this.defaults?.map((item) => ({
      value: String(item.shortCode),
      label: item.name,
      name: item.name,
      isSelected: item.isDefault === 1,
      isDisabled: false,
    }));
  }
}
