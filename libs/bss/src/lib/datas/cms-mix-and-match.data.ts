import { CMS } from '@libs/types';

export class MixAndMatchData {
  private defaults: CMS.MixAndMatchOffers;

  constructor(mixAndMatchOffers?: CMS.MixAndMatchOffers) {
    this.defaults = mixAndMatchOffers;
  }

  get results(): CMS.MixAndMatchOffer[] {
    return this.defaults?.viewMixAndMatchPlans.results;
  }

  get filters(): CMS.MixAndMatchFilter[] {
    return this.defaults?.viewMixAndMatchPlans.filters;
  }

  get price(): number {
    return this.defaults?.viewMixAndMatchPlans.results.reduce((acc, item) => acc + item.prices[0].price.value, 0);
  }
}
