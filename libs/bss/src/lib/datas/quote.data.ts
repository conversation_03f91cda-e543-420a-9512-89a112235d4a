import {
  AbstractOfferInstance,
  AbstractQuote,
  CatalogGroupContractType,
  CustomerOrderItemActionTypeEnum,
  eBusinessFlow,
  eProduct,
  OfferInstanceKeyEnum,
  OfferInstanceProductTypeEnum,
  PaymentInfo,
  QuotePrice,
  ServiceSpecKeyEnum,
} from '@libs/types';
import { PlanData } from './plan.data';

export class QuoteData {
  private defaults: AbstractQuote;
  private priceDetail: QuotePrice.QuotePriceDetail;

  constructor(defaults?: AbstractQuote, priceDetail?: QuotePrice.QuotePriceDetail) {
    this.defaults = defaults;
    this.priceDetail = priceDetail;
  }

  get quote(): AbstractQuote {
    return this.defaults;
  }

  get price(): QuotePrice.QuotePriceDetail {
    return this.priceDetail;
  }

  get bundleOffers(): AbstractOfferInstance[] {
    return this.getOfferInstancesByKey(OfferInstanceKeyEnum.MOBILE_PLAN_BUNDLE) ?? [];
  }

  get bundleOfferIds(): string[] {
    return this.bundleOffers.map((item) => item.offerId?.toString());
  }

  get deactivationBundleOffer(): AbstractOfferInstance {
    return this.bundleOffers.find((item) => item.actionCode === CustomerOrderItemActionTypeEnum.DEACTIVATION);
  }

  get activationBundleOffers(): AbstractOfferInstance[] {
    return this.bundleOffers.filter((item) => item.actionCode === CustomerOrderItemActionTypeEnum.ACTIVATION);
  }

  get prepaidBundleOffers(): AbstractOfferInstance[] {
    return this.bundleOffers.filter((item) => item.saleType === CatalogGroupContractType.PREPAID);
  }

  get postpaidBundleOffers(): AbstractOfferInstance[] {
    return this.bundleOffers.filter((item) => item.saleType === CatalogGroupContractType.POSTPAID);
  }

  get bundleShipmentOfferIds(): number[] {
    return this.bundleOffers.map((item) => item?.specItemIds?.[ServiceSpecKeyEnum.SHIPMENT_CFS]).filter(Boolean);
  }

  get paymentInformation(): PaymentInfo {
    return this.defaults?.paymentInfo;
  }

  get nonBYODBundleOffers(): AbstractOfferInstance[] {
    return this.bundleOffers.filter((item) => !item?.offerInstances?.[eProduct.ProductConsts.MOBILE_BYOD]);
  }

  get paymentInfoBillingAddress() {
    return this.defaults?.paymentInfo?.billingAddress || null;
  }

  get paymentReference() {
    return this.defaults?.paymentInfo?.paymentReference;
  }

  get paymentMethod() {
    return this.defaults?.paymentInfo?.type;
  }

  get lastCurrentOrderItemId() {
    return this.quote.offerInstances.mobilePlanBundle[0].customerOrderItemId; // TODO
  }

  get familyCategoryName(): string {
    return this.bundleOffers.find(Boolean)?.familyCategoryName;
  }

  get plans(): AbstractOfferInstance[] {
    const offerInstances = this.getOfferInstancesByKey(OfferInstanceKeyEnum.MOBILE_PLAN_BUNDLE);

    return offerInstances.map((offer) => {
      const device = this.deviceOffer(offer.customerOrderItemId);
      return new PlanData(offer, device, this.priceDetail).plan;
    });
  }

  get rawPlans(): PlanData[] {
    const offerInstances = this.getOfferInstancesByKey(OfferInstanceKeyEnum.MOBILE_PLAN_BUNDLE);

    return offerInstances.map((offer) => {
      const device = this.deviceOffer(offer.customerOrderItemId);
      return new PlanData(offer, device, this.priceDetail);
    });
  }

  get planSaleType(): CatalogGroupContractType {
    return this.bundleOffers[0]?.saleType;
  }

  get currentBusinessFlowSpecShortCode(): eBusinessFlow.Specification {
    return this.defaults?.flowSpecCode;
  }

  get customerOrderId(): number {
    return this.defaults?.customerOrderId;
  }

  getOfferInstancesByKey(key: OfferInstanceKeyEnum): AbstractOfferInstance[] {
    return this.defaults?.offerInstances[key] ?? [];
  }

  findOffer(customerOrderItemId: number, offers: AbstractOfferInstance[]): AbstractOfferInstance {
    return offers.filter((offer) => offer.customerOrderItemId === customerOrderItemId)?.find(Boolean);
  }

  deviceOffer(planCustomerOrderItemId: number): AbstractOfferInstance {
    const offerInstances = this.findOffer(planCustomerOrderItemId, this.bundleOffers);
    return offerInstances?.offerInstances[OfferInstanceKeyEnum.MOBILE_DEVICE]
      ?.filter((device) => device.productType === OfferInstanceProductTypeEnum.DEVICE)
      ?.find(Boolean);
  }

  findPlan(customerOrderItemId: number): PlanData {
    const offerInstances = this.getOfferInstancesByKey(OfferInstanceKeyEnum.MOBILE_PLAN_BUNDLE);
    const plan = offerInstances?.find((offer) => offer.customerOrderItemId === customerOrderItemId);

    if (!plan) {
      return null;
    }
    return new PlanData(plan, this.deviceOffer(plan.customerOrderItemId), this.priceDetail);
  }

  getOfferInstanceOfBundleOffer(offerKey: OfferInstanceKeyEnum): AbstractOfferInstance {
    return this.bundleOffers.flatMap((item) => item?.offerInstances?.[offerKey]).find(Boolean);
  }

  hasShipment(): boolean {
    return this.bundleShipmentOfferIds.length > 0;
  }
}
