import { AlternateProductOfferingProposal, CMS } from '@libs/types';
import { CMSOfferData } from './cms-offer.data';
import { drupalGetAttributes } from '../utils/drupal.utils';

export class CMSOffersData {
  private _cms_offers: CMS.DrupalJsonApiResponse<CMS.PageData[]>;
  private _bss_offers: AlternateProductOfferingProposal[];

  constructor(cms_offers?: CMS.DrupalJsonApiResponse<CMS.PageData[]>, offers?: AlternateProductOfferingProposal[]) {
    this._cms_offers = cms_offers;
    this._bss_offers = offers;
  }

  get offers() {
    return (
      this._bss_offers?.map(
        (_bss_offer) =>
          new CMSOfferData(
            drupalGetAttributes(
              this._cms_offers?.data?.find(
                (cmsOffer) => cmsOffer.attributes.field_offer_id == _bss_offer.alternateProductOffering.id,
              ),
              this._cms_offers.included,
            ),
            _bss_offer,
          ),
      ) ?? []
    );
  }

  getOffersByExpoGroupId(expoGroupId: string) {
    return this.offers.filter((offerData) => offerData.bss_offer.expoGroupIds.includes(expoGroupId));
  }

  getOfferById(offerId: string) {
    return this.offers.find((offerData) => offerData.bss_offer.offerId == offerId);
  }

  getCmsOfferById(offerId: string) {
    const findOffer = this._cms_offers?.data
      ?.filter((offerData) => offerData.attributes.field_offer_id === offerId)
      ?.find(Boolean);

    return new CMSOfferData(drupalGetAttributes(findOffer, this._cms_offers.included));
  }
}
