import { eCommon, ProductOfferDetail } from '@libs/types';

export class OfferCatalogData {
  private defaults: ProductOfferDetail[];

  constructor(defaults: ProductOfferDetail[] = []) {
    this.defaults = defaults;
  }

  get offerCatalog() {
    return this.defaults;
  }

  getByodOffer() {
    return this.filterOfferByCharValue(eCommon.ProductCharType.IS_BYOD).find(Boolean);
  }

  getNonByodOffers() {
    return this.filterOfferByCharValue(eCommon.ProductCharType.IS_BYOD, '0');
  }

  private filterOfferByCharValue(shortCode: string, value = '1') {
    const filterFn = (item: ProductOfferDetail) =>
      item.productOfferCharVals.find((char) => {
        const charShortCode = char.charShortCode || char.shortCode;
        return charShortCode === shortCode && char.val === value;
      });

    return this.offerCatalog.filter(filterFn);
  }
}
