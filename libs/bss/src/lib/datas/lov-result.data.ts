import { Lov } from '@libs/types';
import { SelectOption } from '@libs/widgets';

export class LovResultData {
  private defaults: Lov.LovResult[] = [];

  constructor(defaults?: Lov.LovResult[]) {
    this.defaults = defaults;
  }

  get items(): Lov.LovResult[] {
    return this?.defaults;
  }

  options(): SelectOption[] {
    return this.defaults?.map((item) => {
      return {
        name: item.name,
        label: item.name,
        value: item.shortCode,
        isSelected: false,
        isDisabled: false,
      };
    });
  }

  filterOptions(excludeStatusCode?: { shortCode: string; exactMatch: boolean }[]): SelectOption[] {
    let filteredDefaults = this.defaults ?? [];

    if (excludeStatusCode?.length) {
      filteredDefaults = filteredDefaults.filter((item) => {
        return !excludeStatusCode.some((excluded) => {
          if (excluded.exactMatch) {
            return item.shortCode === excluded.shortCode;
          } else {
            return item.shortCode?.includes(excluded.shortCode);
          }
        });
      });
    }

    return filteredDefaults.map((item) => ({
      name: item.name,
      label: item.name,
      value: item.shortCode,
      isSelected: false,
      isDisabled: false,
    }));
  }
}
