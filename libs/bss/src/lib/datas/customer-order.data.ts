import { CustomerOrder } from '@libs/types';

export class CustomerOrderData {
  private defaults: CustomerOrder.InquireCustomerOrdersResponse;

  constructor(defaults?: CustomerOrder.InquireCustomerOrdersResponse) {
    this.defaults = defaults;
  }

  get default(): CustomerOrder.InquireCustomerOrdersResponse {
    return this.defaults;
  }

  get content(): CustomerOrder.InquireCustomerOrder[] {
    return this.defaults?.content;
  }

  get customerOrderIds(): number[] {
    return this.defaults?.content?.map((item) => item.customerOrderId);
  }

  get totalOrderElements(): number {
    return this.defaults.totalElements ?? 0;
  }
}
