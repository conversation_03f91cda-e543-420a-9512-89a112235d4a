import { DeliveryMethod } from '@libs/types';
import { Radio } from '@libs/widgets';

export class DeliveryMethodData {
  private defaults: DeliveryMethod[] = [];

  constructor(defaults?: DeliveryMethod[]) {
    this.defaults = defaults;
  }

  get items(): DeliveryMethod[] {
    return this?.defaults;
  }

  getMethodOptions(methodId?: number): Radio[] {
    return this.defaults
      ?.filter((item) => item.shortCode === 'postalCourrier') // TODO: remove this once the API is updated
      ?.map((item) => ({
        id: String(item.id),
        name: item.name,
        value: String(item.id),
        label: item.description,
        isChecked: methodId ? item.id === methodId : false,
        isDisabled: false,
        isInvalid: false,
        isRequired: false,
      }));
  }
}
