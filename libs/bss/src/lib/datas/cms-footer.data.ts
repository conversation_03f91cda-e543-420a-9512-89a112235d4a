import { CMS } from '@libs/types';
import { CmsMenuItem } from '@libs/widgets';
import { drupalGetMenu } from '../utils/drupal.utils';

export class CmsFooterData {
  _menu: CMS.PageData[] = [];
  _topMenu: CMS.PageData[] = [];

  constructor(menu: CMS.PageData[] = [], topMenu: CMS.PageData[] = []) {
    this._menu = menu;
    this._topMenu = topMenu;
  }

  get menu(): CmsMenuItem[] {
    return drupalGetMenu(this._menu);
  }

  get topMenu(): CmsMenuItem[] {
    return drupalGetMenu(this._topMenu);
  }
}
