import { inject, Injectable } from '@angular/core';
import {
  BusinessFlow,
  BusinessFlowInitializerRequest,
  CatalogGroupContractType,
  eBusinessFlow,
  RemoteBI,
  UpdateQuoteRequest,
} from '@libs/types';
import { Store } from '@ngxs/store';
import {
  BusinessFlowInitializeAction,
  CurrentState,
  ExecuteBusinessFlowAction,
  OfferGetByodResourcesAction,
  OfferState,
  QuoteGetQuoteAction,
  QuoteState,
  SetCurrentCustomerOrderIdAction,
} from '../states';
import { map, switchMap, tap } from 'rxjs';
import { QuoteService } from './quote.service';
import { ActivatedRoute, Router } from '@angular/router';
import { IframeHandlerService, IframeManagerService } from '@libs/plugins';
import { KeyQuoteData } from '../datas/key-quote.data';
import { ContextDataType } from '../datas';
import { KnownLocalStorageKeys, LocalStorageService } from '@libs/core';
import { ProductData } from '../datas/product.data';

@Injectable({
  providedIn: 'root',
})
export class BusinessFlowInteractionService {
  private store = inject(Store);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private quoteService = inject(QuoteService);
  private iframeHandlerService = inject(IframeHandlerService);
  private iframeManagerService = inject(IframeManagerService);
  private localStorageService = inject(LocalStorageService);

  commonInitialize<T extends eBusinessFlow.Specification>(businessFlowSpecShortCode: string, data: ContextDataType<T>) {
    // TODO: (temp solution) initializeQuoteRequest values and cases are dynamic based on 'businessFlowSpecShortCode'
    const shortCode = businessFlowSpecShortCode as eBusinessFlow.Specification;
    let request = {
      businessFlowSpecShortCode,
    } as BusinessFlowInitializerRequest;

    switch (shortCode) {
      case eBusinessFlow.Specification.PURCHASE_ADDON:
      case eBusinessFlow.Specification.MANAGE_ADDON:
        request = this.purchaseAddonRequest(shortCode, data);
        break;
      case eBusinessFlow.Specification.PACKAGE_CHANGE:
        request = this.packageChangeRequest(shortCode, data);
        break;
      case eBusinessFlow.Specification.USAGE_HISTORY:
        request.nextWorkFlowStateShortCode = eBusinessFlow.WorkflowStateType.ORDER_INITIALIZE;
        request.currentWorkFlowStateShortCode = eBusinessFlow.WorkflowStateType.ORDER_INITIALIZE;
        break;
      case eBusinessFlow.Specification.ESIM_ACTIVATION:
        return this.executeBusinessFlow(businessFlowSpecShortCode, data);
      default:
        break;
    }
    return this.initialize(request).pipe(map(() => this.handleInitializeResponse(shortCode, data)));
  }

  initialize(request: BusinessFlowInitializerRequest) {
    return this.store.dispatch(new BusinessFlowInitializeAction(request)).pipe(
      tap(() => {
        // this.store.dispatch(new MyServicesClearProductsAction()); // TODO find an alternative wat to clear products
      }),
    );
  }

  // TODO: getCustomerOrderId
  byodInitialize$(productOfferId: number) {
    return this.quoteService.getCustomerOrderId().pipe(
      switchMap(() =>
        this.store.dispatch(new OfferGetByodResourcesAction({ bundleProductOfferId: productOfferId })).pipe(
          map(() => this.store.selectSnapshot(OfferState.byodResources)),
          switchMap((byodResources) => {
            switch (this.route.snapshot.queryParams.flow) {
              case eBusinessFlow.Specification.PACKAGE_CHANGE:
                return this.packageChangePlanSelection$(productOfferId);
              default:
                return this.activationInitialize$(productOfferId, byodResources.productOfferId);
            }
          }),
        ),
      ),
    );
  }

  deviceInitalize$(productOfferId: number, deviceId: number) {
    return this.quoteService.getCustomerOrderId().pipe(
      switchMap(() => {
        switch (this.route.snapshot.queryParams.flow) {
          case eBusinessFlow.Specification.PACKAGE_CHANGE:
            return this.packageChangePlanSelection$(productOfferId);
          default:
            return this.activationInitialize$(productOfferId, deviceId);
        }
      }),
    );
  }

  packageChangePlanSelection$(activationOffersProductOfferId: number) {
    const quote = this.store.selectSnapshot(QuoteState.quote);
    const orderItemIds = quote.activationBundleOffers?.map((item) => item.customerOrderItemId);

    const request = {
      activationOffers: [{ productOfferId: activationOffersProductOfferId }],
      currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.PLAN_SELECTION,
      nextWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.PLAN_SELECTION,
      removeQuoteItemIds: orderItemIds,
    } as UpdateQuoteRequest;

    return this.quoteService.updateQuote(request).pipe(
      tap(() => {
        this.router.navigate(['cart'], {
          queryParams: { customerOrderId: this.store.selectSnapshot(CurrentState.currentCustomerOrderId) },
        });
      }),
    );
  }

  activationInitialize$(productOfferId: number, activationOffersProductOfferId: number) {
    const request = {
      address: {
        extMarketingRegionId: this.localStorageService.get(KnownLocalStorageKeys.REGION),
      },
      businessFlowSpecShortCode: eBusinessFlow.Specification.MAIN_ORDER,
      planOffer: {
        productOfferId,
        instantCharValues: [
          {
            shortCode: 'actionReasonCode',
            value: 'acquisition',
            charType: 'offerInstanceChar',
          },
        ],
      },
      activationOffers: [{ productOfferId: activationOffersProductOfferId }],
      currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.ORDER_INITIALIZE,
      nextWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.PLAN_SELECTION,
      customerId: this.store.selectSnapshot(CurrentState.customerId),
      reservedOrderId: this.store.selectSnapshot(CurrentState.currentCustomerOrderId),
    } as BusinessFlowInitializerRequest;

    return this.initialize(request).pipe(
      tap(() => {
        this.router.navigate(['cart'], {
          queryParams: {
            customerOrderId: this.store.selectSnapshot(QuoteState.initializeQuoteResponse).quote?.customerOrderId,
          },
        });
      }),
    );
  }

  private packageChangeRequest(
    shortCode: eBusinessFlow.Specification,
    data: ContextDataType<eBusinessFlow.Specification.PACKAGE_CHANGE>,
  ) {
    return {
      businessFlowSpecShortCode: shortCode,
      nextWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.PLAN_SELECTION,
      currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.ORDER_INITIALIZE,
      customerId: this.store.selectSnapshot(CurrentState.customerId),
      calculateQuotePrice: true,
      sourceProduct: {
        productId: data.plan?.productId,
      },
    };
  }

  protected packageChangeRequestOLD(
    shortCode: eBusinessFlow.Specification,
    data: ContextDataType<eBusinessFlow.Specification.PACKAGE_CHANGE>,
  ): RemoteBI {
    return {
      flow: eBusinessFlow.Specification.PACKAGE_CHANGE,
      billingAccountId: data.product?.billingAccountId,
      productId: data.plan?.productId,
      shortCode: 'customer-plan-change',
      nextWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.PLAN_SELECTION,
      currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.ORDER_INITIALIZE,
      currentBIData: null as string,

      customerId: this.store.selectSnapshot(CurrentState.customerId),
    };
  }

  private handlePackageChange(response: KeyQuoteData) {
    // const requestRemote = this.packageChangeRequestOLD(eBusinessFlow.Specification.PACKAGE_CHANGE, data);
    // return this.handlePackageChangeOLD(requestRemote, response);
    return this.store
      .dispatch([
        new SetCurrentCustomerOrderIdAction(response.quote.customerOrderId),
        new QuoteGetQuoteAction({
          customerOrderId: response.quote.customerOrderId,
        }),
      ])
      .pipe(
        map(() => this.store.selectSnapshot(QuoteState.quote)),
        tap((quote) => {
          switch (quote.planSaleType) {
            case CatalogGroupContractType.POSTPAID:
              this.router.navigate([`/mobile-postpaid`], {
                queryParams: {
                  flow: quote.currentBusinessFlowSpecShortCode,
                  customerOrderId: quote.customerOrderId,
                },
              });
              break;
            case CatalogGroupContractType.PREPAID:
              this.router.navigate([`/mobile-prepaid`], {
                queryParams: {
                  flow: quote.currentBusinessFlowSpecShortCode,
                  customerOrderId: quote.customerOrderId,
                },
              });
              break;
            default:
              break;
          }
        }),
      );
  }

  private purchaseAddonRequest(
    shortCode: eBusinessFlow.Specification,
    data: ContextDataType<eBusinessFlow.Specification.PACKAGE_CHANGE>,
  ) {
    return {
      businessFlowSpecShortCode: shortCode,
      nextWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.ADDON_SELECTION,
      currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.ORDER_INITIALIZE,
      customerId: this.store.selectSnapshot(CurrentState.customerId),
      calculateQuotePrice: true,
      sourceProduct: {
        productId: new ProductData(data.product)?.plan?.productId,
      },
    };
  }

  protected purchaseAddonRequestOLD(
    shortCode: eBusinessFlow.Specification,
    data: ContextDataType<eBusinessFlow.Specification.PURCHASE_ADDON>,
  ): RemoteBI {
    return {
      flow: eBusinessFlow.Specification.PURCHASE_ADDON,
      billingAccountId: data.billingAccountId,
      productId: data.planBundleSummary?.productId,
      shortCode: 'customer-purchase-addon',
      nextWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.ADDON_SELECTION,
      currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.ORDER_INITIALIZE,
      currentBIData: null as string,
      productOfferId: data.productDetailList()?.plan.productOfferId,

      customerId: this.store.selectSnapshot(CurrentState.customerId),
    };
  }

  private handleInitializeResponse(
    shortCode: eBusinessFlow.Specification,
    data: ContextDataType<eBusinessFlow.Specification>,
  ) {
    const response = this.store.selectSnapshot(QuoteState.initializeQuoteResponse);
    switch (shortCode) {
      case eBusinessFlow.Specification.PACKAGE_CHANGE:
        return this.handlePackageChange(response);
      case eBusinessFlow.Specification.PURCHASE_ADDON:
        return this.handlePurchaseAddon(response, data);
      default:
        return data;
    }
  }

  private sendToTopWSC(remoteBI: object) {
    this.iframeHandlerService.sendMessageToParent({
      type: 'REMOTE_BI',
      payload: remoteBI,
    });
  }

  private handlePurchaseAddon(response: KeyQuoteData, data: ContextDataType<eBusinessFlow.Specification>) {
    const requestRemote = this.purchaseAddonRequestOLD(eBusinessFlow.Specification.PACKAGE_CHANGE, data);
    return this.handlePurchaseAddonOLD(requestRemote, response);
  }

  private handlePurchaseAddonOLD(requestRemote: RemoteBI, response: KeyQuoteData) {
    return this.sendRemoteStartedBI(requestRemote, response, [
      'ext',
      'customer-purchase-addon',
      {
        productId: requestRemote.productId,
        billingAccountId: requestRemote.billingAccountId,
        productOfferId: requestRemote.productOfferId,
      },
    ]);
  }

  private handlePackageChangeOLD(requestRemote: RemoteBI, response: KeyQuoteData) {
    return this.sendRemoteStartedBI(requestRemote, response, [
      'ext',
      'customer-plan-change',
      {
        productId: requestRemote.productId,
        billingAccountId: requestRemote.billingAccountId,
        productOfferId: requestRemote.productOfferId,
      },
    ]);
  }

  private sendToIframeWSC(remoteBI: RemoteBI, navigate: (string | object)[] = null) {
    this.iframeManagerService.startOldUIBI({
      type: 'REMOTE_BI',
      payload: remoteBI,
    });
    this.router.navigate(navigate || ['ext/activity-feed']);
  }

  private sendRemoteStartedBI(remoteBI: RemoteBI, response: KeyQuoteData, navigate: (string | object)[] = null) {
    this.iframeManagerService.startOldUIBI({
      type: 'REMOTE_STARTED_BI',
      payload: remoteBI,
      response: response.quote,
    });
    this.router.navigate(navigate || ['ext/activity-feed']);
  }

  private executeBusinessFlow(shortCode: string, data: ContextDataType<eBusinessFlow.Specification.ESIM_ACTIVATION>) {
    const request = {
      businessFlowSpecShortCode: shortCode,
      businessInteractionShortCode: shortCode,
      nextWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.ESIM_ACTIVATION,
      currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.ESIM_ACTIVATION,
      customerId: this.store.selectSnapshot(CurrentState.customerId),
      productId: data.productId,
    } as BusinessFlow.BusinessFlowRequest;

    const billingAccountId = this.store.selectSnapshot(CurrentState.currentBillingAccountId);
    this.store.dispatch(new ExecuteBusinessFlowAction(request)).subscribe(() => {
      this.router.navigate([`/my/products/${billingAccountId}/activation-esim`]);
    });
  }
}
