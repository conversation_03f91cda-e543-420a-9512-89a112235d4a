import { inject, Injectable } from '@angular/core';
import { Store } from '@ngxs/store';
import {
  CurrentState,
  PaymentGetPaymentMethodDetailsAction,
  PaymentRemovePaymentMethodAction,
  PaymentSaveNewPaymentMethodAction,
  PaymentState,
} from '../states';
import { catchError, of, switchMap } from 'rxjs';
import { ePayment, PaymentMethod } from '@libs/types';
import { BankAccountFormFields, CreditCardFormFields } from '@libs/widgets';
import { ToasterService, TranslateService } from '@libs/plugins';

@Injectable({
  providedIn: 'root',
})
export class PaymentService {
  private store = inject(Store);
  private toasterService = inject(ToasterService);
  private translateService = inject(TranslateService);

  removePaymentMethod$(paymentMethodId: number) {
    return this.store.dispatch(new PaymentRemovePaymentMethodAction(paymentMethodId)).pipe(
      catchError(({ error }) => {
        this.toasterService.error({
          title: 'Error',
          description: this.translateService.translate(`error.${error.message}`),
        });

        return of(null);
      }),
    );
  }

  saveNewPaymentMethod$(paymentMethodTypeId: number, request: CreditCardFormFields | BankAccountFormFields) {
    return this.store.dispatch(new PaymentGetPaymentMethodDetailsAction(paymentMethodTypeId)).pipe(
      switchMap(() => {
        const paymentMethodTypeDetail = this.store.selectSnapshot(PaymentState.paymentMethodDetails);
        const saveRequest = {
          customerId: this.store.selectSnapshot(CurrentState.customerId),
          name: paymentMethodTypeDetail.name,
          paymentMethodType: {
            paymentMethodTypeId,
            shortCode: paymentMethodTypeDetail.shortCode,
          },
          paymentMethodTypeId,
          createDate: new Date(),
          paymentMethodCharacteristics: this.requestPaymentCharacterictics(
            paymentMethodTypeDetail.paymentMethodTypeCharacteristics,
            request,
          ),
        } as PaymentMethod.SavePaymentMethod;

        return this.store.dispatch(new PaymentSaveNewPaymentMethodAction(saveRequest));
      }),
    );
  }

  private requestPaymentCharacterictics(
    paymentMethodCharacteristics: PaymentMethod.PaymentMethodDetailTypeCharacteristics[],
    request?: CreditCardFormFields | BankAccountFormFields,
  ): PaymentMethod.PaymentMethodTypeCharacteristics[] {
    return paymentMethodCharacteristics.map((data) => ({
      ...data,
      characteristicValue: {
        ...data.characteristicValue.find(Boolean),
        value: this.shortCodeAndValue(data, request),
      },
    }));
  }

  private shortCodeAndValue(
    paymentMethodCharacteristic: PaymentMethod.PaymentMethodDetailTypeCharacteristics,
    request?: CreditCardFormFields & BankAccountFormFields,
  ): string {
    switch (paymentMethodCharacteristic?.shortCode) {
      case ePayment.PaymentMethodCharType.CARD_HOLDER:
        return request?.cardHolder || '';
      case ePayment.PaymentMethodCharType.LAST_FOUR_DIGIT:
        return request.creditCardNumber.substring(12, 16);
      case ePayment.PaymentMethodCharType.EXPR_DATE:
        return request.exprDate.length === 4
          ? request.exprDate.substring(0, 2) + '/' + request.exprDate.substring(2, 4)
          : request.exprDate;
      case ePayment.PaymentMethodCharType.EXT_CARD_REF_ID:
        return request.cvc;
      case ePayment.PaymentMethodCharType.BANK_NAME:
        return request.bankName;
      case ePayment.PaymentMethodCharType.IBAN:
        return request.iban;
      case ePayment.PaymentMethodCharType.BANK_ACCOUNT_NUMBER_DISPLAY:
        return request.iban.substring(22, 26);
      case ePayment.PaymentMethodCharType.NAME_ON_BANK_ACCOUNT:
        return request.nameOnBankAccount;
      default:
        return paymentMethodCharacteristic.characteristicValue?.[0].value;
    }
  }
}
