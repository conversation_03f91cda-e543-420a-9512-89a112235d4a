import { inject, Injectable } from '@angular/core';
import {
  AddActivationOffer,
  Address,
  eBusinessFlow,
  eCommon,
  ePayment,
  OrderSubmitRequest,
  QuoteNextStateRequest,
  SimpleDeliveryMethod,
  UpdateOfferInstanceQuoteChar,
  UpdateQuoteRequest,
} from '@libs/types';
import { select, Store } from '@ngxs/store';
import { map, Observable, of, switchMap } from 'rxjs';
import {
  BusinessFlowState,
  CurrentState,
  GetQuotePriceDetailAction,
  QuoteGetQuoteAction,
  QuoteNextStateAction,
  QuoteQueryAction,
  QuoteState,
  QuoteSubmitQuoteAction,
  QuoteUpdateQuoteAction,
  SetCurrentCustomerOrderIdAction,
} from '../states';

@Injectable({
  providedIn: 'root',
})
export class QuoteService {
  private store = inject(Store);
  customerOrderId = select(CurrentState.currentCustomerOrderId);

  updateQuote(request: UpdateQuoteRequest) {
    if (!request.nextWorkFlowStateShortCode) {
      request.nextWorkFlowStateShortCode = this.store
        .selectSnapshot(BusinessFlowState.businessWorkflowConfig)
        .getNextStepShortCode(request.currentWorkFlowStateShortCode);
    }
    return this.store.dispatch(
      new QuoteUpdateQuoteAction({
        customerOrderId: this.customerOrderId(),
        ...request,
      }),
    );
  }

  updateCreditCheck$(value: string) {
    const updateQuoteChars = [
      {
        shortCode: eCommon.QuoteChars.CUSTOMER_CREDIT_SCORE_CONSENT_AUTHORIZED,
        value,
        charValueShortCode: eCommon.QuoteChars.CUSTOMER_CREDIT_SCORE_CONSENT_AUTHORIZED,
      },
    ];

    const request = {
      currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.CREDIT_CHECK,
      updateQuoteChars,
    } as UpdateQuoteRequest;

    return this.updateQuote(request);
  }

  savePaymentMethods$(paymentReferencePaymentId: number) {
    const quote = this.store.selectSnapshot(QuoteState.quote);

    const billingInfo = quote.paymentInformation?.billingInfo?.map((item) => ({
      ...item,
      paymentReference: {
        rowId: paymentReferencePaymentId,
        dataType: ePayment.PaymentMethodType.PYMNT_METH,
      },
    }));

    const paymentInformation = {
      paymentReference: {
        rowId: paymentReferencePaymentId,
        dataType: ePayment.PaymentMethodType.PYMNT_METH,
      },
      billingInfo,
    };

    const request = {
      paymentInformation,
      nextWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.ORDER_REVIEW,
      currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.PAYMENT_BILLING,
    } as UpdateQuoteRequest;
    return this.updateQuote(request);
  }

  submitQuote$() {
    const request = {
      businessFlowSpecShortCode: eBusinessFlow.Specification.MAIN_ORDER,
      customerOrderId: this.customerOrderId(),
      currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.ORDER_REVIEW,
      nextWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.ORDER_SUBMIT,
    } as OrderSubmitRequest;
    return this.store.dispatch(new QuoteSubmitQuoteAction(request));
  }

  submitPlanChangeQuote$() {
    const request = {
      businessFlowSpecShortCode: eBusinessFlow.Specification.PACKAGE_CHANGE,
      customerOrderId: this.customerOrderId(),
      currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.CART_SUMMARY,
      nextWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.ORDER_SUBMIT,
    } as OrderSubmitRequest;
    return this.store.dispatch(new QuoteSubmitQuoteAction(request));
  }

  updateMsisdn$(
    updateQuoteChars: UpdateOfferInstanceQuoteChar[],
    activationOffers: AddActivationOffer[],
    nextState: eBusinessFlow.WorkflowStateType,
    removeQuoteItemIds: number[] = [],
  ) {
    const request = {
      nextWorkFlowStateShortCode: nextState,
      currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.PRODUCT_CONF,
      activationOffers: activationOffers,
      removeQuoteItemIds: removeQuoteItemIds,
      updateOfferInstanceChars: updateQuoteChars,
    } as UpdateQuoteRequest;

    return this.updateQuote(request);
  }

  // TODO: getCustomerOrderId remove
  getCustomerOrderId(): Observable<number> {
    if (
      this.store.selectSnapshot(CurrentState.currentCustomerOrderId) ||
      !this.store.selectSnapshot(CurrentState.customerId)
    ) {
      return of(this.store.selectSnapshot(CurrentState.currentCustomerOrderId));
    }

    return this.store
      .dispatch(
        new QuoteQueryAction({
          customerId: this.store.selectSnapshot(CurrentState.customerId),
          responseQuery: 'customerOrderId',
        }),
      )
      .pipe(
        switchMap(() => {
          const customerOrderId = (
            this.store.selectSnapshot(QuoteState.quoteQueryResponse) as {
              data: number;
            }
          ).data;
          return this.store.dispatch(new SetCurrentCustomerOrderIdAction(customerOrderId));
        }),
        map(() => {
          return this.store.selectSnapshot(CurrentState.currentCustomerOrderId);
        }),
      );
  }

  updateDefaultDeliveryAddress$(deliveryAddress: Address) {
    const updateRequest = {
      currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.DEFAULT_DELIVERY_ADDRESS_SELECTION,
      nextWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.DELIVERY,
      deliveryInformation: { deliveryAddress },
    } as UpdateQuoteRequest;

    return this.updateQuote(updateRequest);
  }

  updateDeliveryInformation$(deliveryOptionId: number, deliveryMethodId: number, deliveryAddress: Address) {
    const quote = this.store.selectSnapshot(QuoteState.quote);
    const deliverInstallationRetrieveConfig = this.store.selectSnapshot(
      QuoteState.getOfferDeliverInstallationRetrieveConfig,
    );
    const deliveryMethod = deliverInstallationRetrieveConfig.items
      .flatMap((item) => item.deliverMethods)
      .find((item) => item.id === +deliveryMethodId);
    const expectedDeliveryDate = new Date(
      deliveryMethod?.deliveryOptions.find((item) => item.offerId === deliveryOptionId)?.expectedDeliveryDate,
    );
    const deliveryOfferIds = deliverInstallationRetrieveConfig.items.flatMap((item) => item.offerInstanceIdList);
    const activationOffers = [
      {
        productOfferId: deliveryOptionId,
        relatedCustomerOrderItemIdList: [
          ...deliveryOfferIds,
          ...quote.nonBYODBundleOffers.map((item) => item.customerOrderItemId),
        ],
      } as AddActivationOffer,
    ];

    const removeQuoteItemIds = [...new Set(quote.bundleShipmentOfferIds)];

    const deliveryInformation = {
      deliveryAddress,
      expectedDeliveryDate,
      deliveryMethod: {
        deliveryMethodId,
        deliveryOfferId: deliveryOptionId,
        deliveryMethodType: { id: deliveryMethod?.deliveryMethTpId },
      } as SimpleDeliveryMethod,
    };

    const request = {
      currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.DELIVERY,
      nextWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.DELIVERY,
      activationOffers,
      deliveryInformation,
      removeQuoteItemIds,
    } as UpdateQuoteRequest;

    return this.updateQuote(request);
  }

  buildNextStateRequest(
    currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType,
    nextWorkFlowStateShortCode?: eBusinessFlow.WorkflowStateType,
  ) {
    if (!nextWorkFlowStateShortCode) {
      nextWorkFlowStateShortCode = this.store
        .selectSnapshot(BusinessFlowState.businessWorkflowConfig)
        .getNextStepShortCode(currentWorkFlowStateShortCode);
    }
    const request = {
      customerId: this.store.selectSnapshot(CurrentState.customerId),
      customerOrderId: this.customerOrderId(),
      currentWorkFlowStateShortCode,
      nextWorkFlowStateShortCode,
    } as QuoteNextStateRequest;

    return new QuoteNextStateAction(request);
  }

  callNextState(
    currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType,
    nextWorkFlowStateShortCode?: eBusinessFlow.WorkflowStateType,
  ) {
    return this.store.dispatch(this.buildNextStateRequest(currentWorkFlowStateShortCode, nextWorkFlowStateShortCode));
  }

  updateInvoiceInformation$(billingAddress: Address, isExistingInvoiceAddress: boolean) {
    const quote = this.store.selectSnapshot(QuoteState.quote);

    const prepaidBillingInfo = quote.prepaidBundleOffers.length
      ? [
          {
            ...(isExistingInvoiceAddress && { billingAddress }),
            offerInstanceKey: quote.prepaidBundleOffers.flatMap((item) => ({
              customerOrderItemId: item.customerOrderItemId,
            })),
          },
        ]
      : [];

    const postpaidBillingInfo = quote.postpaidBundleOffers.length
      ? [
          {
            ...(isExistingInvoiceAddress && {
              selectedInvoicingAccountKey: { accountId: billingAddress?.rowId },
              billingAddress,
            }),
            offerInstanceKey: quote.postpaidBundleOffers.flatMap((item) => ({
              customerOrderItemId: item.customerOrderItemId,
            })),
          },
        ]
      : [];

    const request = {
      currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.INVOICE_SELECT,
      paymentInformation: { billingInfo: [...prepaidBillingInfo, ...postpaidBillingInfo] },
    } as UpdateQuoteRequest;

    return this.updateQuote(request);
  }

  updateBillingAddress$(billingAddress: Address, isExistingInvoiceAddress: boolean, isSameInvoiceAccount: boolean) {
    const quote = this.store.selectSnapshot(QuoteState.quote);
    const bundleOffers = quote.bundleOffers;

    const prepaidBillingInfo = quote.prepaidBundleOffers.length
      ? [
          {
            ...(isExistingInvoiceAddress && {
              selectedInvoicingAccountKey: { accountId: billingAddress?.rowId },
            }),
            billingAddress,
            offerInstanceKey: quote.prepaidBundleOffers.flatMap((item) => ({
              customerOrderItemId: item.customerOrderItemId,
            })),
          },
        ]
      : [];

    const postpaidBillingInfo = quote.postpaidBundleOffers.length
      ? [
          {
            ...(isExistingInvoiceAddress && {
              selectedInvoicingAccountKey: { accountId: billingAddress?.rowId },
            }),
            billingAddress,
            offerInstanceKey: quote.postpaidBundleOffers.flatMap((item) => ({
              customerOrderItemId: item.customerOrderItemId,
            })),
          },
        ]
      : [];

    const sameInvoiceChar = bundleOffers.map((item) => ({
      customerOrderItemId: item.customerOrderItemId,
      shortCode: eCommon.QuoteChars.IS_SAME_INVOICE,
      charValueShortCode: isSameInvoiceAccount
        ? eCommon.QuoteChars.IS_SAME_INVOICE_YES
        : eCommon.QuoteChars.IS_SAME_INVOICE_NO,
      value: isSameInvoiceAccount ? '1' : '0',
    }));

    const request = {
      currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.BILLING_ADDRESS,
      paymentInformation: { billingInfo: [...prepaidBillingInfo, ...postpaidBillingInfo] },
      updateQuoteChars: [...sameInvoiceChar],
    } as UpdateQuoteRequest;

    return this.updateQuote(request);
  }

  inquireQuote(currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType) {
    return this.store.dispatch(
      new QuoteGetQuoteAction({ customerOrderId: this.customerOrderId(), currentWorkFlowStateShortCode }),
    );
  }

  getQuotePriceDetail() {
    return this.store.dispatch(new GetQuotePriceDetailAction({ customerOrderId: this.customerOrderId() }));
  }
}
