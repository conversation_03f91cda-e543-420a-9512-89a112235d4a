import { inject, Injectable, Signal } from '@angular/core';
import { Store } from '@ngxs/store';
import { CustomerState } from '../states/customer-state/customer.state';
import {
  Address,
  CommunicationPreference,
  ContactMediumType,
  CreateAddressWrapper,
  eBusinessFlow,
  eCommon,
  CustomerInfo,
  SelectOption,
} from '@libs/types';
import { DataListItem } from '@eds/components';
import { computed, signal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { CommunicationPreferencesState } from '../states/communication-preferences-state/communication-preferences.state';
import {
  GetCommunicationPreferencesAction,
  GetCustomerContactMediumConsentAction,
} from '../states/communication-preferences-state/communication-preferences.actions';
import { EMPTY, Observable, switchMap } from 'rxjs';
import {
  CreateCustomerContactMediumAction,
  DeleteCustomerContactMediumAction,
  UpdateCustomerContactMediumAction,
} from '../states/profile-settings-state/profile-settings.actions';
import { CurrentState } from '../states/current-state/current.state';
import {
  CustomerCreateAddressAction,
  CustomerGetAddressListAction,
  InquireCustomerContactMediumAction,
  InquireCustomerInformationAction,
  RemoveCustomerAddressAction,
  UpdateCustomerAddressAction,
  UpdateCustomerDemographicInfoAction,
} from '../states/customer-state';
import { BusinessFlowStateType } from '@libs/bss';
import { PermissionState } from '../states/permission-state/permission.state';
import { TranslateService } from '@libs/plugins';

@Injectable({
  providedIn: 'root',
})
export class CustomerProfileService {
  private store = inject(Store);
  private translateService = inject(TranslateService);
  private businessFlowState = toSignal(this.store.select((state) => state.BusinessFlowState as BusinessFlowStateType));

  // Contact Medium
  readonly contactMediumList = toSignal(this.store.select(CustomerState.getContactMediumList));
  readonly customerContactMediumConsent = toSignal(
    this.store.select(CommunicationPreferencesState.customerContactMediumConsent),
  );

  // Address
  private readonly addressList = toSignal(this.store.select(CustomerState.getAddressList));
  readonly addresses = computed<Address[]>(() => {
    const items = this.addressList()?.items;
    if (!items) return [];
    return [...items].sort((a, b) => Number(b.isPrimary ?? false) - Number(a.isPrimary ?? false));
  });

  // Profile Info
  readonly customerDetails = toSignal(this.store.select(CustomerState.getDetails));
  private readonly individualCustomerFormLovContent = toSignal(
    this.store.select(CustomerState.getIndividualCustomerFormLovContent),
  );
  readonly languageOptions = computed<SelectOption[]>(() =>
    this.individualCustomerFormLovContent()?.languageList.map((language) => ({
      value: language.shortCode,
      label: language.name,
      name: language.name,
      isSelected: this.customerDetails()?.userInformation.language.shortCode === language.shortCode,
      isDisabled: false,
    })),
  );
  readonly occupationOptions = computed<SelectOption[]>(() =>
    this.individualCustomerFormLovContent()?.occupationTypeList.map((occupation) => ({
      value: occupation.shortCode,
      label: occupation.name,
      name: occupation.name,
      isSelected: this.customerDetails()?.partyInformation.occupation?.shortCode === occupation.shortCode,
      isDisabled: false,
    })),
  );
  readonly profileDataList = computed<DataListItem[]>(() => {
    const customerData = this.customerDetails();
    if (!customerData) return [];
    const { partyInformation, userInformation, customerInformation } = customerData;
    const list = [
      { key: 'customerId', value: customerInformation.customerId.toString() },
      { key: 'userName', value: userInformation.uname },
      { key: 'firstName', value: partyInformation.firstName },
      { key: 'lastName', value: partyInformation.lastName },
      { key: 'birthDate', value: partyInformation.birthDate },
      { key: 'occupation', value: partyInformation.occupation?.name },
      { key: 'language', value: userInformation.language.name },
    ];
    return list.filter((item) => item.value !== undefined);
  });

  // Permissions
  private readonly profilePermissions = toSignal(
    this.store.select(PermissionState.getPermission(eCommon.BsnInterSpecShortCode.CUSTOMER_PROFILE)),
  );
  readonly hasPermissionToEditProfile = computed(() => {
    const permissionData = this.profilePermissions();
    if (!permissionData?.items) return false;
    const editPermission = permissionData.items.find(
      (item) => item.shortCode === eCommon.BsnInterSpecShortCode.CUSTOMER_DEMOGRAPHIC_EDIT,
    );
    return !!editPermission?.visible;
  });

  hasPermissionFor(level: eBusinessFlow.Levels, interaction: eCommon.BsnInterSpecShortCode): Signal<boolean> {
    return computed(() => {
      const state = this.businessFlowState();
      const interactions = state?.applicableInteractionMap?.[level]?.interactions ?? [];
      return !!interactions.find((i) => i.shortCode === interaction);
    });
  }

  // Profile Info Methods
  updateDemographicInfo(payload: CustomerInfo.UpdateCustomerDemographicInfo): Observable<void> {
    return this.store
      .dispatch(new UpdateCustomerDemographicInfoAction(payload))
      .pipe(switchMap(() => this.refreshDemographicInfo(payload.customerId)));
  }

  refreshDemographicInfo(customerId: number): Observable<void> {
    return this.store.dispatch(new InquireCustomerInformationAction({ customerId }));
  }

  // Contact Medium Methods
  getContactMediumListByType(
    type: ContactMediumType.ContactMediumTypeGroupCode,
  ): Signal<ContactMediumType.ContactMediumDTO[]> {
    return computed(() => {
      const list = this.contactMediumList() || [];
      const filtered = list.filter((c) => c.contactMediumType.groupTypeCode === type);
      return [...filtered].sort((a, b) => Number(b.isPrimary ?? false) - Number(a.isPrimary ?? false));
    });
  }

  fetchCommunicationPreferences(body: CommunicationPreference.PartyPrivacySearchRequestForCrud): Observable<void> {
    return this.store.dispatch(new GetCommunicationPreferencesAction(body));
  }

  fetchContactMediumConsent(groupCode: ContactMediumType.ContactMediumTypeGroupCode): Observable<void> {
    const customerId = this.store.selectSnapshot(CurrentState.customerId);
    if (customerId) {
      return this.store.dispatch(new GetCustomerContactMediumConsentAction(customerId, groupCode));
    }
    return EMPTY;
  }

  createContactMedium(payload: ContactMediumType.CreateCustomerContactMediumRequest): Observable<void> {
    return this.store.dispatch(new CreateCustomerContactMediumAction(payload));
  }

  updateContactMedium(payload: ContactMediumType.CreateCustomerContactMediumRequest): Observable<void> {
    return this.store.dispatch(new UpdateCustomerContactMediumAction(payload));
  }

  deleteContactMedium(id: number): Observable<void> {
    return this.store.dispatch(new DeleteCustomerContactMediumAction(id));
  }

  refreshContactList(): Observable<void> {
    const customerId = this.store.selectSnapshot(CurrentState.customerId);
    return this.store.dispatch(new InquireCustomerContactMediumAction({ customerId }));
  }

  // Address Methods
  createAddress(payload: CreateAddressWrapper): Observable<void> {
    return this.store.dispatch(new CustomerCreateAddressAction(payload));
  }

  updateAddress(payload: CustomerInfo.UpdateCustomerAddressRequest): Observable<void> {
    return this.store.dispatch(new UpdateCustomerAddressAction(payload));
  }

  deleteAddress(addressId: number): Observable<void> {
    return this.store.dispatch(new RemoveCustomerAddressAction({ addressId }));
  }

  refreshAddressList(): Observable<void> {
    const customerId = this.store.selectSnapshot(CurrentState.customerId);
    return this.store.dispatch(new CustomerGetAddressListAction(customerId));
  }

  isEditMode = signal(false);

  setEditMode(isEditMode: boolean): void {
    this.isEditMode.set(isEditMode);
  }
}
