import { AbstractQuote } from '@libs/types';

export const MOCK_GET_QOUTE = {
  customerOrderId: 650100006955,
  referenceOrderId: 0,
  createdDate: 1737980615767,
  flowSpecCode: 'MAIN_ORDER',
  customer: {
    id: 550100000459,
    name: '<PERSON>cwvz<PERSON>',
    lastName: 'Floydigeng',
  },
  paymentInfo: {
    id: 139004,
    type: 'PYMNT_METH',
    status: 'PAID',
    chargedAmount: 582.5,
    chargedTaxAmount: 582.5,
  },
  offerInstances: {
    mobilePlanBundle: [
      {
        customerOrderItemId: -2,
        offerId: 12002,
        offerName: 'All Inclusive Prepaid 20 GB',
        quantity: 1,
        createdDate: 1737980615868,
        removable: true,
        bundle: true,
        actionCode: 'ACTIVATION',
        familyCategoryCode: 'mobile',
        familyCategoryName: 'Mobile',
        productType: 'planBundle',
        chars: {
          msisdnReservationExpirationDate: {
            code: 'msisdnReservationExpirationDate',
            value: 'EMPTY',
            optional: false,
            editable: false,
            name: 'MSISDN Reservation Expiration Date ',
            min: 0,
            max: 0,
            values: [],
          },
          msisdnReservationId: {
            code: 'msisdnReservationId',
            value: 'EMPTY',
            optional: false,
            editable: false,
            name: 'MSISDN Reservation ID ',
            min: 0,
            max: 0,
            values: [],
          },
          actionReasonCode: {
            code: 'actionReasonCode',
            value: 'acquisition',
            optional: false,
            editable: false,
            name: 'actionReasonCode',
            min: 0,
            max: 0,
            values: [
              {
                code: 'actionReasonCode',
                value: 'acquisition',
                label: 'EMPTY',
                default: true,
                uom: {
                  code: 'example',
                  name: 'Example',
                },
              },
            ],
          },
          productActivationOrderId: {
            code: 'productActivationOrderId',
            value: 'EMPTY',
            optional: false,
            editable: false,
            name: 'productActivationOrderId',
            min: 0,
            max: 0,
            values: [],
          },
        },
        offerInstances: {
          mobileDevice: [
            {
              customerOrderItemId: -12,
              offerId: 10016,
              offerName: 'Google Pixel 5 128 GB',
              quantity: 1,
              createdDate: 1737980615918,
              removable: true,
              bundle: false,
              actionCode: 'ACTIVATION',
              familyCategoryCode: 'mobile',
              familyCategoryName: 'Mobile',
              productType: 'device',
              chars: {
                installmentOptions: {
                  code: 'installmentOptions',
                  value: '1',
                  optional: true,
                  editable: false,
                  name: 'Installment Options',
                  min: 0,
                  max: 0,
                  values: [],
                  sortId: 3,
                },
                capacity: {
                  code: 'capacity',
                  value: '128Gb',
                  optional: true,
                  editable: false,
                  name: 'Capacity',
                  min: 0,
                  max: 0,
                  values: [],
                  sortId: 2,
                },
                color: {
                  code: 'color',
                  value: 'black',
                  optional: true,
                  editable: false,
                  name: 'Color',
                  min: 0,
                  max: 0,
                  values: [],
                  sortId: 1,
                },
              },
            },
          ],
          mobilePlan: [
            {
              customerOrderItemId: -3,
              offerId: 11002,
              offerName: 'All Inclusive 20 GB',
              quantity: 1,
              createdDate: 1737980615870,
              removable: true,
              bundle: false,
              actionCode: 'ACTIVATION',
              familyCategoryCode: 'mobile',
              familyCategoryName: 'Mobile',
              productType: 'plan',
              chars: {
                genMarketingText: {
                  code: 'genMarketingText',
                  value: '30daysOfSatisfactionGuarantee',
                  optional: true,
                  editable: false,
                  name: 'General Marketing Text',
                  min: 0,
                  max: 0,
                  values: [],
                },
                callMarketingText: {
                  code: 'callMarketingText',
                  value: 'PPUNationWideMinutes',
                  optional: true,
                  editable: false,
                  name: 'Voice Marketing Text',
                  min: 0,
                  max: 0,
                  values: [],
                },
                hierType: {
                  code: 'hierType',
                  value: 'Line',
                  optional: true,
                  editable: false,
                  name: 'hierType',
                  min: 0,
                  max: 0,
                  values: [],
                },
                isMainPlan: {
                  code: 'isMainPlan',
                  value: '1',
                  optional: true,
                  editable: false,
                  name: 'Main Plan Indicator',
                  min: 0,
                  max: 0,
                  values: [],
                },
                isHiddenInInvoice: {
                  code: 'isHiddenInInvoice',
                  value: '1',
                  optional: true,
                  editable: false,
                  name: 'Hidden in Invoice',
                  min: 0,
                  max: 0,
                  values: [],
                },
                msisdnReservationExpirationDate: {
                  code: 'msisdnReservationExpirationDate',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'MSISDN Reservation Expiration Date ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                msisdnReservationId: {
                  code: 'msisdnReservationId',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'MSISDN Reservation ID ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                productActivationOrderId: {
                  code: 'productActivationOrderId',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'productActivationOrderId',
                  min: 0,
                  max: 0,
                  values: [],
                },
              },
            },
          ],
          dataBucket: [
            {
              customerOrderItemId: -4,
              offerId: 11102,
              offerName: 'Prepaid Data 20 GB',
              quantity: 1,
              createdDate: 1737980615870,
              removable: true,
              bundle: false,
              actionCode: 'ACTIVATION',
              familyCategoryCode: 'mobile',
              familyCategoryName: 'Mobile',
              productType: 'subOffer',
              chars: {
                dataAmount: {
                  code: 'dataAmount',
                  value: '20',
                  optional: true,
                  editable: false,
                  name: 'dataAmount',
                  min: 0,
                  max: 0,
                  values: [],
                },
                bucketType: {
                  code: 'bucketType',
                  value: 'dataPP',
                  optional: true,
                  editable: false,
                  name: 'Bucket Type',
                  min: 0,
                  max: 0,
                  values: [],
                },
                freeUnitType: {
                  code: 'freeUnitType',
                  value: 'lineLevel',
                  optional: true,
                  editable: false,
                  name: 'freeUnitType',
                  min: 0,
                  max: 0,
                  values: [],
                },
                msisdnReservationExpirationDate: {
                  code: 'msisdnReservationExpirationDate',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'MSISDN Reservation Expiration Date ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                msisdnReservationId: {
                  code: 'msisdnReservationId',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'MSISDN Reservation ID ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                productActivationOrderId: {
                  code: 'productActivationOrderId',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'productActivationOrderId',
                  min: 0,
                  max: 0,
                  values: [],
                },
              },
            },
          ],
          smsBucket: [
            {
              customerOrderItemId: -6,
              offerId: 11110,
              offerName: 'SMS Unlimited',
              quantity: 1,
              createdDate: 1737980615872,
              removable: true,
              bundle: false,
              actionCode: 'ACTIVATION',
              familyCategoryCode: 'mobile',
              familyCategoryName: 'Mobile',
              productType: 'subOffer',
              chars: {
                smsAmount: {
                  code: 'smsAmount',
                  value: 'unlimited',
                  optional: true,
                  editable: false,
                  name: 'smsAmount',
                  min: 0,
                  max: 0,
                  values: [],
                },
                isHiddenInInvoice: {
                  code: 'isHiddenInInvoice',
                  value: '1',
                  optional: true,
                  editable: false,
                  name: 'Hidden in Invoice',
                  min: 0,
                  max: 0,
                  values: [],
                },
                freeUnitType: {
                  code: 'freeUnitType',
                  value: 'lineLevel',
                  optional: true,
                  editable: false,
                  name: 'freeUnitType',
                  min: 0,
                  max: 0,
                  values: [],
                },
                bucketType: {
                  code: 'bucketType',
                  value: 'SMS/MMS',
                  optional: true,
                  editable: false,
                  name: 'Bucket Type',
                  min: 0,
                  max: 0,
                  values: [],
                },
                msisdnReservationExpirationDate: {
                  code: 'msisdnReservationExpirationDate',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'MSISDN Reservation Expiration Date ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                msisdnReservationId: {
                  code: 'msisdnReservationId',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'MSISDN Reservation ID ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                productActivationOrderId: {
                  code: 'productActivationOrderId',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'productActivationOrderId',
                  min: 0,
                  max: 0,
                  values: [],
                },
              },
            },
          ],
          voiceBucket: [
            {
              customerOrderItemId: -5,
              offerId: 11108,
              offerName: 'Prepaid Voice 250 Minutes',
              quantity: 1,
              createdDate: 1737980615872,
              removable: true,
              bundle: false,
              actionCode: 'ACTIVATION',
              familyCategoryCode: 'mobile',
              familyCategoryName: 'Mobile',
              productType: 'subOffer',
              chars: {
                voiceAmount: {
                  code: 'voiceAmount',
                  value: '250',
                  optional: true,
                  editable: false,
                  name: 'voiceAmount',
                  min: 0,
                  max: 0,
                  values: [],
                },
                bucketType: {
                  code: 'bucketType',
                  value: 'voice',
                  optional: true,
                  editable: false,
                  name: 'Bucket Type',
                  min: 0,
                  max: 0,
                  values: [],
                },
                isHiddenInInvoice: {
                  code: 'isHiddenInInvoice',
                  value: '1',
                  optional: true,
                  editable: false,
                  name: 'Hidden in Invoice',
                  min: 0,
                  max: 0,
                  values: [],
                },
                freeUnitType: {
                  code: 'freeUnitType',
                  value: 'lineLevel',
                  optional: true,
                  editable: false,
                  name: 'freeUnitType',
                  min: 0,
                  max: 0,
                  values: [],
                },
                msisdnReservationExpirationDate: {
                  code: 'msisdnReservationExpirationDate',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'MSISDN Reservation Expiration Date ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                msisdnReservationId: {
                  code: 'msisdnReservationId',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'MSISDN Reservation ID ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                productActivationOrderId: {
                  code: 'productActivationOrderId',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'productActivationOrderId',
                  min: 0,
                  max: 0,
                  values: [],
                },
              },
            },
          ],
        },
      },
      {
        customerOrderItemId: -13,
        offerId: 12002,
        offerName: 'All Inclusive Prepaid 21 GB',
        quantity: 1,
        createdDate: 1737980657352,
        removable: true,
        bundle: true,
        actionCode: 'ACTIVATION',
        familyCategoryCode: 'mobile',
        familyCategoryName: 'Mobile',
        productType: 'planBundle',
        chars: {
          msisdnReservationExpirationDate: {
            code: 'msisdnReservationExpirationDate',
            value: 'EMPTY',
            optional: false,
            editable: false,
            name: 'MSISDN Reservation Expiration Date ',
            min: 0,
            max: 0,
            values: [],
          },
          msisdnReservationId: {
            code: 'msisdnReservationId',
            value: 'EMPTY',
            optional: false,
            editable: false,
            name: 'MSISDN Reservation ID ',
            min: 0,
            max: 0,
            values: [],
          },
          actionReasonCode: {
            code: 'actionReasonCode',
            value: 'acquisition',
            optional: false,
            editable: false,
            name: 'actionReasonCode',
            min: 0,
            max: 0,
            values: [
              {
                code: 'actionReasonCode',
                value: 'acquisition',
                label: 'EMPTY',
                default: true,
                uom: {
                  code: 'example',
                  name: 'Example',
                },
              },
            ],
          },
          productActivationOrderId: {
            code: 'productActivationOrderId',
            value: 'EMPTY',
            optional: false,
            editable: false,
            name: 'productActivationOrderId',
            min: 0,
            max: 0,
            values: [],
          },
        },
        offerInstances: {
          voiceNetworkService: [
            {
              customerOrderItemId: -19,
              offerId: 400132,
              offerName: 'Voice Pay As You Go',
              quantity: 1,
              createdDate: 1737980657361,
              removable: true,
              bundle: false,
              actionCode: 'ACTIVATION',
              familyCategoryCode: 'mobile',
              familyCategoryName: 'Mobile',
              productType: 'resourceFacingService',
              chars: {
                isHiddenInInvoice: {
                  code: 'isHiddenInInvoice',
                  value: '1',
                  optional: true,
                  editable: false,
                  name: 'Hidden in Invoice',
                  min: 0,
                  max: 0,
                  values: [],
                },
                callForwardNumber: {
                  code: 'callForwardNumber',
                  value: 'EMPTY',
                  optional: true,
                  editable: false,
                  name: 'Call Forward Number',
                  min: 0,
                  max: 0,
                  values: [],
                },
                callForward: {
                  code: 'callForward',
                  value: 'TRUE',
                  optional: true,
                  editable: false,
                  name: 'Call Forward',
                  min: 0,
                  max: 0,
                  values: [],
                },
                callWait: {
                  code: 'callWait',
                  value: 'TRUE',
                  optional: true,
                  editable: false,
                  name: 'Call Waiting',
                  min: 0,
                  max: 0,
                  values: [],
                },
                clip: {
                  code: 'clip',
                  value: 'TRUE',
                  optional: true,
                  editable: false,
                  name: 'Outgoing Caller ID',
                  min: 0,
                  max: 0,
                  values: [],
                },
                conferenceCall: {
                  code: 'conferenceCall',
                  value: 'TRUE',
                  optional: true,
                  editable: false,
                  name: 'Conference Call',
                  min: 0,
                  max: 0,
                  values: [],
                },
                incomingCallBarring: {
                  code: 'incomingCallBarring',
                  value: '0',
                  optional: true,
                  editable: false,
                  name: 'incomingCallBarring',
                  min: 0,
                  max: 0,
                  values: [],
                },
                specBarring1: {
                  code: 'specBarring1',
                  value: 'FALSE',
                  optional: true,
                  editable: false,
                  name: 'specBarring1',
                  min: 0,
                  max: 0,
                  values: [],
                },
                specBarring2: {
                  code: 'specBarring2',
                  value: 'FALSE',
                  optional: true,
                  editable: false,
                  name: 'specBarring2',
                  min: 0,
                  max: 0,
                  values: [],
                },
                specBarring3: {
                  code: 'specBarring3',
                  value: 'FALSE',
                  optional: true,
                  editable: false,
                  name: 'specBarring3',
                  min: 0,
                  max: 0,
                  values: [],
                },
                specBarring4: {
                  code: 'specBarring4',
                  value: 'FALSE',
                  optional: true,
                  editable: false,
                  name: 'specBarring4',
                  min: 0,
                  max: 0,
                  values: [],
                },
                voiceChargingMode: {
                  code: 'voiceChargingMode',
                  value: 'offline',
                  optional: true,
                  editable: false,
                  name: 'voiceChargingMode',
                  min: 0,
                  max: 0,
                  values: [],
                },
                clir: {
                  code: 'clir',
                  value: '0',
                  optional: true,
                  editable: false,
                  name: 'Incoming Caller ID',
                  min: 0,
                  max: 0,
                  values: [],
                },
                customerVolteOption: {
                  code: 'customerVolteOption',
                  value: '1',
                  optional: true,
                  editable: false,
                  name: 'VoLTE',
                  min: 0,
                  max: 0,
                  values: [],
                },
                callHold: {
                  code: 'callHold',
                  value: 'FALSE',
                  optional: true,
                  editable: false,
                  name: 'Call Hold',
                  min: 0,
                  max: 0,
                  values: [],
                },
                isRedirectOutgoingCallsCustomerService: {
                  code: 'isRedirectOutgoingCallsCustomerService',
                  value: '0',
                  optional: true,
                  editable: false,
                  name: 'Mark Line as Stolen Device',
                  min: 0,
                  max: 0,
                  values: [],
                },
                outgoingCallBarring: {
                  code: 'outgoingCallBarring',
                  value: '0',
                  optional: true,
                  editable: false,
                  name: 'outgoingCallBarring',
                  min: 0,
                  max: 0,
                  values: [],
                },
                roamingCallBarring: {
                  code: 'roamingCallBarring',
                  value: '0',
                  optional: true,
                  editable: false,
                  name: 'roamingCallBarring',
                  min: 0,
                  max: 0,
                  values: [],
                },
                msisdnReservationExpirationDate: {
                  code: 'msisdnReservationExpirationDate',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'MSISDN Reservation Expiration Date ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                msisdnReservationId: {
                  code: 'msisdnReservationId',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'MSISDN Reservation ID ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                productActivationOrderId: {
                  code: 'productActivationOrderId',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'productActivationOrderId',
                  min: 0,
                  max: 0,
                  values: [],
                },
              },
            },
          ],
          smsNetworkService: [
            {
              customerOrderItemId: -20,
              offerId: 400131,
              offerName: 'SMS Pay As You Go',
              quantity: 1,
              createdDate: 1737980657363,
              removable: true,
              bundle: false,
              actionCode: 'ACTIVATION',
              familyCategoryCode: 'mobile',
              familyCategoryName: 'Mobile',
              productType: 'resourceFacingService',
              chars: {
                isHiddenInInvoice: {
                  code: 'isHiddenInInvoice',
                  value: '1',
                  optional: true,
                  editable: false,
                  name: 'Hidden in Invoice',
                  min: 0,
                  max: 0,
                  values: [],
                },
                incomingSms: {
                  code: 'incomingSms',
                  value: 'FALSE',
                  optional: true,
                  editable: false,
                  name: 'incomingSms',
                  min: 0,
                  max: 0,
                  values: [],
                },
                outgoingSms: {
                  code: 'outgoingSms',
                  value: 'FALSE',
                  optional: true,
                  editable: false,
                  name: 'outgoingSms',
                  min: 0,
                  max: 0,
                  values: [],
                },
                msisdnReservationExpirationDate: {
                  code: 'msisdnReservationExpirationDate',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'MSISDN Reservation Expiration Date ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                msisdnReservationId: {
                  code: 'msisdnReservationId',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'MSISDN Reservation ID ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                productActivationOrderId: {
                  code: 'productActivationOrderId',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'productActivationOrderId',
                  min: 0,
                  max: 0,
                  values: [],
                },
              },
            },
          ],
          line: [
            {
              customerOrderItemId: -21,
              offerId: 400129,
              offerName: 'Line',
              quantity: 1,
              createdDate: 1737980657365,
              removable: true,
              bundle: false,
              actionCode: 'ACTIVATION',
              familyCategoryCode: 'mobile',
              familyCategoryName: 'Mobile',
              productType: 'subscription',
              chars: {
                endUserEmail: {
                  code: 'endUserEmail',
                  value: 'EMPTY',
                  optional: true,
                  editable: false,
                  name: 'End User E-mail',
                  min: 0,
                  max: 0,
                  values: [],
                },
                endUserSurname: {
                  code: 'endUserSurname',
                  value: 'EMPTY',
                  optional: true,
                  editable: false,
                  name: 'End User Surname',
                  min: 0,
                  max: 0,
                  values: [],
                },
                endUserName: {
                  code: 'endUserName',
                  value: 'EMPTY',
                  optional: true,
                  editable: false,
                  name: 'End User Name',
                  min: 0,
                  max: 0,
                  values: [],
                },
                msisdnReservationExpirationDate: {
                  code: 'msisdnReservationExpirationDate',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'MSISDN Reservation Expiration Date ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                msisdnReservationId: {
                  code: 'msisdnReservationId',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'MSISDN Reservation ID ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                productActivationOrderId: {
                  code: 'productActivationOrderId',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'productActivationOrderId',
                  min: 0,
                  max: 0,
                  values: [],
                },
              },
            },
          ],
          dataNetworkService: [
            {
              customerOrderItemId: -22,
              offerId: 400125,
              offerName: 'Data Network Offer',
              quantity: 1,
              createdDate: 1737980657367,
              removable: true,
              bundle: false,
              actionCode: 'ACTIVATION',
              familyCategoryCode: 'mobile',
              familyCategoryName: 'Mobile',
              productType: 'resourceFacingService',
              chars: {
                isHiddenInInvoice: {
                  code: 'isHiddenInInvoice',
                  value: '1',
                  optional: true,
                  editable: false,
                  name: 'Hidden in Invoice',
                  min: 0,
                  max: 0,
                  values: [],
                },
                odbGprsBarring: {
                  code: 'odbGprsBarring',
                  value: '0',
                  optional: true,
                  editable: false,
                  name: 'odbGprsBarring',
                  min: 0,
                  max: 0,
                  values: [],
                },
                msisdnReservationExpirationDate: {
                  code: 'msisdnReservationExpirationDate',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'MSISDN Reservation Expiration Date ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                msisdnReservationId: {
                  code: 'msisdnReservationId',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'MSISDN Reservation ID ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                productActivationOrderId: {
                  code: 'productActivationOrderId',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'productActivationOrderId',
                  min: 0,
                  max: 0,
                  values: [],
                },
              },
            },
          ],
          msisdn: [
            {
              customerOrderItemId: -23,
              offerId: 400126,
              offerName: 'Mobile Phone Number',
              quantity: 1,
              createdDate: 1737980657369,
              removable: true,
              bundle: false,
              actionCode: 'ACTIVATION',
              familyCategoryCode: 'logicalNumber',
              familyCategoryName: 'logica Number',
              productType: 'subscriptionIdentity',
              chars: {
                npa: {
                  code: 'npa',
                  value: 'EMPTY',
                  optional: true,
                  editable: false,
                  name: 'npa',
                  min: 0,
                  max: 0,
                  values: [],
                },
                isBlockOutgoingPremium: {
                  code: 'isBlockOutgoingPremium',
                  value: '0',
                  optional: true,
                  editable: false,
                  name: 'Outgoing Premium',
                  min: 0,
                  max: 0,
                  values: [],
                },
                isRedirectOutgoingCallsSecurity: {
                  code: 'isRedirectOutgoingCallsSecurity',
                  value: '0',
                  optional: true,
                  editable: false,
                  name: 'Line with Suspection of Fraud',
                  min: 0,
                  max: 0,
                  values: [],
                },
                externalResourceId: {
                  code: 'externalResourceId',
                  value: 'EMPTY',
                  optional: true,
                  editable: false,
                  name: 'externalResourceId',
                  min: 0,
                  max: 0,
                  values: [],
                },
                isHiddenInInvoice: {
                  code: 'isHiddenInInvoice',
                  value: '1',
                  optional: true,
                  editable: false,
                  name: 'Hidden in Invoice',
                  min: 0,
                  max: 0,
                  values: [],
                },
                msisdn: {
                  code: 'msisdn',
                  value: 'EMPTY',
                  optional: true,
                  editable: false,
                  name: 'Mobile Number',
                  min: 0,
                  max: 0,
                  values: [],
                },
                msisdnReservationExpirationDate: {
                  code: 'msisdnReservationExpirationDate',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'MSISDN Reservation Expiration Date ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                msisdnReservationId: {
                  code: 'msisdnReservationId',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'MSISDN Reservation ID ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                isExplicitMsisdnSelected: {
                  code: 'isExplicitMsisdnSelected',
                  value: '1',
                  optional: false,
                  editable: false,
                  name: 'Explicit MSISDN Selected',
                  min: 0,
                  max: 0,
                  values: [],
                },
                productActivationOrderId: {
                  code: 'productActivationOrderId',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'productActivationOrderId',
                  min: 0,
                  max: 0,
                  values: [],
                },
              },
            },
          ],
          mobileDevice: [
            {
              customerOrderItemId: -24,
              offerId: 10013,
              offerName: 'Apple iPhone XR 128 GB',
              quantity: 1,
              createdDate: 1737980657407,
              removable: true,
              bundle: false,
              actionCode: 'ACTIVATION',
              familyCategoryCode: 'mobile',
              familyCategoryName: 'Mobile',
              productType: 'device',
              chars: {
                capacity: {
                  code: 'capacity',
                  value: '128Gb',
                  optional: true,
                  editable: false,
                  name: 'Capacity',
                  min: 0,
                  max: 0,
                  values: [],
                },
                color: {
                  code: 'color',
                  value: 'black',
                  optional: true,
                  editable: false,
                  name: 'Color',
                  min: 0,
                  max: 0,
                  values: [],
                },
                installmentOptions: {
                  code: 'installmentOptions',
                  value: '1',
                  optional: true,
                  editable: false,
                  name: 'Installment Options',
                  min: 0,
                  max: 0,
                  values: [],
                },
                imei2: {
                  code: 'imei2',
                  value: 'EMPTY',
                  optional: true,
                  editable: false,
                  name: 'imei2',
                  min: 0,
                  max: 0,
                  values: [],
                },
                isESimCompatible: {
                  code: 'isESimCompatible',
                  value: 'TRUE',
                  optional: true,
                  editable: false,
                  name: 'isESimCompatible',
                  min: 0,
                  max: 0,
                  values: [],
                },
                isPhysicalSimCompatible: {
                  code: 'isPhysicalSimCompatible',
                  value: 'TRUE',
                  optional: true,
                  editable: false,
                  name: 'isPhysicalSimCompatible',
                  min: 0,
                  max: 0,
                  values: [],
                },
                model: {
                  code: 'model',
                  value: 'iPhone XR',
                  optional: true,
                  editable: false,
                  name: 'Model',
                  min: 0,
                  max: 0,
                  values: [],
                },
                deviceType: {
                  code: 'deviceType',
                  value: 'Mobile Phone',
                  optional: true,
                  editable: false,
                  name: 'Equipment Type',
                  min: 0,
                  max: 0,
                  values: [],
                },
                mobileBrand: {
                  code: 'mobileBrand',
                  value: 'Apple',
                  optional: true,
                  editable: false,
                  name: 'Mobile Brand',
                  min: 0,
                  max: 0,
                  values: [],
                },
                display: {
                  code: 'display',
                  value: 'Liquid Retina HD display',
                  optional: true,
                  editable: false,
                  name: 'Display',
                  min: 0,
                  max: 0,
                  values: [],
                },
                camera: {
                  code: 'camera',
                  value: 'Single 12MP Wide camera',
                  optional: true,
                  editable: false,
                  name: 'Camera',
                  min: 0,
                  max: 0,
                  values: [],
                },
                processor: {
                  code: 'processor',
                  value: 'A12 Bionic chip',
                  optional: true,
                  editable: false,
                  name: 'Processor',
                  min: 0,
                  max: 0,
                  values: [],
                },
                battery: {
                  code: 'battery',
                  value: 'Built‑in rechargeable lithium‑ion battery',
                  optional: true,
                  editable: false,
                  name: 'Battery',
                  min: 0,
                  max: 0,
                  values: [],
                },
                network: {
                  code: 'network',
                  value: 'FDD-LTE/TD-LTE/UMTS/HSPA+/DC-HSDPA/GSM/EDGE ',
                  optional: true,
                  editable: false,
                  name: 'Network',
                  min: 0,
                  max: 0,
                  values: [],
                },
                modelNumber: {
                  code: 'modelNumber',
                  value: 'EMPTY',
                  optional: true,
                  editable: false,
                  name: 'Model Number',
                  min: 0,
                  max: 0,
                  values: [],
                },
                simType: {
                  code: 'simType',
                  value: 'Dual SIM',
                  optional: true,
                  editable: false,
                  name: 'SIM Type',
                  min: 0,
                  max: 0,
                  values: [],
                },
                width: {
                  code: 'width',
                  value: '75.7 mm (2.98 inches)',
                  optional: true,
                  editable: false,
                  name: 'Width',
                  min: 0,
                  max: 0,
                  values: [],
                },
                height: {
                  code: 'height',
                  value: '150.9 mm (5.94 inches)',
                  optional: true,
                  editable: false,
                  name: 'Height ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                depth: {
                  code: 'depth',
                  value: '8.3 mm (0.33 inch)',
                  optional: true,
                  editable: false,
                  name: 'Depth',
                  min: 0,
                  max: 0,
                  values: [],
                },
                weight: {
                  code: 'weight',
                  value: '194 grams (6.84 ounces)',
                  optional: true,
                  editable: false,
                  name: 'Weight ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                sizeAndType: {
                  code: 'sizeAndType',
                  value: '6.1‑inch Liquid Retina HD display',
                  optional: true,
                  editable: false,
                  name: 'Size and type',
                  min: 0,
                  max: 0,
                  values: [],
                },
                resolution: {
                  code: 'resolution',
                  value: '1792 x 828 pixel',
                  optional: true,
                  editable: false,
                  name: 'Resolution',
                  min: 0,
                  max: 0,
                  values: [],
                },
                pixelsPerInch: {
                  code: 'pixelsPerInch',
                  value: '326 ppi',
                  optional: true,
                  editable: false,
                  name: 'Pixels Per Inch',
                  min: 0,
                  max: 0,
                  values: [],
                },
                brightness: {
                  code: 'brightness',
                  value: '625 nits',
                  optional: true,
                  editable: false,
                  name: 'Brightness',
                  min: 0,
                  max: 0,
                  values: [],
                },
                colorGamut: {
                  code: 'colorGamut',
                  value: 'EMPTY',
                  optional: true,
                  editable: false,
                  name: 'Color Gamut',
                  min: 0,
                  max: 0,
                  values: [],
                },
                technology: {
                  code: 'technology',
                  value: 'HD',
                  optional: true,
                  editable: false,
                  name: 'Technology',
                  min: 0,
                  max: 0,
                  values: [],
                },
                contrastRatio: {
                  code: 'contrastRatio',
                  value: '58.3340277777778',
                  optional: true,
                  editable: false,
                  name: 'contrast ratio',
                  min: 0,
                  max: 0,
                  values: [],
                },
                displayBullets: {
                  code: 'displayBullets',
                  value:
                    '|True Tone|Wide colour (P3)|Haptic Touch|Fingerprint-resistant oleophobic coating|Support for display of multiple languages and characters simultaneously',
                  optional: true,
                  editable: false,
                  name: 'Display Bullets',
                  min: 0,
                  max: 0,
                  values: [],
                },
                operatingSystem: {
                  code: 'operatingSystem',
                  value: 'iOS 14',
                  optional: true,
                  editable: false,
                  name: 'Operating System',
                  min: 0,
                  max: 0,
                  values: [],
                },
                cpuProcess: {
                  code: 'cpuProcess',
                  value: 'EMPTY',
                  optional: true,
                  editable: false,
                  name: 'CPU Process',
                  min: 0,
                  max: 0,
                  values: [],
                },
                gpu: {
                  code: 'gpu',
                  value: 'Apple GPU (4-core graphics)',
                  optional: true,
                  editable: false,
                  name: 'GPU',
                  min: 0,
                  max: 0,
                  values: [],
                },
                cpuCore: {
                  code: 'cpuCore',
                  value: 'Hexa-core (16)',
                  optional: true,
                  editable: false,
                  name: 'CPU Core',
                  min: 0,
                  max: 0,
                  values: [],
                },
                chipset: {
                  code: 'chipset',
                  value: 'A12 Bionic chip',
                  optional: true,
                  editable: false,
                  name: 'Chipset',
                  min: 0,
                  max: 0,
                  values: [],
                },
                mainCameraBullets: {
                  code: 'mainCameraBullets',
                  value: '1',
                  optional: true,
                  editable: false,
                  name: 'Main Camera Bullets',
                  min: 0,
                  max: 0,
                  values: [],
                },
                frontCameraBullets: {
                  code: 'frontCameraBullets',
                  value: '1',
                  optional: true,
                  editable: false,
                  name: 'Front Camera Bullets',
                  min: 0,
                  max: 0,
                  values: [],
                },
                tripleRearCameraBullets: {
                  code: 'tripleRearCameraBullets',
                  value: 'EMPTY',
                  optional: true,
                  editable: false,
                  name: 'Triple Rear Camera Bullets',
                  min: 0,
                  max: 0,
                  values: [],
                },
                rearCameraBullets: {
                  code: 'rearCameraBullets',
                  value: 'EMPTY',
                  optional: true,
                  editable: false,
                  name: 'Rear Camera Bullets',
                  min: 0,
                  max: 0,
                  values: [],
                },
                videoRecordingBullets: {
                  code: 'videoRecordingBullets',
                  value: '1',
                  optional: true,
                  editable: false,
                  name: 'Video Recording Bullets',
                  min: 0,
                  max: 0,
                  values: [],
                },
                ram: {
                  code: 'ram',
                  value: '3 GB',
                  optional: true,
                  editable: false,
                  name: 'RAM',
                  min: 0,
                  max: 0,
                  values: [],
                },
                microSDCardSlot: {
                  code: 'microSDCardSlot',
                  value: '0',
                  optional: true,
                  editable: false,
                  name: 'MicroSD card slot',
                  min: 0,
                  max: 0,
                  values: [],
                },
                cableType: {
                  code: 'cableType',
                  value: 'USB‑C to Lightning Cable',
                  optional: true,
                  editable: false,
                  name: 'Cable type',
                  min: 0,
                  max: 0,
                  values: [],
                },
                wifi: {
                  code: 'wifi',
                  value: 'Wi‑Fi 6 (802.11ax) with 2x2 MIMO',
                  optional: true,
                  editable: false,
                  name: 'WiFi',
                  min: 0,
                  max: 0,
                  values: [],
                },
                bluetooth: {
                  code: 'bluetooth',
                  value: 'Bluetooth 5.0',
                  optional: true,
                  editable: false,
                  name: 'Bluetooth',
                  min: 0,
                  max: 0,
                  values: [],
                },
                gps: {
                  code: 'gps',
                  value: 'Built‑in GPS, GLONASS, Galileo, QZSS and BeiDou',
                  optional: true,
                  editable: false,
                  name: 'GPS ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                sensors: {
                  code: 'sensors',
                  value: '|Barometer|Three‑axis gyro|Accelerometer|Proximity sensor|Ambient light sensor',
                  optional: true,
                  editable: false,
                  name: 'Sensors',
                  min: 0,
                  max: 0,
                  values: [],
                },
                'talkTime(Wireless)': {
                  code: 'talkTime(Wireless)',
                  value: 'EMPTY',
                  optional: true,
                  editable: false,
                  name: 'Talk Time (Wireless)',
                  min: 0,
                  max: 0,
                  values: [],
                },
                internetUse: {
                  code: 'internetUse',
                  value: 'EMPTY',
                  optional: true,
                  editable: false,
                  name: 'Internet use',
                  min: 0,
                  max: 0,
                  values: [],
                },
                'videoPlayback(Wireless)': {
                  code: 'videoPlayback(Wireless)',
                  value: 'Up to 16 hours',
                  optional: true,
                  editable: false,
                  name: 'Video playback (wireless)',
                  min: 0,
                  max: 0,
                  values: [],
                },
                batteryBullets: {
                  code: 'batteryBullets',
                  value:
                    '|Built-in rechargeable lithium‑ion battery|Charging via USB to computer system or power adapter|Fast-charge capable|Up to 50% charge in around 30 minutes11 with 20W adapter or higher (sold separately',
                  optional: true,
                  editable: false,
                  name: 'Battery Bullets',
                  min: 0,
                  max: 0,
                  values: [],
                },
                'audioPlayback(Wireless)': {
                  code: 'audioPlayback(Wireless)',
                  value: 'Up to 65 hours',
                  optional: true,
                  editable: false,
                  name: 'Audio playback (wireless)',
                  min: 0,
                  max: 0,
                  values: [],
                },
                fastChargeCapable: {
                  code: 'fastChargeCapable',
                  value: 'Up to 50% charge in around 30 minutes11 with 20W adapter or higher (sold separately)',
                  optional: true,
                  editable: false,
                  name: 'Fast Charge Capable',
                  min: 0,
                  max: 0,
                  values: [],
                },
                features: {
                  code: 'features',
                  value: 'EMPTY',
                  optional: true,
                  editable: false,
                  name: 'Features',
                  min: 0,
                  max: 0,
                  values: [],
                },
                imei: {
                  code: 'imei',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'IMEI',
                  min: 0,
                  max: 0,
                  values: [],
                },
                serialNumber: {
                  code: 'serialNumber',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'Serial number',
                  min: 0,
                  max: 0,
                  values: [],
                },
                UPCCode: {
                  code: 'UPCCode',
                  value: '1004ipxr',
                  optional: true,
                  editable: false,
                  name: 'UPC Code',
                  min: 0,
                  max: 0,
                  values: [],
                },
                msisdnReservationExpirationDate: {
                  code: 'msisdnReservationExpirationDate',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'MSISDN Reservation Expiration Date ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                msisdnReservationId: {
                  code: 'msisdnReservationId',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'MSISDN Reservation ID ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                deliveryOrderId: {
                  code: 'deliveryOrderId',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'deliveryOrderId',
                  min: 0,
                  max: 0,
                  values: [],
                },
                isStandalonePurchase: {
                  code: 'isStandalonePurchase',
                  value: '0',
                  optional: false,
                  editable: false,
                  name: 'Is Standalone Purchase',
                  min: 0,
                  max: 0,
                  values: [],
                },
                productActivationOrderId: {
                  code: 'productActivationOrderId',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'productActivationOrderId',
                  min: 0,
                  max: 0,
                  values: [],
                },
              },
            },
          ],
          dataBucket: [
            {
              customerOrderItemId: -15,
              offerId: 11102,
              offerName: 'Prepaid Data 20 GB',
              quantity: 1,
              createdDate: 1737980657354,
              removable: true,
              bundle: false,
              actionCode: 'ACTIVATION',
              familyCategoryCode: 'mobile',
              familyCategoryName: 'Mobile',
              productType: 'subOffer',
              chars: {
                dataAmount: {
                  code: 'dataAmount',
                  value: '20',
                  optional: true,
                  editable: false,
                  name: 'dataAmount',
                  min: 0,
                  max: 0,
                  values: [],
                },
                bucketType: {
                  code: 'bucketType',
                  value: 'dataPP',
                  optional: true,
                  editable: false,
                  name: 'Bucket Type',
                  min: 0,
                  max: 0,
                  values: [],
                },
                freeUnitType: {
                  code: 'freeUnitType',
                  value: 'lineLevel',
                  optional: true,
                  editable: false,
                  name: 'freeUnitType',
                  min: 0,
                  max: 0,
                  values: [],
                },
                msisdnReservationExpirationDate: {
                  code: 'msisdnReservationExpirationDate',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'MSISDN Reservation Expiration Date ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                msisdnReservationId: {
                  code: 'msisdnReservationId',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'MSISDN Reservation ID ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                productActivationOrderId: {
                  code: 'productActivationOrderId',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'productActivationOrderId',
                  min: 0,
                  max: 0,
                  values: [],
                },
              },
            },
          ],
          mobilePlan: [
            {
              customerOrderItemId: -14,
              offerId: 11002,
              offerName: 'All Inclusive 20 GB',
              quantity: 1,
              createdDate: 1737980657354,
              removable: true,
              bundle: false,
              actionCode: 'ACTIVATION',
              familyCategoryCode: 'mobile',
              familyCategoryName: 'Mobile',
              productType: 'plan',
              chars: {
                genMarketingText: {
                  code: 'genMarketingText',
                  value: '30daysOfSatisfactionGuarantee',
                  optional: true,
                  editable: false,
                  name: 'General Marketing Text',
                  min: 0,
                  max: 0,
                  values: [],
                },
                callMarketingText: {
                  code: 'callMarketingText',
                  value: 'PPUNationWideMinutes',
                  optional: true,
                  editable: false,
                  name: 'Voice Marketing Text',
                  min: 0,
                  max: 0,
                  values: [],
                },
                hierType: {
                  code: 'hierType',
                  value: 'Line',
                  optional: true,
                  editable: false,
                  name: 'hierType',
                  min: 0,
                  max: 0,
                  values: [],
                },
                isMainPlan: {
                  code: 'isMainPlan',
                  value: '1',
                  optional: true,
                  editable: false,
                  name: 'Main Plan Indicator',
                  min: 0,
                  max: 0,
                  values: [],
                },
                isHiddenInInvoice: {
                  code: 'isHiddenInInvoice',
                  value: '1',
                  optional: true,
                  editable: false,
                  name: 'Hidden in Invoice',
                  min: 0,
                  max: 0,
                  values: [],
                },
                msisdnReservationExpirationDate: {
                  code: 'msisdnReservationExpirationDate',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'MSISDN Reservation Expiration Date ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                msisdnReservationId: {
                  code: 'msisdnReservationId',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'MSISDN Reservation ID ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                productActivationOrderId: {
                  code: 'productActivationOrderId',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'productActivationOrderId',
                  min: 0,
                  max: 0,
                  values: [],
                },
              },
            },
          ],
          smsBucket: [
            {
              customerOrderItemId: -17,
              offerId: 11110,
              offerName: 'SMS Unlimited',
              quantity: 1,
              createdDate: 1737980657356,
              removable: true,
              bundle: false,
              actionCode: 'ACTIVATION',
              familyCategoryCode: 'mobile',
              familyCategoryName: 'Mobile',
              productType: 'subOffer',
              chars: {
                smsAmount: {
                  code: 'smsAmount',
                  value: 'unlimited',
                  optional: true,
                  editable: false,
                  name: 'smsAmount',
                  min: 0,
                  max: 0,
                  values: [],
                },
                isHiddenInInvoice: {
                  code: 'isHiddenInInvoice',
                  value: '1',
                  optional: true,
                  editable: false,
                  name: 'Hidden in Invoice',
                  min: 0,
                  max: 0,
                  values: [],
                },
                freeUnitType: {
                  code: 'freeUnitType',
                  value: 'lineLevel',
                  optional: true,
                  editable: false,
                  name: 'freeUnitType',
                  min: 0,
                  max: 0,
                  values: [],
                },
                bucketType: {
                  code: 'bucketType',
                  value: 'SMS/MMS',
                  optional: true,
                  editable: false,
                  name: 'Bucket Type',
                  min: 0,
                  max: 0,
                  values: [],
                },
                msisdnReservationExpirationDate: {
                  code: 'msisdnReservationExpirationDate',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'MSISDN Reservation Expiration Date ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                msisdnReservationId: {
                  code: 'msisdnReservationId',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'MSISDN Reservation ID ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                productActivationOrderId: {
                  code: 'productActivationOrderId',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'productActivationOrderId',
                  min: 0,
                  max: 0,
                  values: [],
                },
              },
            },
          ],
          voiceBucket: [
            {
              customerOrderItemId: -16,
              offerId: 11108,
              offerName: 'Prepaid Voice 250 Minutes',
              quantity: 1,
              createdDate: 1737980657356,
              removable: true,
              bundle: false,
              actionCode: 'ACTIVATION',
              familyCategoryCode: 'mobile',
              familyCategoryName: 'Mobile',
              productType: 'subOffer',
              chars: {
                voiceAmount: {
                  code: 'voiceAmount',
                  value: '250',
                  optional: true,
                  editable: false,
                  name: 'voiceAmount',
                  min: 0,
                  max: 0,
                  values: [],
                },
                bucketType: {
                  code: 'bucketType',
                  value: 'voice',
                  optional: true,
                  editable: false,
                  name: 'Bucket Type',
                  min: 0,
                  max: 0,
                  values: [],
                },
                isHiddenInInvoice: {
                  code: 'isHiddenInInvoice',
                  value: '1',
                  optional: true,
                  editable: false,
                  name: 'Hidden in Invoice',
                  min: 0,
                  max: 0,
                  values: [],
                },
                freeUnitType: {
                  code: 'freeUnitType',
                  value: 'lineLevel',
                  optional: true,
                  editable: false,
                  name: 'freeUnitType',
                  min: 0,
                  max: 0,
                  values: [],
                },
                msisdnReservationExpirationDate: {
                  code: 'msisdnReservationExpirationDate',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'MSISDN Reservation Expiration Date ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                msisdnReservationId: {
                  code: 'msisdnReservationId',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'MSISDN Reservation ID ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                productActivationOrderId: {
                  code: 'productActivationOrderId',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'productActivationOrderId',
                  min: 0,
                  max: 0,
                  values: [],
                },
              },
            },
          ],
          coverage: [
            {
              customerOrderItemId: -18,
              offerId: 400124,
              offerName: 'Coverage',
              quantity: 1,
              createdDate: 1737980657357,
              removable: true,
              bundle: false,
              actionCode: 'ACTIVATION',
              familyCategoryCode: 'coverage',
              familyCategoryName: 'Coverage',
              productType: 'subOffer',
              chars: {
                coverage: {
                  code: 'coverage',
                  value: 'Canada',
                  optional: true,
                  editable: false,
                  name: 'coverage',
                  min: 0,
                  max: 0,
                  values: [],
                },
                isHiddenInInvoice: {
                  code: 'isHiddenInInvoice',
                  value: '1',
                  optional: true,
                  editable: false,
                  name: 'Hidden in Invoice',
                  min: 0,
                  max: 0,
                  values: [],
                },
                msisdnReservationExpirationDate: {
                  code: 'msisdnReservationExpirationDate',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'MSISDN Reservation Expiration Date ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                msisdnReservationId: {
                  code: 'msisdnReservationId',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'MSISDN Reservation ID ',
                  min: 0,
                  max: 0,
                  values: [],
                },
                productActivationOrderId: {
                  code: 'productActivationOrderId',
                  value: 'EMPTY',
                  optional: false,
                  editable: false,
                  name: 'productActivationOrderId',
                  min: 0,
                  max: 0,
                  values: [],
                },
              },
            },
          ],
        },
      },
    ],
  },
} as unknown as AbstractQuote;
