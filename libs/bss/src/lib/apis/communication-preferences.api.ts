import { inject, Injectable } from '@angular/core';
import { RestService } from '@libs/core';
import { CapturedPartyPrivacy, CommunicationPreference, EndpointKey } from '@libs/types';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class CommunicationPreferencesApi extends RestService {
  private restService = inject(RestService);

  getCustomerContactMediumConsent$(
    desc: string,
    custId: number,
    mediumType: string,
  ): Observable<CapturedPartyPrivacy.Result> {
    return this.restService.get(`/consent/${custId}/contactMedium`, {
      queryParams: { mediumType },
      desc,
      service: EndpointKey.CRM,
    });
  }

  getCommunicationPreferences$(
    desc: string,
    body: CommunicationPreference.PartyPrivacySearchRequestForCrud,
  ): Observable<CapturedPartyPrivacy.Result> {
    return this.restService.post('/consent/contactMedium', body, {
      desc,
      service: EndpointKey.CRM,
    });
  }
}
