import { inject, Injectable } from '@angular/core';
import { RestService } from '@libs/core';
import { EndpointKey, Terms } from '@libs/types';

@Injectable({
  providedIn: 'root',
})
export class TermApi extends RestService {
  private restService = inject(RestService);

  offerTermsFromKeyProductOffer$(desc: string, payload: Terms.OfferTermsRequest) {
    return this.restService.post<Terms.OfferTermsRequest, Terms.Clause[]>(
      '/public/product-offer/inquireOfferTermsFromKeyProductOffer',
      payload,
      {
        desc,
        autoErrorHandling: false,
        service: EndpointKey.PCM,
      },
    );
  }
}
