import { inject, Injectable } from '@angular/core';
import { RestService } from '@libs/core';
import { Observable } from 'rxjs';
import {
  AbstractQuote,
  DeliverInstallationRetrieveConfigResponse,
  eCommon,
  GenericResponse,
  InquireDeliverMethodRequest,
  KeyQuote,
  OperationResult,
  OrderSubmitRequest,
  QuoteNextStateRequest,
  QuotePrice,
  QuoteQueryRequest,
  QuoteRequest,
  UpdateQuoteRequest,
  ValidateSimCard,
} from '@libs/types';
import EndpointKey = eCommon.EndpointKey;

@Injectable({
  providedIn: 'root',
})
export class QuoteApi {
  private restService = inject(RestService);

  priceDetail$(desc: string, payload: QuotePrice.PriceDetailRequest) {
    return this.restService.get<QuotePrice.QuotePriceDetail>('/quote/priceDetail', {
      queryParams: {
        ...payload,
      },
      service: EndpointKey.CPQ,
      desc,
      autoErrorHandling: false,
      withCache: true,
    });
  }

  getQuote$(desc: string, payload: QuoteRequest): Observable<AbstractQuote> {
    // return of(MOCK_GET_QOUTE);
    return this.restService.post('/quote', payload, {
      desc,
      autoErrorHandling: false,
      service: EndpointKey.CPQ,
    });
  }

  updateQuote$(desc: string, payload: UpdateQuoteRequest): Observable<KeyQuote> {
    return this.restService.put<UpdateQuoteRequest, KeyQuote>('/quote/update', payload, {
      service: EndpointKey.CPQ,
      desc,
    });
  }

  nextState$(desc: string, payload: QuoteNextStateRequest): Observable<KeyQuote> {
    return this.restService.post<QuoteNextStateRequest, KeyQuote>('/quote/nextState', payload, {
      service: EndpointKey.CPQ,
      desc,
      autoErrorHandling: false,
    });
  }

  removeQuote$(desc: string, payload: UpdateQuoteRequest): Observable<KeyQuote> {
    return this.restService.put<UpdateQuoteRequest, KeyQuote>('/public/quote/remove', payload, {
      desc,
      service: EndpointKey.CPQ,
      autoErrorHandling: false,
    });
  }

  submitQuote$(desc: string, payload: OrderSubmitRequest): Observable<KeyQuote> {
    return this.restService.post<OrderSubmitRequest, KeyQuote>('/quote/submit', payload, {
      service: EndpointKey.CPQ,
      desc,
    });
  }

  getDeliverInstallationRetrieveConfig$(
    desc: string,
    payload: InquireDeliverMethodRequest,
  ): Observable<DeliverInstallationRetrieveConfigResponse[]> {
    return this.restService.post<InquireDeliverMethodRequest, DeliverInstallationRetrieveConfigResponse[]>(
      '/deliverInstallationRetrieve/deliverMethod',
      payload,
      {
        service: EndpointKey.CPQ,
        desc,
        autoErrorHandling: false,
      },
    );
  }

  validateSimCard$(desc: string, payload: ValidateSimCard.Request) {
    return this.restService.post<ValidateSimCard.Request, GenericResponse<ValidateSimCard.Response>>(
      '/resourceQualification/validate',
      payload,
      {
        service: EndpointKey.CPQ,
        desc,
        autoErrorHandling: false,
        withCache: true,
      },
    );
  }

  getQuoteQuery$(desc: string, payload: QuoteQueryRequest): Observable<unknown> {
    return this.restService.post('/public/quote/query', payload, {
      desc,
      autoErrorHandling: false,
      service: EndpointKey.CPQ,
    });
  }

  clearQuote$(desc: string, customerOrderId: number): Observable<KeyQuote> {
    return this.restService.put('/public/quote/clear', undefined, {
      desc,
      service: EndpointKey.CPQ,
      autoErrorHandling: false,
      queryParams: {
        customerOrderId,
      },
    });
  }

  cancelQuote$(desc: string, payload: KeyQuote): Observable<OperationResult> {
    return this.restService.post('/public/quote/cancel', payload, {
      desc,
      service: EndpointKey.CPQ,
    });
  }
}
