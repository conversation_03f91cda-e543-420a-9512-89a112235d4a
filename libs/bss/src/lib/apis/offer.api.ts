import { inject, Injectable } from '@angular/core';
import { RestService } from '@libs/core';
import { Observable } from 'rxjs';
import {
  ByodResources,
  EndpointKey,
  ExpoGroup,
  InquireByodResourcesRequest,
  InquireExpoGroupsRequest,
  ProductOfferDetail,
  ProductOfferingQualification,
  ProductOfferingQualificationRequest,
} from '@libs/types';

@Injectable({
  providedIn: 'root',
})
export class OfferApi {
  private restService = inject(RestService);

  getExpoGroups$(desc: string, payload: InquireExpoGroupsRequest): Observable<ExpoGroup[]> {
    return this.restService.post<InquireExpoGroupsRequest, ExpoGroup[]>('/public/expo-group/inquire', payload, {
      desc,
      autoErrorHandling: false,
      service: EndpointKey.PCM,
    });
  }

  productOfferingQualification$(desc: string, payload: ProductOfferingQualificationRequest) {
    return this.restService.post<ProductOfferingQualificationRequest, ProductOfferingQualification>(
      '/public/product-offer/qualification',
      payload,
      {
        desc,
        autoErrorHandling: false,
        service: EndpointKey.PCM,
      },
    );
  }

  getOfferCatalog$(desc: string, shortCode: string) {
    return this.restService.get<ProductOfferDetail[]>(`/public/product-offer/catalog/${shortCode}`, {
      desc,
      autoErrorHandling: false,
      service: EndpointKey.PCM,
    });
  }

  getByodResources$(desc: string, payload: InquireByodResourcesRequest) {
    return this.restService.post<InquireByodResourcesRequest, ByodResources[]>(
      '/public/product-offer/inquireByodResources',
      payload,
      {
        desc,
        autoErrorHandling: false,
        service: EndpointKey.PCM,
      },
    );
  }
}
