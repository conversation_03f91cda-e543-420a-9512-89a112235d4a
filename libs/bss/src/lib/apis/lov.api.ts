import { RestService } from '@libs/core';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { DeliveryMethod, EndpointKey, GeographyPlace, Lov, PlaceType } from '@libs/types';

@Injectable({
  providedIn: 'root',
})
export class Lov<PERSON><PERSON> extends RestService {
  private restService = inject(RestService);

  getDeliveryMethods$(desc: string): Observable<DeliveryMethod[]> {
    return this.restService.get(`/public/delivery`, {
      desc,
      autoErrorHandling: false,
      service: EndpointKey.PCM,
    });
  }

  getLanguageConfig$(desc: string, payload: Lov.CriteriaRequest): Observable<Lov.LovLanguageResponse> {
    return this.restService.post('/public/language/criteria', payload, {
      desc,
      autoErrorHandling: false,
      service: EndpointKey.DOMAIN_CONFIG,
    });
  }

  getCountryTypesConfig$(desc: string, payload: Lov.CriteriaRequest): Observable<Lov.LovCountryResponse> {
    return this.restService.post('/public/country/criteria', payload, {
      desc,
      autoErrorHandling: false,
      service: EndpointKey.DOMAIN_CONFIG,
    });
  }

  getStateCriteria$(desc: string, payload: Lov.CriteriaRequest): Observable<Lov.LovStateResponse> {
    return this.restService.post('/public/state/criteria', payload, {
      desc,
      autoErrorHandling: false,
      service: EndpointKey.DOMAIN_CONFIG,
    });
  }

  getCityCriteria$(desc: string, payload: Lov.CriteriaRequest): Observable<Lov.LovCityResponse> {
    return this.restService.post('/public/city/criteria', payload, {
      desc,
      autoErrorHandling: false,
      service: EndpointKey.DOMAIN_CONFIG,
    });
  }

  getContactMediumTypeConfig$(desc: string, payload: Lov.CriteriaRequest): Observable<Lov.LovContactMediumResponse> {
    return this.restService.post('/public/contactMediumType/criteria', payload, {
      desc,
      autoErrorHandling: false,
      service: EndpointKey.DOMAIN_CONFIG,
    });
  }

  getNpa$(desc: string): Observable<Lov.LovNpaXXResponse> {
    return this.restService.get(`/npaNxx/inquireNpa`, {
      desc,
      autoErrorHandling: false,
      service: EndpointKey.DOMAIN_CONFIG,
    });
  }

  getNXXByNpa$(desc: string, npa: string): Observable<Lov.LovNpaXXResponse> {
    return this.restService.get(`/npaNxx/inquireNxx/${npa}`, {
      desc,
      autoErrorHandling: false,
      service: EndpointKey.DOMAIN_CONFIG,
    });
  }

  searchMsisdn$(desc: string, payload: Lov.LovSearchMsisdnRequest): Observable<Lov.LovSearchMsisdnResponse> {
    return this.restService.post('/utility/lov/searchMsisdn', payload, {
      desc,
      service: EndpointKey.CPQ,
      autoErrorHandling: false,
    });
  }

  getGeographyPlaces$(desc: string, shortCode: PlaceType): Observable<GeographyPlace[]> {
    return this.restService.get(`/public/geographyPlace/placeType/shortCode/${shortCode}`, {
      desc,
      autoErrorHandling: false,
      service: EndpointKey.DOMAIN_CONFIG,
    });
  }

  getOrderTypes$(desc: string): Observable<Lov.LovResult[]> {
    return this.restService.get('/businessflowspec/listOrderType', {
      desc,
      autoErrorHandling: false,
      service: EndpointKey.DOMAIN_CONFIG,
    });
  }

  getOrderStatuses$(desc: string): Observable<Lov.LovResult[]> {
    return this.restService.get('/orderStatus/listOrderStatus', {
      desc,
      autoErrorHandling: false,
      service: EndpointKey.DOMAIN_CONFIG,
    });
  }
}
