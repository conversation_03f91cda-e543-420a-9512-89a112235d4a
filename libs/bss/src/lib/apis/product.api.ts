import { inject, Injectable } from '@angular/core';
import { RestService } from '@libs/core';
import { Observable } from 'rxjs';
import { EndpointKey, Product, ProductSearchRequest } from '@libs/types';

@Injectable({
  providedIn: 'root',
})
export class ProductApi {
  private restService = inject(RestService);

  getCustomerProducts$(desc: string, payload: ProductSearchRequest): Observable<Product.ProductResponse> {
    return this.restService.post('/products', payload, {
      desc,
      autoErrorHandling: false,
      service: EndpointKey.MASH_UP,
    });
  }
  getCustomerProductDetail$(desc: string, payload: ProductSearchRequest): Observable<Product.ProductResponse> {
    return this.restService.post('/product', payload, {
      desc,
      autoErrorHandling: false,
      service: EndpointKey.MASH_UP,
    });
  }
}
