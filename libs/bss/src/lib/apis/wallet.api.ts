import { inject, Injectable } from '@angular/core';
import { RestService } from '@libs/core';
import { Observable } from 'rxjs';
import { EndpointKey, WalletModel } from '@libs/types';

@Injectable({
  providedIn: 'root',
})
export class WalletApi {
  private restService = inject(RestService);

  getWalletBalance$(
    desc: string,
    payload: WalletModel.WalletBalanceRequest,
  ): Observable<WalletModel.GetWalletBalanceResponse[]> {
    return this.restService.get('/wallet/balance', {
      queryParams: {
        billingAccountId: payload.billingAccountId,
      },
      desc,
      autoErrorHandling: false,
      service: EndpointKey.CRM,
    });
  }
}
