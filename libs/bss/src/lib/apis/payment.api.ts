import { inject, Injectable } from '@angular/core';
import { RestService } from '@libs/core';
import { Observable } from 'rxjs';
import { eBusinessFlow, EndpointKey, OperationResult, PaymentMethod } from '@libs/types';

@Injectable({
  providedIn: 'root',
})
export class PaymentApi {
  private restService = inject(RestService);

  getCustomerPaymentMethods$(desc: string, customerId: number): Observable<PaymentMethod.PaymentMethods[]> {
    return this.restService.get(`/payment/methods/${customerId}`, {
      desc,
      autoErrorHandling: false,
      service: EndpointKey.CRM,
    });
  }

  getPaymentMethodDetails$(paymentMethodTypeId: number): Observable<PaymentMethod.PaymentMethodDetails> {
    return this.restService.get(`/paymentMethodType/details`, {
      queryParams: {
        paymentMethodTypeId,
      },
      autoErrorHandling: false,
      service: EndpointKey.DOMAIN_CONFIG,
    });
  }

  saveNewPaymentMethod$(payload: PaymentMethod.SavePaymentMethod): Observable<PaymentMethod.PaymentMethods> {
    return this.restService.post(`/payment/methods`, payload, {
      service: EndpointKey.CRM,
    });
  }

  removePaymentMethod$(paymentMethodId: number): Observable<OperationResult> {
    return this.restService.delete<OperationResult>(`/payment/methods/${paymentMethodId}`, undefined, {
      service: EndpointKey.CRM,
    });
  }

  getPaymentMethodTypes$(
    bsnInterSpecShortCode?: eBusinessFlow.Specification,
  ): Observable<PaymentMethod.PaymentMethodDetails[]> {
    return this.restService.get<PaymentMethod.PaymentMethodDetails[]>(`/payment/methods/types`, {
      queryParams: {
        bsnInterSpecShortCode,
      },
      autoErrorHandling: false,
      service: EndpointKey.CRM,
    });
  }
}
