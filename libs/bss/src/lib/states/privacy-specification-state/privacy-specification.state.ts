import { Action, Selector, State, StateContext, StateToken } from '@ngxs/store';
import { inject, Injectable } from '@angular/core';
import { PartyPrivacySpecificationData, TermsData } from '../../datas';
import { PrivacySpecificationApi } from '../../apis/privacy-specification.api';
import { PrivacySpecificationStateType } from './privacy-specification-state-type';
import {
  GetInquirePartyPrivacyDocumentAction,
  GetPartyPrivacySpecificationAction,
} from './privacy-specification.actions';
import { tap } from 'rxjs/operators';
import { Observable } from 'rxjs';
import { PrivacySpecification } from '@libs/types';

export const PrivacySpecificationStateToken = new StateToken<PrivacySpecificationStateType>(
  'PrivacySpecificationState',
);

@Injectable()
@State<PrivacySpecificationStateType>({
  name: PrivacySpecificationStateToken,
  defaults: {
    partyPrivacySpecificationInfo: new PartyPrivacySpecificationData(),
    partyPrivacyDocument: new TermsData(),
  },
})
export class PrivacySpecificationState {
  private privacySpecificationApi = inject(PrivacySpecificationApi);

  @Selector()
  static partyPrivacySpecificationInfo({
    partyPrivacySpecificationInfo,
  }: PrivacySpecificationStateType): PartyPrivacySpecificationData {
    return partyPrivacySpecificationInfo;
  }

  @Selector()
  static partyPrivacyDocument({ partyPrivacyDocument }: PrivacySpecificationStateType): TermsData {
    return partyPrivacyDocument;
  }

  @Action(GetPartyPrivacySpecificationAction)
  getPartyPrivacySpecification(
    { patchState }: StateContext<PrivacySpecificationStateType>,
    { payload }: GetPartyPrivacySpecificationAction,
  ): Observable<PrivacySpecification.PartyPrivacySpecificationResponse> {
    return this.privacySpecificationApi
      .getPartyPrivacySpecification$(GetPartyPrivacySpecificationAction.type, payload)
      .pipe(
        tap((response) => {
          patchState({ partyPrivacySpecificationInfo: new PartyPrivacySpecificationData(response) });
        }),
      );
  }

  @Action(GetInquirePartyPrivacyDocumentAction)
  inquirePartyPrivacyDocument(
    { patchState }: StateContext<PrivacySpecificationStateType>,
    { partyPrivacySpecId }: GetInquirePartyPrivacyDocumentAction,
  ) {
    return this.privacySpecificationApi
      .getPartyPrivacyDocument$(GetInquirePartyPrivacyDocumentAction.type, partyPrivacySpecId)
      .pipe(tap((response) => patchState({ partyPrivacyDocument: new TermsData(response) })));
  }
}
