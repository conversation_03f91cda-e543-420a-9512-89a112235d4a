import { Action, Selector, State, StateContext, StateToken } from '@ngxs/store';
import { inject, Injectable } from '@angular/core';
import { LovStateType } from './lov-state-type';
import {
  LovGetCityCriteriaAction,
  LovGetContactMediumTypeAction,
  LovGetCountryTypesAction,
  LovGetDeliveryMethodsAction,
  LovGetGeographyPlacesAction,
  LovGetLanguageTypeAction,
  LovGetNpaAction,
  LovGetNXXByNpaAction,
  LovGetStateCriteriaAction,
  LovSearchMsisdnAction,
  LovClearStateCriteriaAction,
  LovClearCityCriteriaAction,
  LovGetOrderTypesAction,
  LovGetOrderStatusesAction,
} from './lov.actions';
import { LovApi } from '../../apis/lov.api';
import { tap } from 'rxjs/operators';
import { buildCriteriaRequest } from '@libs/core';
import { DeliveryMethod, GeographyPlace, Lov } from '@libs/types';
import {
  DeliveryMethodData,
  GeographyPlacesData,
  LovCityCriteriaData,
  LovContactMediumTypeData,
  LovCountryTypeData,
  LovItemData,
  LovLanguageTypeData,
  LovResultData,
  LovStateCriteriaData,
  SearchResourceQualificationInfoData,
} from '../../datas';
import { Observable } from 'rxjs';

export const LovStateToken = new StateToken<LovStateType>('LovState');

@Injectable()
@State<LovStateType>({
  name: LovStateToken,
  defaults: {
    languageType: new LovLanguageTypeData(),
    countryType: new LovCountryTypeData(),
    stateCriteria: new LovStateCriteriaData(),
    cityCriteria: new LovCityCriteriaData(),
    contactMediumType: new LovContactMediumTypeData(),
    npaItems: new LovItemData(),
    nxxItems: new LovItemData(),
    msisdnList: new SearchResourceQualificationInfoData(),
    deliveryMethods: new DeliveryMethodData(),
    geographyPlaces: new GeographyPlacesData(),
    orderTypes: new LovResultData(),
    orderStatuses: new LovResultData(),
  },
})
export class LovState {
  private lovApi = inject(LovApi);

  @Selector()
  static languageType({ languageType }: LovStateType): LovLanguageTypeData {
    return languageType;
  }

  @Selector()
  static countryType({ countryType }: LovStateType): LovCountryTypeData {
    return countryType;
  }

  @Selector()
  static stateCriteria({ stateCriteria }: LovStateType): LovStateCriteriaData {
    return stateCriteria;
  }

  @Selector()
  static cityCriteria({ cityCriteria }: LovStateType): LovCityCriteriaData {
    return cityCriteria;
  }

  @Selector()
  static contactMediumType({ contactMediumType }: LovStateType): LovContactMediumTypeData {
    return contactMediumType;
  }

  @Selector()
  static npaItems({ npaItems }: LovStateType) {
    return npaItems;
  }

  @Selector()
  static nxxItems({ nxxItems }: LovStateType) {
    return nxxItems;
  }

  @Selector()
  static misdnList({ msisdnList }: LovStateType) {
    return msisdnList;
  }

  @Selector()
  static deliveryMethods({ deliveryMethods }: LovStateType) {
    return deliveryMethods;
  }

  @Selector()
  static geographyPlaces({ geographyPlaces }: LovStateType) {
    return geographyPlaces;
  }

  @Selector()
  static orderTypes({ orderTypes }: LovStateType) {
    return orderTypes;
  }

  @Selector()
  static orderStatuses({ orderStatuses }: LovStateType) {
    return orderStatuses;
  }

  @Action(LovGetLanguageTypeAction)
  getLanguageConfig(
    { patchState }: StateContext<LovStateType>,
    { payload }: LovGetLanguageTypeAction,
  ): Observable<Lov.LovLanguageResponse> {
    return this.lovApi.getLanguageConfig$(LovGetLanguageTypeAction.type, buildCriteriaRequest(payload)).pipe(
      tap((response) => {
        patchState({ languageType: new LovLanguageTypeData(response.content) });
      }),
    );
  }

  @Action(LovGetCountryTypesAction)
  getCountryTypesConfig(
    { patchState }: StateContext<LovStateType>,
    { payload }: LovGetCountryTypesAction,
  ): Observable<Lov.LovCountryResponse> {
    return this.lovApi.getCountryTypesConfig$(LovGetCountryTypesAction.type, buildCriteriaRequest(payload)).pipe(
      tap((response) => {
        patchState({ countryType: new LovCountryTypeData(response.content) });
      }),
    );
  }

  @Action(LovGetStateCriteriaAction)
  getStateCriteria(
    { patchState }: StateContext<LovStateType>,
    { countryId }: LovGetStateCriteriaAction,
  ): Observable<Lov.LovStateResponse> {
    return this.lovApi.getStateCriteria$(LovGetStateCriteriaAction.type, buildCriteriaRequest({ countryId })).pipe(
      tap((response) => {
        patchState({ stateCriteria: new LovStateCriteriaData(response.content) });
      }),
    );
  }

  @Action(LovGetCityCriteriaAction)
  cityCriteria({ patchState }: StateContext<LovStateType>, { stateId }: LovGetCityCriteriaAction) {
    return this.lovApi.getCityCriteria$(LovGetCityCriteriaAction.type, buildCriteriaRequest({ stateId })).pipe(
      tap((response) => {
        patchState({ cityCriteria: new LovCityCriteriaData(response.content) });
      }),
    );
  }

  @Action(LovGetContactMediumTypeAction)
  getContactMediumTypeConfig(
    { patchState }: StateContext<LovStateType>,
    { payload }: LovGetContactMediumTypeAction,
  ): Observable<Lov.LovContactMediumResponse> {
    return this.lovApi.getContactMediumTypeConfig$(LovGetContactMediumTypeAction.type, payload).pipe(
      tap((response) => {
        patchState({ contactMediumType: new LovContactMediumTypeData(response.content) });
      }),
    );
  }

  @Action(LovGetNpaAction)
  inquireNpa({ patchState }: StateContext<LovStateType>): Observable<Lov.LovNpaXXResponse> {
    return this.lovApi.getNpa$(LovGetNpaAction.type).pipe(
      tap((response) => {
        patchState({ npaItems: new LovItemData(response.items) });
      }),
    );
  }

  @Action(LovGetNXXByNpaAction)
  getNXXByNpa(
    { patchState }: StateContext<LovStateType>,
    { payload }: LovGetNXXByNpaAction,
  ): Observable<Lov.LovNpaXXResponse> {
    return this.lovApi.getNXXByNpa$(LovGetNXXByNpaAction.type, payload).pipe(
      tap((response) => {
        patchState({ nxxItems: new LovItemData(response.items) });
      }),
    );
  }

  @Action(LovSearchMsisdnAction)
  inquireMsisdnList(
    { patchState }: StateContext<LovStateType>,
    { payload }: LovSearchMsisdnAction,
  ): Observable<Lov.LovSearchMsisdnResponse> {
    return this.lovApi.searchMsisdn$(LovSearchMsisdnAction.type, payload).pipe(
      tap((response) => {
        patchState({ msisdnList: new SearchResourceQualificationInfoData(response) });
      }),
    );
  }

  @Action(LovGetDeliveryMethodsAction)
  getDeliveryMethods({ patchState }: StateContext<LovStateType>): Observable<DeliveryMethod[]> {
    return this.lovApi.getDeliveryMethods$(LovGetDeliveryMethodsAction.type).pipe(
      tap((response) => {
        patchState({ deliveryMethods: new DeliveryMethodData(response) });
      }),
    );
  }

  @Action(LovGetGeographyPlacesAction)
  getGeographyPlaces(
    { patchState }: StateContext<LovStateType>,
    { payload }: LovGetGeographyPlacesAction,
  ): Observable<GeographyPlace[]> {
    return this.lovApi.getGeographyPlaces$(LovGetGeographyPlacesAction.type, payload).pipe(
      tap((response) => {
        patchState({ geographyPlaces: new GeographyPlacesData(response) });
      }),
    );
  }

  @Action(LovClearStateCriteriaAction)
  clearStateCriteria({ patchState }: StateContext<LovStateType>) {
    patchState({ stateCriteria: new LovStateCriteriaData([]) });
  }

  @Action(LovClearCityCriteriaAction)
  clearCityCriteria({ patchState }: StateContext<LovStateType>) {
    patchState({ cityCriteria: new LovCityCriteriaData([]) });
  }

  @Action(LovGetOrderTypesAction)
  getOrderTypes({ patchState }: StateContext<LovStateType>): Observable<Lov.LovResult[]> {
    return this.lovApi.getOrderTypes$(LovGetOrderTypesAction.type).pipe(
      tap((response) => {
        patchState({ orderTypes: new LovResultData(response) });
      }),
    );
  }

  @Action(LovGetOrderStatusesAction)
  getOrderStatuses({ patchState }: StateContext<LovStateType>): Observable<Lov.LovResult[]> {
    return this.lovApi.getOrderStatuses$(LovGetOrderStatusesAction.type).pipe(
      tap((response) => {
        patchState({ orderStatuses: new LovResultData(response) });
      }),
    );
  }
}
