import { inject, Injectable } from '@angular/core';
import { Action, Selector, State, StateContext, StateToken, Store } from '@ngxs/store';
import { map, tap } from 'rxjs/operators';
import { UsageSummaryGetUsageSummaryAction } from './usage-summary.action';
import { eCommon, eProduct, UsageSummary } from '@libs/types';
import { UsageSummaryApi } from '../../apis/usage-summary.api';
import { UsageSummaryStateType } from './usage-summary-state.type';
import { MyProductsState } from '../my-services-state';
import { ProductCharListData } from '../../datas';

export const UsageStateToken = new StateToken<UsageSummaryStateType>('UsageState');

@Injectable()
@State<UsageSummaryStateType>({
  name: UsageStateToken,
  defaults: {
    usageSummary: [],
  },
})
export class UsageSummaryState {
  private usageSummaryApi = inject(UsageSummaryApi);
  private store = inject(Store);

  @Selector()
  static usageSummary({ usageSummary }: UsageSummaryStateType): UsageSummary.UsageSummary[] {
    return usageSummary;
  }

  @Action(UsageSummaryGetUsageSummaryAction)
  getUsageSummaryAction(
    { patchState }: StateContext<UsageSummaryStateType>,
    { payload }: UsageSummaryGetUsageSummaryAction,
  ) {
    return this.usageSummaryApi.getUsageSummary$(UsageSummaryGetUsageSummaryAction.type, payload).pipe(
      map(() => {
        return this.usageMockMap();
      }),
      tap((usageSummary) => {
        patchState({
          usageSummary: usageSummary,
        });
      }),
    );
  }

  private usageMockMap() {
    const products = this.store.selectSnapshot(MyProductsState.products);
    return products.products?.reduce((acc: UsageSummary.UsageSummary[], product) => {
      const usageSummary = product.productDetailList
        .filter((detail) => ['subOffer', 'addOn'].includes(detail.productType))
        .map((detail) => {
          const charListData = new ProductCharListData(detail.productCharList);
          const maxValue =
            charListData.find(eProduct.ProductCharTypes.VOICE_AMOUNT)?.productCharValue ??
            charListData.find(eProduct.ProductCharTypes.DATA_AMOUNT)?.productCharValue ??
            charListData.find(eProduct.ProductCharTypes.SMS_AMOUNT)?.productCharValue ??
            'unlimited';

          const max = maxValue === 'unlimited' ? 100 : Number(maxValue);
          const usageAmount =
            maxValue === 'unlimited' ? 100 : Math.round((1 - new Date().getDate() / 31) * max * 100) / 100;
          return {
            billingAccountId: product.billingAccountId,
            productDetail: detail,
            usageType:
              detail.familyInfo.familySubCategory === eCommon.ProductFamilyCategoryShortCodes.VOICE_BUCKET ||
              detail.familyInfo.familySubCategory === 'voiceAddon' ||
              detail.familyInfo.family === eCommon.ProductFamilyShortCodes.FIXED_LTE
                ? 'Voice'
                : detail.familyInfo.familySubCategory === eCommon.ProductFamilyCategoryShortCodes.SMS_BUCKET ||
                    detail.familyInfo.familySubCategory === 'smsAddon'
                  ? 'Sms'
                  : detail.familyInfo.familySubCategory === eCommon.ProductFamilyCategoryShortCodes.DATA_BUCKET ||
                      detail.familyInfo.familySubCategory === 'dataAddon' ||
                      detail.familyInfo.family === eCommon.ProductFamilyShortCodes.INTERNET
                    ? 'Data'
                    : 'Other',
            isUnlimited: maxValue === 'unlimited',
            totalAmount: max,
            usageAmount,
            productId: detail.productId,
            unit:
              detail.familyInfo.familySubCategory === eCommon.ProductFamilyCategoryShortCodes.VOICE_BUCKET
                ? 'Min'
                : detail.familyInfo.familySubCategory === eCommon.ProductFamilyCategoryShortCodes.SMS_BUCKET
                  ? 'Sms'
                  : detail.familyInfo.familySubCategory === eCommon.ProductFamilyCategoryShortCodes.DATA_BUCKET
                    ? 'GB'
                    : '',
          } as UsageSummary.UsageSummary;
        });

      return acc.concat(usageSummary);
    }, []);
  }
}
