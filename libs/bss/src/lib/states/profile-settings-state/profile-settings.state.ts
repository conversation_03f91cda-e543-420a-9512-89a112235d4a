import { inject, Injectable } from '@angular/core';
import { Action, State, StateContext, StateToken } from '@ngxs/store';
import { catchError, throwError } from 'rxjs';
import {
  CreateCustomerContactMediumAction,
  DeleteCustomerContactMediumAction,
  UpdateCustomerContactMediumAction,
} from './profile-settings.actions';
import { ProfileSettingsStateType } from './profile-settings-state.type';
import { ProfileSettings } from '@libs/types';
import { ContactMediumApi } from '../../apis/contact-medium.api';

export const ProfileSettingsStateToken = new StateToken<ProfileSettingsStateType>('ProfileSettingsState');

@Injectable()
@State<ProfileSettingsStateType>({
  name: ProfileSettingsStateToken,
  defaults: {
    createCustomerContactMedium: null,
    deleteCustomerContactMedium: null,
  },
})
export class ProfileSettingsState {
  private contactMediumApi = inject(ContactMediumApi);

  @Action(CreateCustomerContactMediumAction)
  addEmailContactMedium({}: StateContext<ProfileSettings.State>, { payload }: CreateCustomerContactMediumAction) {
    return this.contactMediumApi
      .createContactMedium$(CreateCustomerContactMediumAction.type, payload)
      .pipe(catchError((error) => throwError(() => error)));
  }

  @Action(DeleteCustomerContactMediumAction)
  deleteEmailAddress({}: StateContext<ProfileSettings.State>, { payload }: DeleteCustomerContactMediumAction) {
    return this.contactMediumApi
      .deleteContactMedium$(DeleteCustomerContactMediumAction.type, payload)
      .pipe(catchError((error) => throwError(() => error)));
  }

  @Action(UpdateCustomerContactMediumAction)
  updateContactMedium({}: StateContext<ProfileSettings.State>, { payload }: UpdateCustomerContactMediumAction) {
    return this.contactMediumApi
      .updateContactMedium$(UpdateCustomerContactMediumAction.type, payload)
      .pipe(catchError((error) => throwError(error)));
  }
}
