import { Action, Selector, State, StateContext, StateToken, Store } from '@ngxs/store';
import { inject, Injectable } from '@angular/core';
import { PaymentStateType } from './payment-state.type';
import {
  PaymentGetCustomerPaymentMethodsAction,
  PaymentGetPaymentMethodDetailsAction,
  PaymentGetPaymentMethodTypesAction,
  PaymentRemovePaymentMethodAction,
  PaymentSaveNewPaymentMethodAction,
} from './payment.actions';
import { CurrentState, PaymentApi } from '@libs/bss';
import { switchMap, tap } from 'rxjs/operators';
import { PaymentMethodsData } from '../../datas/payment-methods.data';

export const PaymentStateToken = new StateToken<PaymentStateType>('PaymentState');

@Injectable()
@State<PaymentStateType>({
  name: PaymentStateToken,
  defaults: {
    customerPaymentMethods: new PaymentMethodsData(),
    paymentMethodDetails: null,
    paymentMethodTypes: null,
    paymentMethodDetail: null,
  },
})
export class PaymentState {
  private store = inject(Store);
  private paymentApi = inject(PaymentApi);

  @Selector()
  static customerPaymentMethods({ customerPaymentMethods }: PaymentStateType) {
    return customerPaymentMethods;
  }

  @Selector()
  static paymentMethodDetails({ paymentMethodDetails }: PaymentStateType) {
    return paymentMethodDetails;
  }

  @Selector()
  static paymentMethodTypes({ paymentMethodTypes }: PaymentStateType) {
    return paymentMethodTypes;
  }

  @Selector()
  static paymentMethodDetail({ paymentMethodDetail }: PaymentStateType) {
    return paymentMethodDetail;
  }

  @Action(PaymentGetCustomerPaymentMethodsAction)
  getCustomerPaymentMethods(
    { patchState }: StateContext<PaymentStateType>,
    { customerId }: PaymentGetCustomerPaymentMethodsAction,
  ) {
    return this.paymentApi.getCustomerPaymentMethods$(PaymentGetCustomerPaymentMethodsAction.type, customerId).pipe(
      tap((response) => {
        patchState({
          customerPaymentMethods: new PaymentMethodsData(response),
        });
      }),
    );
  }

  @Action(PaymentGetPaymentMethodDetailsAction)
  getPaymentMethodDetails(
    { patchState }: StateContext<PaymentStateType>,
    { paymentMethodTypeId }: PaymentGetPaymentMethodDetailsAction,
  ) {
    return this.paymentApi.getPaymentMethodDetails$(paymentMethodTypeId).pipe(
      tap((response) => {
        patchState({
          paymentMethodDetails: response,
        });
      }),
    );
  }

  @Action(PaymentSaveNewPaymentMethodAction)
  saveNewPaymentMethod(
    { patchState, dispatch }: StateContext<PaymentStateType>,
    { payload }: PaymentSaveNewPaymentMethodAction,
  ) {
    return this.paymentApi.saveNewPaymentMethod$(payload).pipe(
      tap((detail) => {
        patchState({
          paymentMethodDetail: detail,
        });
      }),
      switchMap(() => dispatch(new PaymentGetCustomerPaymentMethodsAction(payload.customerId))),
    );
  }

  @Action(PaymentRemovePaymentMethodAction)
  removePaymentMethod(
    { dispatch }: StateContext<PaymentStateType>,
    { paymentMethodId }: PaymentRemovePaymentMethodAction,
  ) {
    return this.paymentApi
      .removePaymentMethod$(paymentMethodId)
      .pipe(
        switchMap(() =>
          dispatch(new PaymentGetCustomerPaymentMethodsAction(this.store.selectSnapshot(CurrentState.customerId))),
        ),
      );
  }

  @Action(PaymentGetPaymentMethodTypesAction)
  getPaymentMethodTypes(
    { patchState }: StateContext<PaymentStateType>,
    { bsnInterSpecShortCode }: PaymentGetPaymentMethodTypesAction,
  ) {
    return this.paymentApi.getPaymentMethodTypes$(bsnInterSpecShortCode).pipe(
      tap((response) => {
        patchState({
          paymentMethodTypes: response,
        });
      }),
    );
  }
}
