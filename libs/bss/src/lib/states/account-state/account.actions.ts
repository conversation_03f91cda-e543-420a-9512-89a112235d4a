import { Address, eBusinessFlow } from '@libs/types';

export class GetIntegrationBillingAccountsAction {
  static readonly type = '[Account] GetIntegrationBillingAccountsAction';

  constructor(
    public customerId: number,
    public reasonType: eBusinessFlow.Specification,
    public page: number = 0,
    public size: number = 10,
  ) {}
}

export class SetNewInvoiceAddressAction {
  static readonly type = '[Account] SetNewInvoiceAddressAction';

  constructor(public billingAddress: Address) {}
}

export class GetInquireInstallmentAction {
  static readonly type = '[Account] GetInquireInstallmentAction';

  constructor(
    public customerId: number,
    public billingAccountId: number,
  ) {}
}
