import { tap } from 'rxjs/operators';
import { Address, Auth, Role, User } from '@libs/types';
import { Action, Selector, State, StateContext, StateToken, Store } from '@ngxs/store';
import { inject, Injectable } from '@angular/core';
import { UserCreateCredentialEcaTokenAction, UserGetAllRolesAction, UserGetLoggedInUserAction } from './user.actions';
import { UserApi } from '../../apis/user.api';
import { UserStateType } from './user-state.type';
import { Observable, of } from 'rxjs';
import { ConfigState, KeycloakService } from '@libs/plugins';

export const UserStateToken = new StateToken<UserStateType>('UserState');

@Injectable()
@State<UserStateType>({
  name: UserStateToken,
  defaults: {
    user: null,
    address: null,
    roles: [],
    credentialToken: null,
  },
})
export class UserState {
  private userApi = inject(UserApi);
  private store = inject(Store);
  private keycloakService = inject(KeycloakService);

  @Selector()
  static getLoggedInUser({ user }: UserStateType): User.LoggedInUser {
    return user;
  }

  @Selector()
  static getFirstName({ user }: UserStateType): string {
    return user?.name?.split(' ').slice(0, -1).join(' ');
  }

  @Selector()
  static loggedInCustomerId({ user }: UserStateType): number {
    return user.customerId;
  }

  @Selector()
  static csr({ user: { userType } }: UserStateType): boolean {
    return userType === 'CSR';
  }

  @Selector()
  static address({ address }: UserStateType): Address {
    return address;
  }

  @Selector()
  static getRoles({ roles }: UserStateType): Role.UserRole[] {
    return roles;
  }

  @Selector()
  static credentialToken({ credentialToken }: UserStateType) {
    return credentialToken;
  }

  @Action(UserGetLoggedInUserAction)
  getLoggedInUserAction({ patchState }: StateContext<UserStateType>) {
    if (!this.keycloakService.authenticated) {
      return of(null);
    }
    return this.userApi.getLoggedInUser$(UserGetLoggedInUserAction.type).pipe(
      tap((user) =>
        patchState({
          user: user,
        }),
      ),
    );
  }

  @Action(UserGetAllRolesAction)
  getAllRolesAction({ patchState }: StateContext<UserStateType>, { payload }: UserGetAllRolesAction) {
    return this.userApi.getAllRoles$(UserGetAllRolesAction.type, payload).pipe(
      tap((roles) =>
        patchState({
          roles,
        }),
      ),
    );
  }

  @Action(UserCreateCredentialEcaTokenAction)
  createCredentialToken(
    { patchState }: StateContext<UserStateType>,
    { payload }: UserCreateCredentialEcaTokenAction,
  ): Observable<string> {
    const config = this.store.selectSnapshot(ConfigState.getDeep('keycloak.config')) as Auth.Keycloak.ConfigOptions;
    const realm = config?.realm || 'master';

    return this.userApi.createCredential$(UserCreateCredentialEcaTokenAction.type, realm, payload).pipe(
      tap((response) => {
        patchState({ credentialToken: response });
      }),
    );
  }
}
