import { inject, Injectable } from '@angular/core';
import { Action, Selector, State, StateContext, StateToken } from '@ngxs/store';
import { tap } from 'rxjs/operators';
import { GetOfferTerms } from './term.actions';
import { TermApi } from '@libs/bss';
import { TermStateType } from './term-state.type';

export const TermStateToken = new StateToken<TermStateType>('TermState');

@Injectable()
@State<TermStateType>({
  name: TermStateToken,
  defaults: {
    termClauses: [],
  },
})
export class TermState {
  private termApi = inject(TermApi);

  @Selector()
  static terms({ termClauses }: TermStateType) {
    return termClauses;
  }

  @Action(GetOfferTerms)
  getOfferTermsFromKeyProductOffer({ patchState }: StateContext<TermStateType>, { payload }: GetOfferTerms) {
    return this.termApi.offerTermsFromKeyProductOffer$(GetOfferTerms.type, payload).pipe(
      tap((response) => {
        patchState({
          termClauses: response,
        });
      }),
    );
  }
}
