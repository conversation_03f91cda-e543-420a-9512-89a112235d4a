import { CustomerAddressListData, CustomerBillingAddressListData, CustomerNewAddressData } from '@libs/bss';
import { CustomerOrderData } from '../../datas/customer-order.data';
import { ContactMediumType, CustomerInfo } from '@libs/types';

export interface CustomerStateType {
  newAddress: CustomerNewAddressData;
  addressList: CustomerAddressListData;
  billingAddressList: CustomerBillingAddressListData;
  currentCustomerOrderItemId: number;
  customerOrders: CustomerOrderData;
  customerOrdersMap: Record<string, CustomerOrderData>;
  totalCustomerOrders: number;
  details: CustomerInfo.InquireCustomerInformationResponse;
  contactMediumList: ContactMediumType.ContactMediumDTO[];
  individualCustomerFormLovContent: CustomerInfo.InquireIndividualCustomerFormLovContentResponse;
  updateCustomerDemographicInfo: CustomerInfo.UpdateCustomerDemographicInfo;
  removeAddress: { addressId: number };
  updateAddress: CustomerInfo.UpdateCustomerAddressRequest;
}
