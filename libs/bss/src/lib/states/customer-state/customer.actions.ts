import { Address, CreateAddressWrapper, CustomerOrder, CustomerInfo } from '@libs/types';

export class CustomerGetAddressListAction {
  static readonly type = '[Customer] CustomerGetAddressesAction';

  constructor(public customerId: number) {}
}

export class CustomerGetBillingAddressListAction {
  static readonly type = '[Customer] CustomerGetBillingAddressListAction';

  constructor(public customerId: number) {}
}

export class CustomerCreateAddressAction {
  static readonly type = '[Customer] CustomerCreateAddressAction';

  constructor(public payload: CreateAddressWrapper) {}
}

export class SetCurrentCustomerOrderItemIdAction {
  static readonly type = '[Customer] SetCurrentCustomerOrderItemIdAction';

  constructor(public payload: number) {}
}

export class SetBillingAddressAction {
  static readonly type = '[Customer] SetBillingAddressAction';

  constructor(public billingAddress: Address) {}
}

export class InquireCustomerOrdersAction {
  static readonly type = '[Customer] InquireCustomerOrdersAction';

  constructor(public payload: CustomerOrder.InquireCustomerOrdersParams) {}
}

export class InquireCustomerInformationAction {
  static readonly type = '[Customer] InquireCustomerInformation';

  constructor(public payload: CustomerInfo.InquireCustomerInformationRequest) {}
}

export class InquireCustomerContactMediumAction {
  static readonly type = '[Customer] InquireCustomerContactMedium';

  constructor(public payload: CustomerInfo.InquireCustomerContactMediumRequest) {}
}

export class InquireIndividualCustomerFormLovContentAction {
  static readonly type = '[Customer] InquireIndividualCustomerFormLovContent';

  constructor(public payload: string) {}
}

export class UpdateCustomerDemographicInfoAction {
  static readonly type = '[Customer] UpdateCustomerDemographicInfo';

  constructor(public payload: CustomerInfo.UpdateCustomerDemographicInfo) {}
}

export class RemoveCustomerAddressAction {
  static readonly type = '[Customer] RemoveCustomerAddressAction';

  constructor(public payload: { addressId: number }) {}
}

export class UpdateCustomerAddressAction {
  static readonly type = '[Customer] UpdateCustomerAddressAction';

  constructor(public payload: CustomerInfo.UpdateCustomerAddressRequest) {}
}
