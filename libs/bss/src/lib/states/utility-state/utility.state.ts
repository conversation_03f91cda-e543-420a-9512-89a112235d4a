import { Action, Selector, State, StateContext, StateToken } from '@ngxs/store';
import { inject, Injectable } from '@angular/core';
import { UtilityApi } from '../../apis/utility.api';
import { UtilityStateType } from './utility-state.type';
import { UtilityGetPublicGeneralParameterAction } from './utility.actions';
import { tap } from 'rxjs/operators';
import { GeneralParameter } from '@libs/types';

export const UtilityStateToken = new StateToken<UtilityStateType>('UtilityStateToken');

@Injectable()
@State<UtilityStateType>({
  name: UtilityStateToken,
  defaults: {
    generalParameter: null,
  },
})
export class UtilityState {
  private utilityApi = inject(UtilityApi);

  @Selector()
  static generalParameter({ generalParameter }: UtilityStateType): GeneralParameter {
    return generalParameter;
  }

  @Action(UtilityGetPublicGeneralParameterAction)
  getPublicGeneralParameter(
    { patchState }: StateContext<UtilityStateType>,
    { payload }: UtilityGetPublicGeneralParameterAction,
  ) {
    return this.utilityApi
      .getPublicGeneralParameter$(UtilityGetPublicGeneralParameterAction.type, payload)
      .pipe(tap((response) => patchState({ generalParameter: response })));
  }
}
