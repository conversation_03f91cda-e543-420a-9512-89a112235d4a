import { Action, Selector, State, StateContext, StateToken } from '@ngxs/store';
import { QuoteStateType } from './quote-state.type';
import { inject, Injectable } from '@angular/core';
import { BusinessFlowApi } from '../../apis/business-flow.api';
import { catchError, tap } from 'rxjs/operators';
import {
  BusinessFlowInitializeAction,
  GetQuotePriceDetailAction,
  QuoteCancelQuoteAction,
  QuoteClearQuoteAction,
  QuoteGetDeliverInstallationRetrieveConfigAction,
  QuoteGetQuoteAction,
  QuoteNextStateAction,
  QuoteQueryAction,
  QuoteRemoveQuoteAction,
  QuoteSubmitQuoteAction,
  QuoteUpdateQuoteAction,
  ValidateSimCardAction,
} from './quote.action';

import { DeliverInstallationRetrieveConfigData, QuoteData } from '@libs/bss';
import { QuoteApi } from '../../apis/quote.api';
import { KeyQuoteData } from '../../datas/key-quote.data';
import { QuotePriceData } from '../../datas/quote-price.data';
import { AnalyticsAction } from '@libs/plugins';
import { throwError } from 'rxjs';

export const QuoteStateToken = new StateToken<QuoteStateType>('QuoteStateToken');

@Injectable()
@State<QuoteStateType>({
  name: QuoteStateToken,
  defaults: {
    initializeQuoteResponse: null,
    priceDetail: null,
    priceDetailMap: {},
    quote: new QuoteData(),
    offerDeliverInstallationRetrieveConfig: new DeliverInstallationRetrieveConfigData(),
    validateSimCardResponse: null,
    quoteQueryResponse: null,
    removeQuoteResponse: null,
    clearQuoteResponse: null,
    cancelQuoteResponse: null,
  },
})
export class QuoteState {
  private businessFlowApi = inject(BusinessFlowApi);
  private quoteApi = inject(QuoteApi);

  @Selector()
  static initializeQuoteResponse({ initializeQuoteResponse }: QuoteStateType): KeyQuoteData {
    return new KeyQuoteData(initializeQuoteResponse);
  }

  @Selector()
  static quote({ quote }: QuoteStateType): QuoteData {
    return quote;
  }

  @Selector()
  static priceDetail({ priceDetail }: QuoteStateType): QuotePriceData {
    return priceDetail;
  }

  @Selector()
  static priceDetailMap({ priceDetailMap }: QuoteStateType): Record<number, QuotePriceData> {
    return priceDetailMap;
  }

  @Selector()
  static quoteQueryResponse({ quoteQueryResponse }: QuoteStateType) {
    return quoteQueryResponse;
  }

  @Selector()
  static validateSimCardResponse({ validateSimCardResponse }: QuoteStateType) {
    return validateSimCardResponse;
  }

  @Selector()
  static getOfferDeliverInstallationRetrieveConfig({ offerDeliverInstallationRetrieveConfig }: QuoteStateType) {
    return offerDeliverInstallationRetrieveConfig;
  }

  @Selector()
  static removeQuoteResponse({ removeQuoteResponse }: QuoteStateType) {
    return removeQuoteResponse;
  }

  @Selector()
  static clearQuoteResponse({ clearQuoteResponse }: QuoteStateType) {
    return clearQuoteResponse;
  }

  @Action(BusinessFlowInitializeAction)
  businessFlowInitializeAction(
    { patchState }: StateContext<QuoteStateType>,
    { payload }: BusinessFlowInitializeAction,
  ) {
    return this.businessFlowApi.initializeAction$(BusinessFlowInitializeAction.type, payload).pipe(
      tap((initializeQuoteResponse) => {
        patchState({
          initializeQuoteResponse,
        });
      }),
    );
  }

  @Action(GetQuotePriceDetailAction)
  getQuotePriceDetail({ getState, patchState }: StateContext<QuoteStateType>, { payload }: GetQuotePriceDetailAction) {
    return this.quoteApi.priceDetail$(BusinessFlowInitializeAction.type, payload).pipe(
      tap((priceDetail) => {
        patchState({
          priceDetail: new QuotePriceData(priceDetail),
          quote: new QuoteData(getState().quote?.quote, priceDetail),
          priceDetailMap: {
            ...getState().priceDetailMap,
            [payload.customerOrderId]: new QuotePriceData(priceDetail),
          },
        });
      }),
    );
  }

  @Action(QuoteGetQuoteAction)
  getQuote({ getState, patchState }: StateContext<QuoteStateType>, { payload }: QuoteGetQuoteAction) {
    return this.quoteApi.getQuote$(QuoteGetQuoteAction.type, payload).pipe(
      tap((quoteResponse) => {
        patchState({
          quote: new QuoteData(quoteResponse, getState()?.quote?.price),
        });
      }),
    );
  }

  @Action(QuoteUpdateQuoteAction)
  updateQuote(_: StateContext<QuoteStateType>, { payload }: QuoteUpdateQuoteAction) {
    return this.quoteApi.updateQuote$(QuoteUpdateQuoteAction.type, payload);
  }

  @Action(QuoteNextStateAction)
  quoteNextStateAction(_: StateContext<QuoteStateType>, { payload }: QuoteNextStateAction) {
    return this.quoteApi.nextState$(QuoteNextStateAction.type, payload);
  }

  @Action(QuoteRemoveQuoteAction)
  removeQuote({ patchState }: StateContext<QuoteStateType>, { payload }: QuoteRemoveQuoteAction) {
    return this.quoteApi.removeQuote$(QuoteRemoveQuoteAction.type, payload).pipe(
      tap((quoteResponse) => {
        patchState({
          removeQuoteResponse: quoteResponse,
        });
      }),
    );
  }

  @Action(QuoteSubmitQuoteAction)
  submitQuote({ dispatch }: StateContext<QuoteStateType>, { payload }: QuoteSubmitQuoteAction) {
    const analyticsPayload = {
      category: 'order',
      orderId: payload.customerOrderId,
      businessFlowSpecShortCode: payload.businessFlowSpecShortCode,
    };
    dispatch(new AnalyticsAction('or_sub_start_' + payload.businessFlowSpecShortCode, analyticsPayload));

    return this.quoteApi.submitQuote$(QuoteSubmitQuoteAction.type, payload).pipe(
      tap(() => {
        dispatch(new AnalyticsAction('or_sub_success_' + payload.businessFlowSpecShortCode, analyticsPayload));
      }),
      catchError(({ error, status }) => {
        dispatch(
          new AnalyticsAction('or_sub_fail_' + payload.businessFlowSpecShortCode, {
            ...analyticsPayload,
            error_reason: error.message,
            response_status: status,
          }),
        );
        return throwError(error);
      }),
    );
  }

  @Action(QuoteGetDeliverInstallationRetrieveConfigAction)
  getDeliverInstallationRetrieveConfig(
    { patchState }: StateContext<QuoteStateType>,
    { payload }: QuoteGetDeliverInstallationRetrieveConfigAction,
  ) {
    return this.quoteApi
      .getDeliverInstallationRetrieveConfig$(QuoteGetDeliverInstallationRetrieveConfigAction.type, payload)
      .pipe(
        tap((response) => {
          patchState({
            offerDeliverInstallationRetrieveConfig: new DeliverInstallationRetrieveConfigData(response),
          });
        }),
      );
  }

  @Action(ValidateSimCardAction)
  validateSimCardAction({ patchState }: StateContext<QuoteStateType>, { payload }: ValidateSimCardAction) {
    return this.quoteApi.validateSimCard$(ValidateSimCardAction.type, payload).pipe(
      tap((response) => {
        patchState({
          validateSimCardResponse: response,
        });
      }),
    );
  }

  //TODO: delete
  @Action(QuoteQueryAction)
  quoteQuery({ patchState }: StateContext<QuoteStateType>, { payload }: QuoteQueryAction) {
    return this.quoteApi.getQuoteQuery$(QuoteQueryAction.type, payload).pipe(
      tap((response) => {
        patchState({
          quoteQueryResponse: response,
        });
      }),
    );
  }

  @Action(QuoteClearQuoteAction)
  clearQuote({ patchState }: StateContext<QuoteStateType>, { customerOrderId }: QuoteClearQuoteAction) {
    return this.quoteApi.clearQuote$(QuoteClearQuoteAction.type, customerOrderId).pipe(
      tap((quoteResponse) => {
        patchState({
          clearQuoteResponse: quoteResponse,
        });
      }),
    );
  }

  @Action(QuoteCancelQuoteAction)
  cancelQuote({ patchState }: StateContext<QuoteStateType>, { payload }: QuoteCancelQuoteAction) {
    return this.quoteApi.cancelQuote$(QuoteCancelQuoteAction.type, payload).pipe(
      tap((quoteResponse) => {
        patchState({
          cancelQuoteResponse: quoteResponse,
        });
      }),
    );
  }
}
