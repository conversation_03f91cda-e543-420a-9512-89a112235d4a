import { OffersData } from '@libs/bss';
import { ByodResources, eCommon, ExpoGroup } from '@libs/types';
import { OfferCatalogData } from '../../datas/offer-catalog.data';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type PartialRecord<K extends keyof any, T> = {
  [P in K]?: T;
};

export interface OfferStateType {
  expoGroups: ExpoGroup[];
  offers: OffersData;
  offerCatalog: PartialRecord<eCommon.CatalogShortCode, OfferCatalogData>;
  byodResources: ByodResources[];
}
