import {
  CatalogGroupContractType,
  eBusinessFlow,
  eCommon,
  InquireByodResourcesRequest,
  InquireExpoGroupsRequest,
  ProductOfferingQualification,
} from '@libs/types';

export class OfferGetExpoGroupsAction {
  static readonly type = '[Offer] OfferGetExpoGroupsAction';

  constructor(public payload: InquireExpoGroupsRequest) {}
}

export class OfferPOQWithContractTypeAction {
  static readonly type = '[Offer] OfferPOQWithContractTypeAction';

  constructor(
    public contractType: CatalogGroupContractType,
    public businessFlowSpec: eBusinessFlow.Specification = eBusinessFlow.Specification.MAIN_ORDER,
    public offerId: number = null,
  ) {}
}

export class OfferInquireProductOfferDetailAction {
  static readonly type = '[Offer] OfferInquireProductOfferDetailAction';

  constructor(
    public payload: ProductOfferingQualification,
    public type: CatalogGroupContractType,
  ) {}
}

export class OfferGetCatalogAction {
  static readonly type = '[Offer] GetOfferCatalogAction';

  constructor(public shortCode: eCommon.CatalogShortCode) {}
}

export class OfferGetByodResourcesAction {
  static readonly type = '[Offer] OfferGetByodResourcesAction';

  constructor(public payload: InquireByodResourcesRequest) {}
}
