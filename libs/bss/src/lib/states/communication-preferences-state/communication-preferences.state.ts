import { inject, Injectable } from '@angular/core';
import { Action, Selector, State, StateContext, StateToken } from '@ngxs/store';
import { CommunicationPreferencesStateType } from './communication-preferences-state.type';
import { CommunicationPreference, CapturedPartyPrivacy } from '@libs/types';
import {
  GetCommunicationPreferencesAction,
  GetCustomerContactMediumConsentAction,
} from './communication-preferences.actions';
import { CommunicationPreferencesApi } from '../../apis/communication-preferences.api';
import { tap } from 'rxjs/operators';

export const CommunicationPreferencesStateToken = new StateToken<CommunicationPreferencesStateType>(
  'CommunicationPreferencesState',
);

@Injectable()
@State<CommunicationPreferencesStateType>({
  name: CommunicationPreferencesStateToken,
  defaults: {
    customerContactMediumConsent: null,
  },
})
export class CommunicationPreferencesState {
  private communicationPreferencesApi = inject(CommunicationPreferencesApi);

  @Selector()
  static customerContactMediumConsent({
    customerContactMediumConsent,
  }: CommunicationPreferencesStateType): CapturedPartyPrivacy.Result {
    return customerContactMediumConsent;
  }

  @Action(GetCustomerContactMediumConsentAction)
  getCustomerContactMediumConsent(
    { patchState }: StateContext<CommunicationPreferencesStateType>,
    { custId, mediumType }: GetCustomerContactMediumConsentAction,
  ) {
    return this.communicationPreferencesApi
      .getCustomerContactMediumConsent$(GetCustomerContactMediumConsentAction.type, custId, mediumType)
      .pipe(
        tap((customerContactMediumConsent: CapturedPartyPrivacy.Result) =>
          patchState({ customerContactMediumConsent }),
        ),
      );
  }

  @Action(GetCommunicationPreferencesAction)
  getCommunicationPreferences(
    { patchState }: StateContext<CommunicationPreference.State>,
    { payload }: GetCommunicationPreferencesAction,
  ) {
    return this.communicationPreferencesApi
      .getCommunicationPreferences$(GetCommunicationPreferencesAction.type, payload)
      .pipe(tap((commPref: CapturedPartyPrivacy.Result) => patchState({ commPref })));
  }
}
