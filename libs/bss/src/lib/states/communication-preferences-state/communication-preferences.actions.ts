import { CommunicationPreference } from '@libs/types';

export class GetCustomerContactMediumConsentAction {
  static readonly type = '[CommunicationPreferences] GetCustomerContactMediumConsent';

  constructor(
    public custId: number,
    public mediumType: string,
  ) {}
}

export class GetCommunicationPreferencesAction {
  static readonly type = '[CommunicationPreferences] GetCommunicationPreferences';

  constructor(public readonly payload: CommunicationPreference.PartyPrivacySearchRequestForCrud) {}
}
