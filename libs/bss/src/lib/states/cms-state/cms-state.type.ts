import {
  CmsFooterData,
  CmsHeaderData,
  CMSOffersData,
  CMSPageData,
  CmsPhoneVariationsData,
  MixAndMatchData,
} from '@libs/bss';

export interface CmsStateType {
  page: CMSPageData[];
  header: CmsHeaderData;
  footer: CmsFooterData;
  offers: CMSOffersData;
  phoneVariations: CmsPhoneVariationsData;
  phoneCommerceProductVariations: Record<string, CmsPhoneVariationsData>;
  mixAndMatch: MixAndMatchData;
  mixAndMatchOffers: MixAndMatchData;
}
