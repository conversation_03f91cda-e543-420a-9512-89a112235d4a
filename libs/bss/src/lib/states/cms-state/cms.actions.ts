import { AlternateProductOfferingProposal, CMS } from '@libs/types';

export class CmsGetPageAction {
  static readonly type = '[Cms] CmsGetPageAction';

  constructor(public readonly payload: string) {}
}

export class CmsGetEntityAction {
  static readonly type = '[Cms] CmsGetEntityAction';

  constructor(public readonly payload: CMS.EntityRequestParams) {}
}

export class CmsGetHeaderAction {
  static readonly type = '[Cms] CmsGetHeaderAction';
}

export class CmsGetFooterAction {
  static readonly type = '[Cms] CmsGetFooterAction';
}

export class CmsGetFooterTopAction {
  static readonly type = '[Cms] CmsGetFooterTopAction';
}

export class CmsGetCommerceProductAction {
  static readonly type = '[Cms] CmsGetCommerceProductAction';

  constructor(
    public readonly offerIds?: string[],
    public readonly offers?: AlternateProductOfferingProposal[],
  ) {}
}

export class CmsGetPhoneVariationWithPoqAction {
  static readonly type = '[Cms] CmsGetPhoneVariationWithPoqAction';

  constructor(public readonly payload: { offerId: string }) {}
}

export class CmsGetPhoneCommerceProductVariationsAction {
  static readonly type = '[Cms] CmsGetPhoneCommerceProductVariationsAction';

  constructor(public readonly payload: { offerId: string }) {}
}

export class CmsGetMixAndMatchAction {
  static readonly type = '[Cms] CmsGetMixAndMatchAction';

  constructor(public readonly payload: { filters: CMS.MixAndMatchFilter[] }) {}
}
