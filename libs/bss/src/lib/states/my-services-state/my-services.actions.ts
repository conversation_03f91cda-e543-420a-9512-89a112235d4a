import { ProductSearchRequest } from '@libs/types';

export class MyServicesGetProductListAction {
  static readonly type = '[MyServices] MyServicesGetProductListAction';

  constructor(public readonly payload: ProductSearchRequest) {}
}

export class MyServicesGetProductDetailAction {
  static readonly type = '[MyServices] MyServicesGetProductDetailAction';

  constructor(public readonly payload: ProductSearchRequest) {}
}

export class MyServicesClearProductsAction {
  static readonly type = '[MyServices] MyServicesClearProductsAction';

  constructor() {}
}
