/* eslint-disable @typescript-eslint/no-explicit-any */
import { CMS } from '@libs/types';
import { CmsMenuItem } from '@libs/widgets';

function removeFieldPrefix(attributes: Record<string, unknown>) {
  const cleanedAttributes: Record<string, any> = {};

  for (const [key, value] of Object.entries(attributes)) {
    const cleanedKey = key.startsWith('field_') ? key.slice(6) : key;
    cleanedAttributes[cleanedKey] = value;
  }

  return cleanedAttributes;
}

export function drupalGetAttributes(data: CMS.PageData, included: CMS.PageData[] = []) {
  if (!data) {
    return undefined;
  }
  const relationships = Object.entries(data.relationships).reduce((acc, [key, value]) => {
    if (Array.isArray(value.data)) {
      acc[key] = value.data.map((item) => {
        const relatedData = included.find((related) => related.id === item.id);
        return relatedData ? drupalGetAttributes(relatedData, included) : item;
      });
      return acc;
    }
    const relatedData = included.find((item) => item.id === value.data?.id);

    if (relatedData) {
      acc[key] = drupalGetAttributes(relatedData, included);
    } else {
      acc[key] = value;
    }

    return acc;
  }, {} as any);

  return removeFieldPrefix({ ...data.attributes, ...relationships });
}

export function drupalGetMenu(data: CMS.PageData[]) {
  const map: Record<string, CmsMenuItem & { weight: number }> = {};
  const result: CmsMenuItem[] = [];

  data.forEach((item) => {
    map[item.id] = {
      label: item.attributes.title,
      href: item.attributes.url || '',
      isExternal: isExternalLink(item.attributes.url || ''),
      items: [],
      weight: item.attributes.weight || 0,
      icon: item.attributes.field_icon || '',
    };
  });

  data.forEach((item) => {
    const parentId = item.attributes.parent;
    if (parentId) {
      map[parentId]?.items.push(map[item.id]);
    } else {
      result.push(map[item.id]);
    }
  });

  const sortByWeight = (arr: CmsMenuItem[]) =>
    arr.sort((a, b) => {
      const weightA = map[a.label]?.weight ?? 0;
      const weightB = map[b.label]?.weight ?? 0;
      return weightA - weightB;
    });

  result.forEach((item) => {
    if (item.items?.length) {
      sortByWeight(item.items);
    }
  });

  return sortByWeight(result);
}

export function isExternalLink(url: string) {
  if (!url || url.startsWith('/')) {
    return false;
  }

  const currentDomain = window.location.hostname;
  const linkDomain = new URL(url).hostname;

  return linkDomain !== currentDomain;
}

export function getLink(url: string) {
  return url?.replace('internal:', '');
}
