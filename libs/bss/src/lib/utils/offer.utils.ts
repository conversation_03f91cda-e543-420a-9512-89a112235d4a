import {
  CommercialRegion,
  eBusinessFlow,
  ProductOfferingQualification,
  ProductOfferingQualificationItemFlatten,
  ProductOfferingQualificationRequest,
} from '@libs/types';
import { OfferPOQWithContractTypeAction } from '../states';

export function getAlternateProductOfferingProposal(productOfferingQualification: ProductOfferingQualification) {
  const productOfferingQualificationItem = productOfferingQualification?.productOfferingQualificationItem.flat();
  if (!productOfferingQualificationItem) {
    return [];
  }

  return (
    productOfferingQualificationItem.flatMap(
      ({ alternateProductOfferingProposal }) => alternateProductOfferingProposal,
    ) ?? []
  );
}

export function getAllProductOfferIdList(
  productOfferingQualification: ProductOfferingQualification,
  categoryCode?: string,
): number[] {
  return productOfferingQualification.productOfferingQualificationItem
    .filter((item) => !categoryCode || item.category.code === categoryCode)
    .map((data) => data.alternateProductOfferingProposal)
    .reduce((acc, prep) => [...acc, ...prep])
    .reduce((acc, { alternateProductOffering }) => [...acc, Number(alternateProductOffering.id)], []);
}

export function productOfferingQualificationItemsFlatten(
  productOfferingQualification: ProductOfferingQualification,
): ProductOfferingQualificationItemFlatten[] {
  if (!productOfferingQualification || !productOfferingQualification.productOfferingQualificationItem) {
    return [];
  }
  return productOfferingQualification.productOfferingQualificationItem.flatMap<ProductOfferingQualificationItemFlatten>(
    (productOfferingQualification) =>
      productOfferingQualification.alternateProductOfferingProposal.map(({ alternateProductOffering }) => ({
        alternateProductOffering,
        category: productOfferingQualification.category,
      })),
  );
}

export function productOfferingQualificationPayload(
  action: OfferPOQWithContractTypeAction,
  geographicRegion?: CommercialRegion,
): ProductOfferingQualificationRequest {
  const offerItem = action.offerId
    ? {
        offer: {
          id: action.offerId,
        },
        product: {
          productOffering: {
            id: action.offerId,
          },
        },
      }
    : {};

  return {
    channel: {
      name: 'WSC',
    },
    provideOnlyAvailable: 'true',
    productOrder: {
      orderType:
        action.businessFlowSpec === eBusinessFlow.Specification.MAIN_ORDER
          ? eBusinessFlow.Specification.REAL_SALE
          : action.businessFlowSpec,
      productOrderCharacteristic: [
        {
          code: 'bsnFlowSpec',
          value: action.businessFlowSpec,
        },
        ...(action.businessFlowSpec === eBusinessFlow.Specification.MAIN_ORDER
          ? [
              {
                code: 'actionReasonCode',
                value: 'acquisition',
              },
            ]
          : []),
        {
          code: 'includeOfferDisplayGroupChars',
        },
        { code: 'includeExpoGroupChars' },
      ],
    },
    productOfferingQualificationItem: [
      {
        id: '1',
        ...offerItem,
        category: {
          name: 'recommendationBundleList',
        },
        serviceType: 'mobile',
        contractType: action.contractType,
      },
    ],
    ...(geographicRegion
      ? {
          place: {
            geographicRegion: geographicRegion,
          },
        }
      : {}),
  } as ProductOfferingQualificationRequest;
}
