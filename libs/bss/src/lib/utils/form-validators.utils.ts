import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export function creditCardExpireDateValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;

    if (value && value.length === 4) {
      const month = parseInt(value.slice(0, 2), 10);
      const year = parseInt(value.slice(2), 10);

      const currentDate = new Date();
      const currentMonth = currentDate.getMonth() + 1;
      const currentYear = currentDate.getFullYear() % 100;

      if (isNaN(month) || month < 1 || month > 12) {
        return { invalidMonth: true };
      }

      if (isNaN(year) || year < currentYear || (year === currentYear && month < currentMonth)) {
        return { expiredDate: true };
      }

      return null;
    }

    return { invalidFormat: true };
  };
}

export class BSSValidators {
  static creditCardExpireDateValidator = creditCardExpireDateValidator();
}
