/* eslint-disable @typescript-eslint/no-explicit-any */
import { AbstractControl, ValidatorFn } from '@angular/forms';

export function regexValidator(regex: RegExp, msg: string): ValidatorFn {
  return (control: AbstractControl): { [key: string]: any } | null => {
    if (control.value && control.value.length > 0) {
      const valid = regex.test(control.value);
      return valid ? null : { [msg]: msg };
    } else {
      return null;
    }
  };
}
