import { Injectable } from '@angular/core';
import { AbstractControl, ValidationErrors, Validator } from '@angular/forms';
import { parse, isValid } from 'date-fns';
import { REGISTER_DATE_FORMAT } from '@libs/core';

@Injectable({ providedIn: 'root' })
export class AgeValidator implements Validator {
  validate(control: AbstractControl): ValidationErrors | null {
    let dateStr = control.value;
    if (!dateStr || typeof dateStr !== 'string') return null;

    const minAge = +(this as this & { minAge: number | string }).minAge || 18;
    const today = new Date();

    dateStr = dateStr.slice(0, 10);

    if (/[a-zA-Z]/.test(dateStr)) {
      return { containsInvalidCharacters: true };
    }

    const birthDate = parse(dateStr, REGISTER_DATE_FORMAT, new Date());

    if (!isValid(birthDate)) {
      return { isNotAdult: true };
    }

    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    const dayDiff = today.getDate() - birthDate.getDate();

    if (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)) {
      age--;
    }

    return age >= minAge
      ? null
      : {
        isNotAdult: {
          minAge,
        },
      };
  }
}

