import { Injectable } from '@angular/core';
import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { parse, isValid, format } from 'date-fns';

@Injectable({ providedIn: 'root' })
export class DateFormatValidator {
  validate(expectedFormat: string): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
        return null;
      }

      const parsedDate = parse(control.value, expectedFormat, new Date());

      if (!isValid(parsedDate)) {
        return { invalidFormat: true };
      }

      const formattedDate = format(parsedDate, expectedFormat);
      if (formattedDate !== control.value) {
        return { invalidFormat: true };
      }

      return null;
    };
  }
}
