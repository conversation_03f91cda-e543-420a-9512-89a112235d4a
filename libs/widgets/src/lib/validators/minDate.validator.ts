import { Injectable } from '@angular/core';
import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

@Injectable({ providedIn: 'root' })
export class MinDateValidator {
  validate(minDate: string = '01-01-1900'): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
        return null;
      }

      const controlDate = new Date(control.value);
      const minimumDate = new Date(minDate);

      return controlDate < minimumDate ? { min: { min: new Date(minimumDate).toLocaleDateString('en-GB') } } : null;
    };
  }
}
