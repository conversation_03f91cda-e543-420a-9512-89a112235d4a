export interface SegmentOptions {
  text?: string;
  value?: string;
  iconName?: string;
  onlyIcon?: boolean;
  isDisabled?: boolean;
  isSelected?: boolean;
  defaultSelected?: boolean;
  ariaControls?: string;
  className?: string;
}

export interface SegmentData<T> {
  id: string;
  title: string;
  data: T[];
}

export interface SegmentArea<T> {
  options: Partial<SegmentOptions[]>;
  summary: Partial<SegmentData<T>[]>;
}

export enum SegmentedAreaKnowledgeKeys {
  ALL = 'all', //show all
}
