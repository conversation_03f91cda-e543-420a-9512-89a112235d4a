import { TagAppearances } from '@eds/components';
import { SegmentOptions } from './segment-area.model';
import { ProductData } from '@libs/bss';

export interface UsageSummaryData {
  id: string;
  deactivated?: boolean;
  data: {
    label: string;
    helperText?: string;
    value: number;
    maxValue?: number;
    valueType?: string;
    isIndeterminate?: boolean;
    isAnimated?: boolean;
    isUnlimited?: boolean;
    type?: string;
    circleProperties?: unknown;
  }[];
}

export interface PlanUsage {
  usageOptions: UsageOption[];
  usageSummary: UsageSummaryData[];
}

export type UsageOption = SegmentOptions;

export interface UsageSummaryPlan {
  className?: string;
  label: string;
  value: string;
  name: string;
  product?: ProductData;
  isSelected?: boolean;
  isDisabled?: boolean;
  usage?: PlanUsage;
  appearance?: TagAppearances;
  statusDescription?: string;
}
