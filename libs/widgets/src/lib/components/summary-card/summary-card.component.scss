:host {
  --eds-summary-border: var(
    --card-border,
    var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default)
  );
}

.shopping-cart {
  width: 100%;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 0;
  box-shadow: 0 -4px 8px 0 rgba(0, 0, 0, 0.04);
  overflow: hidden;
  border: var(--eds-summary-border);

  @media (min-width: 1440px) {
    border-radius: 24px;
    box-shadow: 0 3px 0 rgba(0, 0, 0, 0.04);
  }
}
.header {
  display: none;
  align-items: center;
  padding: 16px 24px;
  background-color: #fff;

  @media (min-width: 1440px) {
    display: flex;
  }
}
.header h1 {
  font-size: 20px;
  font-weight: 500;
  color: #242442;
  letter-spacing: -0.4px;
}
