<div part="base">
  @if (showCustomerStatus() && customerStatus() !== undefined) {
    <eds-tag
      part="customer-status"
      [appearance]="statusTagContent().appearance"
      [content]="statusTagContent().content | uppercase"
    ></eds-tag>
  }
  <div part="content">
    <eds-heading part="heading" as="h2" [text]="headingText()" [size]="headingSize()"></eds-heading>
    @if (subheadingText()) {
      <eds-heading part="subheading" as="h3" [text]="subheadingText()" [size]="subheadingSize()"></eds-heading>
    }
  </div>
</div>
