import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  ElementRef,
  input,
  model,
  output,
  ViewChild,
} from '@angular/core';
import { CmsMenuItem } from '../../../model/cms-header.model';
import { MiniShoppingCartComponent } from '../shopping-cart';
import { select } from '@ngxs/store';
import { ConfigState } from '@libs/plugins';
import { FeatureFlagEnum } from '@libs/types';
import { HeaderNavigationComponent } from '../../header-navigation';
@Component({
  selector: 'widget-cms-header',
  templateUrl: './cms-header.component.html',
  styleUrls: ['./cms-header.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [MiniShoppingCartComponent, HeaderNavigationComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CmsHeaderComponent {
  loggedIn = input(false);
  name = input('');
  logoHref = input('');
  logoSrc = input('');
  brandName = input('');
  navigation = input<CmsMenuItem[]>([]);
  quickLinks = input<CmsMenuItem[]>([]);
  userTypeMenu = input(true);
  cartItemsCount = input(0);
  languageSection = select(ConfigState.isFeatureEnabled(FeatureFlagEnum.languageSection));

  languagePreferences = model<boolean>();

  navigateCart = output<void>();

  @ViewChild('base') base!: ElementRef<HTMLElement>;

  openLanguagePreference(): void {
    this.languagePreferences.set(true);
  }

  changeUserType(event: Event): void {
    const container = this.base.nativeElement.querySelector('.user-type-menu-content');

    const clickedButton = event.currentTarget as HTMLElement;

    const buttons = container.querySelectorAll('eds-button');
    buttons.forEach((button) => {
      button.classList.remove('active');
    });

    clickedButton.classList.add('active');
  }
}
