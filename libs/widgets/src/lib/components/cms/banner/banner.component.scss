.base {
  display: grid;
  justify-items: center;
}

.banner {
  position: relative;
  display: grid;
  justify-content: center;
  grid-template-columns: minmax(0, calc(var(--eds-size-multiplier) * 288));
  gap: var(--eds-spacing-400);
  padding: 72px 0px;
  width: 100%;
  z-index: 1;

  .banner-image {
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    width: 100%;
    z-index: 1;

    img {
      position: absolute;
      top: 0;
      right: 0;
      height: 100%;
      width: auto;
      object-fit: cover;
      object-position: 0% 100%;
    }
  }

  eds-heading,
  eds-text {
    color: var(--eds-colors-text-white);
    padding-inline: 24px;
    z-index: 2;
  }

  .title {
    &.desktop {
      display: none;
    }
  }

  @media (min-width: 834px) {
    padding: 48px 0px;

    .title {
      &.desktop {
        display: block;
        width: 50%;
      }
    }

    .title {
      &.mobile {
        display: none;
      }
    }
  }

  @media (min-width: 920px) {
    .banner-image {
      img {
        object-position: 64% 100%;
        object-fit: cover;
        width: 100%;
      }
    }
  }

  @media (min-width: 1440px) {
    padding: 148px 0px;

    .title {
      &.desktop {
        width: unset;
      }
    }
  }
}
