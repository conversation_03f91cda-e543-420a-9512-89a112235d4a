import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';

@Component({
  selector: 'widget-cms-banner',
  templateUrl: './banner.component.html',
  styleUrls: ['./banner.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CmsBannerComponent {
  title = input('');
  description = input('');
  image = input('/assets/images/cms-banner.jpeg');
}
