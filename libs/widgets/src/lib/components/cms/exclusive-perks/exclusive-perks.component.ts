import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { CmsBlogCardComponent } from '../blog-card';
import { SwiperComponent } from '../../swiper';
import { SwiperOptions } from '../../../model/swiper.model';
import { CmsBlogCard } from '../../../model/cms-blog-card.model';

@Component({
  selector: 'widget-cms-exclusive-perks',
  templateUrl: './exclusive-perks.component.html',
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CmsBlogCardComponent, SwiperComponent],
})
export class CmsExclusivePerksComponent {
  items = input<CmsBlogCard[]>([]);

  swiperConfig = input<SwiperOptions>({
    spaceBetween: 16,
    slidesPerView: 1,
    loop: false,
    pagination: true,
    breakpoints: {
      600: {
        slidesPerView: 2,
        spaceBetween: 16,
        pagination: true,
      },
      1200: {
        slidesPerView: 3,
        spaceBetween: 24,
        pagination: false,
      },
    },
  });

  config = computed<SwiperOptions>(() => ({
    ...this.swiperConfig(),
  }));
}
