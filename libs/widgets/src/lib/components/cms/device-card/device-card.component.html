<eds-card [hoverBorderColor]="'var(--eds-colors-secondary-default)'" [tags]="tags()">
  <eds-image [src]="deviceImage()"></eds-image>
  <div class="info">
    <div class="product">
      <eds-text size="lg" [text]="brand()"></eds-text>
      <eds-heading size="sm" [text]="model()"></eds-heading>
    </div>
    <div class="price">
      <eds-text size="lg" [text]="price()"></eds-text>
      @if (oldPrice()) {
        <eds-text size="sm" as="del" [text]="oldPrice()"></eds-text>
      }
    </div>
    @if (couponAvailable()) {
      <eds-button appearance="link" size="compact" iconLeading="coupon" shouldFitContainer>
        Coupons Available
      </eds-button>
    }
  </div>
  <eds-button class="buy-now-button" appearance="primary" shouldFitContainer (button-click)="onSelect.emit(deviceId())">
    Buy Now
  </eds-button>
</eds-card>
