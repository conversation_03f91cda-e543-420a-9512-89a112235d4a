import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { CmsBlogBadgeComponent } from '../blog-badge';
import { CmsBlogBadgeTypes } from '../../../model';

@Component({
  selector: 'widget-cms-blog-card',
  templateUrl: './blog-card.component.html',
  styleUrls: ['./blog-card.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CmsBlogBadgeComponent],
})
export class CmsBlogCardComponent {
  title = input('');
  helperText = input('');
  image = input('');
  badgeType = input<CmsBlogBadgeTypes>('green');
  badgeLabel = input('');
}
