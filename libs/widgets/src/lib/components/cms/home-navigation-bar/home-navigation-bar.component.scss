.base {
  display: flex;
  //padding: 32px 156px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: var(--eds-spacing-400);
  align-self: stretch;
  border-bottom: 1px solid var(--eds-color-grey-200, #cacae2);
}

.menu-container {
  display: flex;
  padding: var(--eds-spacing-0);
  justify-content: center;
  align-items: flex-start;
  gap: var(--eds-spacing-600);
  align-self: stretch;
}

.menu-box {
  display: flex;
  padding: var(--eds-spacing-600) var(--eds-spacing-400);
  flex-direction: column;
  align-items: center;
  gap: var(--eds-spacing-200);
  flex: 1 0 0;

  eds-icon {
    width: 40px;
    height: 40px;
  }

  .title-wrapper {
    display: flex;
    padding: var(--eds-spacing-0, 0px);
    flex-direction: column;
    align-items: center;
    gap: var(--eds-spacing-200);
    align-self: stretch;

    eds-link::part(base) {
      cursor: pointer;
      text-decoration: none;
      color: inherit;
      font-size: calc(var(--eds-size-multiplier) * 4);
    }
  }
}
