<div class="base">
  <widget-swiper [config]="config()">
    @for (banner of banners(); track $index) {
      <div class="banner" [style.backgroundImage]="'url(' + banner.image + ')'">
        <div class="box">
          <div class="information">
            @if (banner?.title) {
              <eds-heading class="superTitle" size="sm" [text]="banner.title"></eds-heading>
            }
            <eds-heading class="title" size="xxl" [text]="banner.superTitle"></eds-heading>
            @if (banner?.subtitle) {
              <eds-text class="subtitle" size="xl" [text]="banner?.subtitle"></eds-text>
            }
          </div>

          <div class="buttons">
            @for (button of banner.buttons; track $index) {
              <eds-button [href]="button.link" size="md" class="button" appearance="primary">
                {{ button.text }}
              </eds-button>
            }
          </div>
        </div>
      </div>
    }
  </widget-swiper>
</div>
