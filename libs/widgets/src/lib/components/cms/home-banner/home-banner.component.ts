import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { CmsHomeBanner } from '../../../model';
import { SwiperComponent } from '../../swiper';
import { SwiperOptions } from '../../../model/swiper.model';

@Component({
  selector: 'widget-cms-home-banner',
  templateUrl: './home-banner.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  styleUrls: ['./home-banner.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [SwiperComponent],
})
export class HomeBannerComponent {
  swiperConfig = input<SwiperOptions>({
    spaceBetween: 16,
    slidesPerView: 1,
    loop: false,
    pagination: true,
  });

  pagination = input<boolean>(false);

  config = computed<SwiperOptions>(() => ({
    ...this.swiperConfig(),
    pagination: this.pagination(),
  }));

  banners = input<CmsHomeBanner[]>([]);
}
