:host {
  --eds-spacing-350: var(--spacing-200, calc(var(--eds-size-multiplier) * 3.5));
  --font-size-heading-sm: calc(var(--eds-size-multiplier) * 6);
  --line-height-heading-sm: calc(var(--eds-size-multiplier) * 8);
  --font-weight-text-sm: var(--eds-font-weight-regular, 400);

  --font-size-heading-xxl: calc(var(--eds-size-multiplier) * 20);
  --line-height-heading-xxl: calc(var(--eds-size-multiplier) * 24);

  --eds-carousel-bottom-spacing: var(--carousel-bottom-spacing, var(--eds-spacing-600));
  --eds-carousel-pagination-height: var(--carousel-pagination-height, var(--eds-sizing-200));
  --eds-carousel-pagination-color: var(--carousel-pagination-color, var(--eds-colors-primary-dark));

  --eds-spacing-1000: var(--spacing-900, calc(var(--eds-size-multiplier) * 14));
}

.base {
  display: flex;
  flex-direction: column;
  position: relative;
}

.banner {
  display: grid;
  justify-content: center;
  grid-template-columns: minmax(0, calc(var(--eds-size-multiplier) * 288));
  gap: var(--eds-spacing-200);
  padding: 96px 0px;
  width: 100%;

  background-color: lightgray;
  background-position: 50%;
  background-size: cover;
  background-repeat: no-repeat;

  .box {
    display: flex;
    width: 701px;
    padding: var(--eds-spacing-1000) var(--eds-spacing-0) var(--eds-spacing-1000) var(--eds-spacing-0);
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: var(--eds-spacing-900);
    flex-shrink: 0;
    align-self: stretch;

    .information {
      display: flex;
      padding: var(--eds-spacing-0);
      flex-direction: column;
      align-items: flex-start;
      gap: var(--eds-spacing-400);
      align-self: stretch;
    }

    eds-heading.superTitle::part(base) {
      font-size: var(--font-size-heading-sm);
      line-height: var(--line-height-heading-sm);
      color: var(--eds-colors-secondary-default);
    }

    eds-heading.title::part(base) {
      font-size: var(--font-size-heading-xxl);
      line-height: var(--line-height-heading-xxl);
      color: var(--eds-colors-text-white);
    }

    eds-text.subtitle::part(base) {
      font-size: var(--font-size-heading-sm);
      line-height: var(--line-height-heading-sm);
      font-weight: var(--font-weight-text-sm);
      color: var(--eds-colors-text-white);
    }

    eds-button {
    }

    .buttons {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: var(--eds-spacing-200);
    }
  }
}

:host ::ng-deep .swiper-pagination {
  position: absolute;
  bottom: 0;

  .bullet-active {
    width: 24px;
    --swiper-pagination-color: var(--eds-colors-secondary-default);
  }
}
