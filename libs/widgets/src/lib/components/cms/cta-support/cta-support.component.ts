import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { Cms } from '../../../model';

@Component({
  selector: 'widget-cms-cta-support',
  templateUrl: './cta-support.component.html',
  styleUrls: ['./cta-support.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CmsCtaSupportComponent {
  title = input();
  subtitle = input();
  buttons = input<Cms.CtaButton[]>([]);
}
