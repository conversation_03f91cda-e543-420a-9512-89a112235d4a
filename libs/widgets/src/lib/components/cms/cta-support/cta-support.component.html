<div class="base">
  <div class="content">
    <eds-heading [text]="title()" size="xl"></eds-heading>
    <eds-text [text]="subtitle()" size="lg"></eds-text>
  </div>
  <div class="buttons">
    @for (button of buttons(); track button.label) {
      <eds-button (button-click)="button.action()" shouldFitContainer [appearance]="button.appearance || 'primary'">
        {{ button.label }}
      </eds-button>
    }
  </div>
</div>
