import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  HostListener,
  input,
  model,
  signal,
  viewChild,
} from '@angular/core';
import { OnInit } from '@angular/core';
@Component({
  selector: 'widget-cms-device-images',
  templateUrl: './device-images.component.html',
  styleUrls: ['./device-images.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CmsDeviceImagesComponent implements OnInit {
  productName = input<string>('Product Name');
  image = model<string>('assets/images/iphone16promax.png');
  images = input<{ src: string; id: string }[]>([
    { src: 'assets/images/iphone16promax.png', id: '1' },
    { src: 'assets/images/iphone16promax-side.png', id: '2' },
    { src: 'assets/images/iphone16promax.png', id: '3' },
    { src: 'assets/images/iphone16promax-side.png', id: '4' },
    { src: 'assets/images/iphone16promax.png', id: '5' },
    { src: 'assets/images/iphone16promax-side.png', id: '6' },
    { src: 'assets/images/iphone16promax.png', id: '7' },
  ]);

  showMore = viewChild<HTMLImageElement>('showMore');

  currentImageSrc = signal<string>('assets/images/iphone16promax.png');
  currentImageIndex = signal<number>(0);
  sliderVisible = signal<boolean>(false);

  ngOnInit(): void {
    this.showMore()?.setAttribute('data-count', String(this.images().length - 3));
  }

  selectImage(id: string): void {
    const selectedImage = this.images().find((image) => image.id === id);
    if (selectedImage) {
      this.image.set(selectedImage.src);
      this.currentImageSrc.set(selectedImage.src);

      const index = this.images().findIndex((img) => img.id === id);
      if (index >= 0) {
        this.currentImageIndex.set(index);
      }
    }
  }

  openImageSlider(startIndex?: number): void {
    if (startIndex !== undefined) {
      this.currentImageIndex.set(startIndex);
      const selectedImage = this.images()[startIndex];
      if (selectedImage) {
        this.currentImageSrc.set(selectedImage.src);
        this.image.set(selectedImage.src);
      }
    }
    this.sliderVisible.set(true);
  }

  prevImage(): void {
    const imagesCount = this.images().length;
    const newIndex = (this.currentImageIndex() - 1 + imagesCount) % imagesCount;
    this.currentImageIndex.set(newIndex);
    this.currentImageSrc.set(this.images()[newIndex].src);
  }

  nextImage(): void {
    const imagesCount = this.images().length;
    const newIndex = (this.currentImageIndex() + 1) % imagesCount;
    this.currentImageIndex.set(newIndex);
    this.currentImageSrc.set(this.images()[newIndex].src);
  }

  @HostListener('document:keydown.escape')
  handleEscapeKey(): void {
    if (this.sliderVisible()) {
      this.closeSlider();
    }
  }

  closeSlider(): void {
    this.sliderVisible.set(false);
  }
}
