.base {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-600);

  .product {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: var(--eds-spacing-400);
  }

  .product-info {
    display: flex;
    flex-direction: column;
    gap: var(--eds-spacing-200);
  }

  .price {
    display: flex;
    align-items: baseline;
  }

  .availability {
    --color: var(--eds-colors-primary-light);
    display: flex;
    align-items: center;
    gap: var(--eds-spacing-200);

    .indicator {
      display: block;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background-color: var(--color);
      flex-shrink: 0;
    }

    &.available {
      --color: var(--eds-colors-success-light);
    }

    &.pre-order {
      --color: var(--eds-colors-warning-light);
    }

    &.out-of-stock {
      --color: var(--eds-colors-error-light);
    }
  }

  .product {
    display: flex;
    flex-direction: column;
    gap: var(--eds-spacing-400);
    padding-bottom: var(--eds-spacing-400);
    border-bottom: 1px solid var(--eds-border-color-light);
  }

  .colors {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: var(--eds-spacing-400);

    eds-radio-group::part(items) {
      position: relative;
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: var(--eds-spacing-200);
    }

    .color-key {
      color: var(--eds-colors-text-light);
    }

    eds-radio {
      width: calc(var(--eds-size-multiplier) * 10);
      height: calc(var(--eds-size-multiplier) * 10);

      &::part(checkmark) {
        position: relative;
        width: calc(var(--eds-size-multiplier) * 10);
        height: calc(var(--eds-size-multiplier) * 10);
        border-radius: 50%;
        background-color: var(--color);
        border: 1px solid var(--eds-border-color-light);

        &::after {
          display: none;
        }
      }
    }
  }

  .storage,
  .installment {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: var(--eds-spacing-400);

    eds-radio-group::part(items) {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      gap: var(--eds-spacing-200);
    }

    eds-radio {
      &[isChecked] {
        &::part(content) {
          --background-color: var(--eds-colors-primary-light);
        }
      }

      &::part(radio) {
        display: none;
      }
    }

    label {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: var(--eds-spacing-400);
      border-radius: var(--eds-radius-300);
      border: 1px solid var(--eds-border-color-light);
      min-width: calc(var(--eds-size-multiplier) * 19);
      background-color: var(--background-color);
    }
  }

  .installment-info {
    display: flex;
    align-items: center;
    gap: var(--eds-spacing-400);
    flex-wrap: wrap;

    @media (max-width: 834px) {
      flex-direction: column;
      align-items: flex-start;
    }

    .installment-info-item {
      display: flex;
      align-items: center;
      gap: var(--eds-spacing-200);
      flex-shrink: 0;

      eds-icon {
        width: var(--eds-sizing-600);
        height: var(--eds-sizing-600);
      }
    }
  }
}
