<div class="base">
  <div class="product">
    @for (tag of tags(); track $index) {
      <eds-tag [content]="tag.text" [appearance]="tag.appearance"></eds-tag>
    }
    <div class="product-info">
      <eds-text size="lg" weight="medium" [text]="name()"></eds-text>
      <div class="price">
        <eds-heading size="md" [text]="price()"></eds-heading>
        <eds-text size="md" [text]="'/' + period()"></eds-text>
      </div>
      <div class="availability">
        <span [class]="'indicator ' + availability()"></span>
        <eds-text size="sm" [text]="availability()"></eds-text>
      </div>
    </div>
  </div>
  <div class="colors">
    <eds-text size="lg" weight="medium" [text]="'Which color would you like?'"></eds-text>
    <eds-radio-group>
      @for (color of colors(); track $index) {
        <eds-radio
          style="--color: {{ color.colorCode }}"
          name="color"
          [value]="color.name"
          [isChecked]="color.isSelected"
          [isDisabled]="!color.isAvailable"
          (radio-change)="selectColor(color.name)"
        >
        </eds-radio>
      }
    </eds-radio-group>
    <div class="choosen-color">
      <eds-text class="color-key" size="sm" [text]="'Color:'"></eds-text>
      <eds-text class="color-name" size="sm" [text]="choosenColor()?.name"></eds-text>
    </div>
  </div>
  <div class="storage">
    <eds-text size="lg" weight="medium" [text]="'How much storage do you need?'"></eds-text>
    <eds-radio-group>
      @for (storage of storages(); track $index) {
        <eds-radio id="storage-{{ storage }}" name="storage" [value]="storage">
          <label for="storage-{{ storage }}">
            <eds-text size="md" [text]="storage"></eds-text>
          </label>
        </eds-radio>
      }
    </eds-radio-group>
  </div>
  <div class="installment">
    <eds-text size="lg" weight="medium" [text]="'Installment Plan'"></eds-text>
    <eds-radio-group>
      @for (installment of installments(); track $index) {
        <eds-radio id="installment-{{ installment }}" name="installment" [value]="installment">
          <label for="installment-{{ installment }}">
            <eds-text size="md" [text]="installment"></eds-text>
          </label>
        </eds-radio>
      }
    </eds-radio-group>
    <div class="installment-info">
      @for (info of installmentInfo(); track $index) {
        <div class="installment-info-item">
          <eds-icon [name]="info.icon"></eds-icon>
          <eds-text size="md" [text]="info.text"></eds-text>
        </div>
      }
    </div>
  </div>
  <widget-cms-plan-preview-card
    [plan]="plan().plan"
    [properties]="plan().properties"
    [price]="plan().price"
    [continueButton]="false"
  ></widget-cms-plan-preview-card>
  <eds-button shouldFitContainer appearance="primary">Proceed with /month</eds-button>
</div>
