import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';
import { CmsMixAndMatchDescription, CmsMixAndMatchMixer } from '../../../model';

@Component({
  selector: 'widget-cms-mix-and-match',
  templateUrl: './mix-and-match.component.html',
  styleUrls: ['./mix-and-match.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CmsMixAndMatchComponent {
  description = input<CmsMixAndMatchDescription[]>([]);
  mixer = input<CmsMixAndMatchMixer[]>([]);
  currency = input<string>('');
  price = input<number>(0);
  sliderChange = output<{ label: string; value: number }>();
  joinNow = output<void>();

  onSliderChange(label: string, event: CustomEvent<{ value: number }>) {
    this.sliderChange.emit({ label, value: event.detail.value });
  }

  onJoinNow() {
    this.joinNow.emit();
  }
}
