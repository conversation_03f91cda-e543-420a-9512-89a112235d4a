import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { TopOffer } from '../../../../model';

@Component({
  selector: 'widget-cms-top-offer',
  templateUrl: './top-offer.component.html',
  styleUrls: ['./top-offer.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [],
})
export class TopOfferComponent {
  topOffer = input<TopOffer>();
}
