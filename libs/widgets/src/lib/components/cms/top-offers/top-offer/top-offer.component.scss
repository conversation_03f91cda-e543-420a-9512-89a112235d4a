.card {
  height: 400px;
  border-radius: var(--eds-radius-600);
  background-color: lightgray;
  background-position: 50%;
  background-size: cover;
  background-repeat: no-repeat;

  display: flex;
  align-items: center;
  justify-content: center;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: var(--eds-spacing-600);
  flex-shrink: 0;
  width: 488px;
  height: 336px;
}

.text-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: var(--eds-spacing-400);
  flex: 1 0 0;
  align-self: stretch;

  eds-heading::part(base) {
    font-size: calc(var(--eds-size-multiplier) * 8);
    line-height: calc(var(--eds-size-multiplier) * 10);
    font-weight: 700;
  }
}

.button-container {
  eds-link::part(base) {
    cursor: pointer;
    text-decoration: none;
    color: inherit;
  }
}
