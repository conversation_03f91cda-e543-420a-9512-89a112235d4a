.base {
  display: grid;
  place-items: center;
  padding: var(--eds-spacing-200) var(--eds-spacing-400);
  border-radius: 0 var(--eds-radius-600) 0 var(--eds-radius-600);

  eds-text::part(base) {
    color: var(--eds-colors-text-white);
  }

  &.green {
    background-color: var(--eds-colors-success-default);
  }

  &.orange {
    background-color: var(--eds-colors-secondary-default);
  }

  &.blue {
    background-color: var(--eds-colors-info-default);
  }
}
