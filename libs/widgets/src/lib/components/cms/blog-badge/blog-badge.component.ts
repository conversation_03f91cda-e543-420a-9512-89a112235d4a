import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { CmsBlogBadgeTypes } from '../../../model';

@Component({
  selector: 'widget-cms-blog-badge',
  templateUrl: './blog-badge.component.html',
  styleUrls: ['./blog-badge.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CmsBlogBadgeComponent {
  label = input('');
  type = input<CmsBlogBadgeTypes>('green');
}
