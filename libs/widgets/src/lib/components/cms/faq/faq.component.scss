.base {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--eds-spacing-900);
}

:host ::ng-deep eds-accordion:not(:last-of-type) {
  border-bottom: 1px solid var(--eds-border-color-light);
  padding-bottom: var(--eds-spacing-600);
}

.buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--eds-spacing-400);
  width: 100%;
}

eds-accordion::part(content) {
  color: var(--eds-colors-text-light);
}

eds-button::part(base) {
  --button-padding: var(--eds-spacing-600);
  --eds-button-default-border-color: var(--eds-border-color-light);
  --eds-button-border-radius: var(--eds-radius-400);
  --eds-button-justify-content: space-between;

  width: 100%;
  height: unset;
}

eds-button::part(wrapper) {
  width: 100%;
}
