<div class="base">
  <eds-heading [text]="title()" size="sm"></eds-heading>
  <widget-accordion-group>
    @for (faq of faqs(); track faq.question) {
      <eds-accordion [caption]="faq.question" [isOpen]="faq.isOpen" appearance="secondary">
        <span [innerHTML]="faq.answer | safe"></span>
      </eds-accordion>
    }
  </widget-accordion-group>
  <div class="buttons">
    @for (button of buttons(); track button.label) {
      <eds-button (button-click)="button.action()" iconTrailing="arrowUpRight" appearance="default">
        {{ button.label }}
      </eds-button>
    }
  </div>
</div>
