import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';

@Component({
  selector: 'widget-cms-sub-offer',
  templateUrl: './sub-offer.component.html',
  styleUrls: ['./sub-offer.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CmsSubOfferComponent {
  helperText = input('');
  title = input('');
  link = input('');
  href = input('');
  image = input('');
  imageAlt = input('');
}
