eds-tabs::part(base) {
  gap: 2rem;
}

eds-tabs::part(tab-list) {
  justify-content: flex-start;
}

eds-tabs::part(tab-panels) {
  container-type: inline-size;
}

eds-tab-panel::part(base) {
  display: grid;
  grid-template-columns: minmax(0, 100%);
  gap: 1.5rem;
}

@media (min-width: 834px) {
  eds-tab-panel::part(base) {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 100%));
    gap: 1.5rem;
  }
}

@media (min-width: 1440px) {
  eds-tab-panel::part(base) {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 100%));
    gap: 1.5rem;
  }
}

:host ::ng-deep {
  eds-tab {
    flex-shrink: 0;
  }
}

.show-more-button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: calc(var(--eds-size-multiplier) * 18);
  grid-column: 1 / -1;
  background-color: var(--eds-colors-body);
  border: 1px solid var(--eds-border-color-default);
  padding: var(--eds-spacing-600);
  border-radius: var(--eds-radius-400);
}

.show-more-button:hover {
  background-color: var(--eds-colors-primary-light);
}
