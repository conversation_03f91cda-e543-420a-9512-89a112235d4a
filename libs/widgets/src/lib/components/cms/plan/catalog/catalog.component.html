<eds-tabs appearance="line">
  @for (tab of visibleTabs(); track tab.title) {
    <eds-tab slot="tabs" id="tab{{ tab.title }}">
      {{ tab.title }}
    </eds-tab>
  }

  @for (tab of visibleTabs(); track tab.title) {
    <eds-tab-panel id="panel{{ tab.title }}" tab="tab{{ tab.title }}">
      @for (plan of tab.visiblePlans; track plan.title) {
        <widget-cms-plan-card
          [title]="plan.title"
          [subTitle]="plan.subTitle"
          [advantages]="plan.advantages"
          [appsTitle]="plan.appsTitle"
          [apps]="plan.apps"
          [price]="plan.price"
          [actions]="plan.actions"
          [tags]="plan.tags"
        ></widget-cms-plan-card>
      }

      @if (tab.hasMorePlans) {
        <button class="show-more-button" (click)="loadMore(tab.title)">
          <eds-text size="md" text="Show more plans"></eds-text>
          <eds-icon name="arrowDown"></eds-icon>
        </button>
      }
    </eds-tab-panel>
  }
</eds-tabs>
