.base {
  display: flex;
  flex-direction: column;
  border-radius: var(--eds-sizing-400);
  background-color: var(--eds-colors-surface-default);
  overflow: hidden;
  border: var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-light);
}

.details {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-600);
  padding: var(--eds-spacing-600);
  border-bottom: var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-light);
}

.advantages-wrapper {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-300);
}

.advantages {
  display: grid;
  gap: var(--eds-spacing-400);

  @media (min-width: 834px) {
    grid-template-columns: repeat(3, 1fr);
  }
}

.advantage {
  display: flex;
  align-items: center;
  gap: var(--eds-spacing-300);
  background-color: var(--eds-colors-surface-level-1);
  padding: var(--eds-spacing-400) var(--eds-spacing-300);
  border-radius: var(--eds-radius-400);
  border: var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-light);

  eds-icon {
    width: var(--eds-sizing-600);
    height: var(--eds-sizing-600);
    color: var(--eds-colors-secondary-light);
    flex-shrink: 0;
  }

  eds-image {
    width: var(--eds-sizing-600);
    height: var(--eds-sizing-600);
    color: var(--eds-colors-secondary-light);
    flex-shrink: 0;
  }
  .advantage-content {
    display: grid;
  }
  .amount-wrapper {
    display: flex;
    align-items: baseline;
    gap: var(--eds-spacing-100);
  }
}

.apps-wrapper,
.benefits-wrapper {
  padding-bottom: var(--eds-spacing-600);
  border-bottom: var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-light);
}
.apps-wrapper {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-300);
}

.apps {
  display: flex;
  gap: var(--eds-spacing-200);

  eds-image {
    width: var(--eds-sizing-600);
    height: var(--eds-sizing-600);
  }
}

.benefits-wrapper {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-300);
}

.benefits {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--eds-spacing-300);
}

.benefit {
  display: flex;
  align-items: center;
  gap: var(--eds-spacing-200);

  eds-icon {
    width: var(--eds-sizing-400);
    height: var(--eds-sizing-400);
    flex-shrink: 0;
  }
}

.information-wrapper {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-300);
}

.information {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-200);
  margin: 0;
  padding-inline-start: 1rem;
}

eds-alert::part(icon) {
  width: var(--eds-sizing-400);
  height: var(--eds-sizing-400);
}

eds-alert::part(wrapper) {
  align-items: center;
}

.commitment-wrapper {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-300);
}

.commitment {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  gap: var(--eds-spacing-400);

  @media (min-width: 834px) {
    flex-direction: row;
  }
}

.price-wrapper {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-200);
}

.price-title {
  --text-color: var(--eds-colors-text-light);
}

.price {
  display: flex;
  align-items: center;
  gap: var(--eds-spacing-200);

  eds-icon {
    width: var(--eds-sizing-800);
    height: var(--eds-sizing-800);
    flex-shrink: 0;
  }
}

.total-price-wrapper {
  display: flex;
  align-items: baseline;

  .current-price {
    &.desktop {
      display: none;
    }

    @media (min-width: 834px) {
      &.desktop {
        display: block;
      }

      &.mobile {
        display: none;
      }
    }
  }
}

.price-value-wrapper {
  --text-color: var(--eds-colors-text-light);
  .price-value {
    --text-decoration: line-through;
  }
}

.order {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  gap: var(--eds-spacing-400);
  padding: var(--eds-spacing-600);

  @media (min-width: 834px) {
    flex-direction: row;
    align-items: center;
  }

  eds-button {
    display: flex;
    justify-content: flex-end;
    width: 100%;

    @media (min-width: 834px) {
      width: auto;
    }
  }
}
