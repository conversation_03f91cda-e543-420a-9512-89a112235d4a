<div class="base">
  <div class="details">
    <div class="advantages-wrapper">
      <eds-text size="lg" [text]="advantagesTitle()"></eds-text>
      <div class="advantages">
        @for (advantage of advantages(); track $index) {
          <div class="advantage">
            @if (advantage.icon.isIcon) {
              <eds-icon [name]="advantage.icon.value"></eds-icon>
            } @else {
              <eds-image [src]="advantage.icon.value" [alt]="advantage.title"></eds-image>
            }
            <div class="advantage-content">
              <eds-text size="md" [text]="advantage.type"></eds-text>
              <div class="amount-wrapper">
                <eds-heading size="sm" [text]="advantage.amount"></eds-heading>
                <eds-text size="sm" [text]="advantage.unit"></eds-text>
              </div>
            </div>
          </div>
        }
      </div>
    </div>
    @if (apps()?.length > 0) {
      <div class="apps-wrapper">
        <eds-text size="lg" [text]="appsTitle()"></eds-text>
        <div class="apps">
          @for (app of apps(); track $index) {
            <eds-image [src]="app.image" [alt]="app.alt"></eds-image>
          }
        </div>
      </div>
    }
    @if (benefits().length > 0) {
      <div class="benefits-wrapper">
        <eds-text size="lg" [text]="benefitsTitle()"></eds-text>
        <div class="benefits">
          @for (benefit of benefits(); track $index) {
            <div class="benefit">
              <eds-icon name="check"></eds-icon>
              <eds-text [text]="benefit"></eds-text>
            </div>
          }
        </div>
      </div>
    }
    @if (information().length > 0) {
      <div class="information-wrapper">
        <eds-text size="lg" [text]="informationTitle()"></eds-text>
        <ul class="information">
          @for (information of information(); track $index) {
            <li class="information-item">
              <eds-text [text]="information"></eds-text>
            </li>
          }
        </ul>
      </div>
    }
    @for (info of additionalInformation(); track $index) {
      <eds-alert
        showIcon="true"
        [appearance]="info.appearance"
        [iconName]="info.icon"
        [description]="info.description"
      ></eds-alert>
    }
    @if (commitment().length) {
      <div class="commitment-wrapper">
        <eds-text size="lg" [text]="commitmentTitle()"></eds-text>
        <div class="commitment">
          @for (commitment of commitment(); track $index) {
            <eds-button
              [appearance]="selectedCommitmentIndex() === $index ? 'default' : 'subtle'"
              (click)="selectCommitment($index)"
            >
              {{ commitment.title }}
            </eds-button>
          }
        </div>
      </div>
    }
  </div>
  <div class="order">
    <div class="price-wrapper">
      <eds-text size="lg" class="price-title" [text]="priceTitle()"></eds-text>
      <div class="price">
        <eds-icon name="coins"></eds-icon>
        <div class="total-price-wrapper">
          <eds-heading size="xl" class="current-price desktop" [text]="price() | currency: currency()"></eds-heading>
          <eds-heading size="md" class="current-price mobile" [text]="price() | currency: currency()"></eds-heading>
          <div class="price-value-wrapper">
            <eds-text size="lg" text="/mth"></eds-text>
            @if (selectedCommitment()?.discountPrice) {
              <eds-text
                class="price-value"
                size="lg"
                [text]="selectedCommitment()?.price | currency: currency()"
              ></eds-text>
            }
          </div>
        </div>
      </div>
      @if (selectedCommitment()?.period) {
        <eds-text
          size="sm"
          class="price-description"
          [text]="
            'For ' +
            selectedCommitment()?.period +
            ' months, Then ' +
            (selectedCommitment()?.discountPrice | currency: currency())
          "
        ></eds-text>
      }
    </div>
    <eds-button shouldFitContainer appearance="primary" (button-click)="orderNow.emit()"
      >{{ orderButton() }}
    </eds-button>
  </div>
</div>
