.base {
  display: grid;
  gap: var(--eds-spacing-600);
  width: 100%;
  .devices {
    display: flex;
    flex-direction: column;
    gap: var(--eds-spacing-600);
    padding: calc(var(--eds-size-multiplier) * 8) 0;
    width: 100%;

    @media (min-width: 834px) {
      padding: calc(var(--eds-size-multiplier) * 12) 0;
    }

    @media (min-width: 1440px) {
      padding: calc(var(--eds-size-multiplier) * 16) 0;
    }

    .devices-list {
      display: grid;
      grid-template-columns: repeat(1, minmax(232px, 1fr));
      align-items: center;
      row-gap: var(--eds-spacing-800);
      column-gap: var(--eds-spacing-600);

      @media (min-width: 834px) {
        grid-template-columns: repeat(2, minmax(232px, 1fr));
      }

      @media (min-width: 1440px) {
        grid-template-columns: repeat(4, minmax(232px, 1fr));
      }

      widget-cms-device-card {
        height: 100%;
      }
    }

    .devices-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: var(--eds-spacing-400);
    }
  }
  .show-more-button {
    display: flex;
    align-items: center;
    justify-content: center;
    height: calc(var(--eds-size-multiplier) * 18);
    grid-column: 1 / -1;
    background-color: var(--eds-colors-body);
    border: 1px solid var(--eds-border-color-default);
    padding: var(--eds-spacing-600);
    border-radius: var(--eds-radius-400);
  }

  .show-more-button:hover {
    background-color: var(--eds-colors-primary-light);
  }
}
