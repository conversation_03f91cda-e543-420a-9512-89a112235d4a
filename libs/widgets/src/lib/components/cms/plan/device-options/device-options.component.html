<div class="base">
  <div class="devices">
    <div class="devices-info">
      <eds-text size="md" [text]="itemAmount()"></eds-text>
    </div>
    <div class="devices-list">
      @for (device of visibleDevices(); track $index) {
        <widget-cms-device-card
          [deviceId]="device.id"
          [brand]="device.brand"
          [model]="device.model"
          [price]="device.price"
          [oldPrice]="device.oldPrice"
          [deviceImage]="device.deviceImage"
          [tags]="device.tags"
          (onSelect)="onSelect.emit($event)"
        >
        </widget-cms-device-card>
      }
    </div>
  </div>
  @if (hasMoreDevices()) {
    <button class="show-more-button" (click)="loadMore()">
      <eds-text size="md" text="Show more plans"></eds-text>
      <eds-icon name="arrowDown"></eds-icon>
    </button>
  }
</div>
