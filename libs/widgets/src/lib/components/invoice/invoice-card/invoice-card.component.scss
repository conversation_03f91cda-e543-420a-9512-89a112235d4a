:host {
  --eds-invoice-card-padding: var(--invoice-card-padding, var(--eds-spacing-400));
  --eds-invoice-card-gap: var(--invoice-card-gap, var(--eds-spacing-400));
  --eds-invoice-card-border-radius: var(--invoice-card-border-radius, var(--eds-sizing-300));

  --eds-invoice-card-title-gap: var(--invoice-card-title-gap, var(--eds-spacing-200));
  --eds-invoice-card-title-justify-content: var(--invoice-card-title-justify-content, space-between);
  --eds-invoice-card-title-align-items: var(--invoice-card-title-align-items, flex-start);
  --eds-invoice-card-title-height: var(--invoice-card-title-height, unset);
  --eds-invoice-card-title-direction: var(--invoice-card-title-direction, column);
  --eds-invoice-card-background: var(--invoice-card-background, transparent);
  --eds-invoice-card-content-gap: var(--invoice-card-content-gap, var(--eds-spacing-200));
  --eds-invoice-card-buttons-gap: var(--invoice-card-buttons-gap, var(--eds-spacing-200));
  --eds-invoice-card-content-direction: var(--invoice-card-content-direction, column);

  --eds-invoice-card-border-properties: var(
    --invoice-card-border-properties,
    var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default)
  );
}

@media (min-width: 834px) {
  :host {
    --eds-invoice-card-content-direction: var(--invoice-card-content-direction, column);
    --eds-invoice-card-title-direction: var(--invoice-card-title-direction, row);
    --eds-invoice-card-title-align-items: var(--invoice-card-title-align-items, center);
    --eds-invoice-card-title-height: var(--invoice-card-title-height, var(--eds-sizing-900));
  }
}

.base,
[part='base'] {
  display: flex;
  border: var(--eds-invoice-card-border-properties);
  border-radius: var(--eds-invoice-card-border-radius);
  padding: var(--eds-invoice-card-padding);
  flex-direction: var(--eds-invoice-card-content-direction);
  gap: var(--eds-invoice-card-gap);
  background-color: var(--eds-invoice-card-background);
}

.header,
[part='header'] {
  display: flex;
  flex-direction: column;
}

.title,
[part='title'] {
  display: flex;
  flex-direction: var(--eds-invoice-card-title-direction);
  justify-content: var(--eds-invoice-card-title-justify-content);
  align-items: var(--eds-invoice-card-title-align-items);
  gap: var(--eds-invoice-card-title-gap);
  height: var(--eds-invoice-card-title-height);
}

.information,
[part='information'] {
  display: flex;
  flex-direction: row;
  gap: var(--eds-invoice-card-content-gap);
}

.buttons,
[part='buttons'] {
  display: flex;
  align-items: center;
  gap: var(--eds-invoice-card-buttons-gap);
}
