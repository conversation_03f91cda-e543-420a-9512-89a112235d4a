import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';

@Component({
  selector: 'widget-invoice-card',
  templateUrl: './invoice-card.component.html',
  styleUrls: ['invoice-card.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InvoiceCardComponent {
  title = input('');
  paymentDueDateLabel = input('');
  paymentDueDate = input('');
  amount = input('');
  detailLink = input('');
  detailLinkText = input('');
  paymentLink = input('');
  paymentLinkText = input('');
  isPaymentCompleted = input(false);
  isPaymentOverdue = input(false);
}
