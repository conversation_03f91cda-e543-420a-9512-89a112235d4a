:host {
  --eds-carousel-pagination-bottom: var(--carousel-pagination-bottom, calc(var(--eds-sizing-400) * -1));
  --eds-carousel-bottom-spacing: var(--carousel-bottom-spacing, var(--eds-spacing-200));
  --eds-carousel-pagination-height: var(--carousel-pagination-height, var(--eds-sizing-200));
  --eds-carousel-pagination-color: var(--carousel-pagination-color, var(--eds-colors-primary-dark));

  padding-bottom: var(--eds-carousel-bottom-spacing);
}

swiper-container::part(container) {
  overflow: visible;
  padding-bottom: var(--eds-carousel-bottom-spacing);
}

swiper-container::part(pagination) {
  height: var(--eds-carousel-pagination-height, var(--eds-sizing-200));
}

swiper-container::part(bullet-active) {
  --swiper-pagination-color: var(--eds-carousel-pagination-color);
}

swiper-slide {
  overflow: hidden;
}
