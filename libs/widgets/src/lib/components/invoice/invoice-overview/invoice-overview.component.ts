import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { CarouselItem } from '../../../model/carousel-item.model';
import { InvoiceCarouselComponent } from '../invoice-carousel/invoice-carousel.component';
import { Alert } from '../../../model';

@Component({
  selector: 'widget-invoice-overview',
  templateUrl: './invoice-overview.component.html',
  styleUrls: ['./invoice-overview.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [InvoiceCarouselComponent],
})
export class InvoiceOverviewComponent {
  alert = input<Alert>({
    showIcon: false,
    iconName: '',
    title: '',
    description: '',
    appearance: 'info',
  });

  carouselPagination = input(false);

  carouselItems = input<CarouselItem[]>([]);
}
