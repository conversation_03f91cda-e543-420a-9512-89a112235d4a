import { Component, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';
import { TranslatePipe } from '@libs/plugins';
import { SelectOption } from '../../../model';

@Component({
  selector: 'widget-plan-change-cart-summary',
  templateUrl: './cart-summary.component.html',
  styleUrls: ['./cart-summary.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [TranslatePipe],
})
export class PlanChangeCartSummaryComponent {
  changeEffectiveDateOptions = input<SelectOption[]>([]);
  date = input<string>();
  deleteItem = output<void>();
  dateChanged = output<string>();

  showDateSelect = false;

  changeDateClick() {
    this.showDateSelect = true;
  }

  changeEffectiveDateClick(event: Event) {
    const customEvent = event as CustomEvent;
    const target = event.target as HTMLSelectElement;
    const selectedValue = customEvent?.detail?.value || target?.value;

    const selectedOption = this.changeEffectiveDateOptions().find((option) => option.value === selectedValue);

    if (selectedOption) {
      this.dateChanged.emit(selectedOption.label);
      this.showDateSelect = false;
    }
  }
}
