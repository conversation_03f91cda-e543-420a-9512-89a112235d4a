.base {
  display: flex;
  flex-direction: column;
  border: 1px solid var(--eds-border-color-default);
  border-radius: var(--eds-radius-300);
  background-color: var(--eds-colors-surface-default);

  .header {
    display: flex;
    flex-direction: column;
    gap: var(--eds-spacing-400);
    padding: var(--eds-spacing-300);
    background-color: var(--eds-colors-surface-disabled);
    border-radius: var(--eds-radius-300) var(--eds-radius-300) 0 0;
    --heading-text-color: var(--eds-colors-text-light);
    --text-color: var(--eds-colors-text-light);

    @media (min-width: 834px) {
      padding: var(--eds-spacing-600);
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    gap: var(--eds-spacing-400);
    padding: var(--eds-spacing-300);
    border-top: 1px solid var(--eds-border-color-default);

    .effectiveDate {
      display: flex;
      flex-direction: column;
      gap: var(--eds-spacing-300);
      border-radius: var(--eds-radius-200);
      background-color: var(--eds-colors-surface-level-1);
      padding: var(--eds-spacing-200);

      .input {
        display: flex;
        flex-direction: column;
        gap: var(--eds-spacing-200);
        flex: 1;

        .date {
          text-transform: capitalize;
        }
      }

      @media (min-width: 834px) {
        flex-direction: row;
        align-items: center;
        padding: var(--eds-spacing-400);
      }
    }

    @media (min-width: 834px) {
      padding: var(--eds-spacing-600);
    }
  }

  .legal-information {
    padding: var(--eds-spacing-300);
    border-top: 1px solid var(--eds-border-color-default);

    @media (min-width: 834px) {
      padding: var(--eds-spacing-400) var(--eds-spacing-600);
    }
  }

  .uppercase {
    text-transform: uppercase;
  }
}
