<div class="base">
  <div class="header">
    <eds-heading class="uppercase" as="h6" size="xs" [text]="'currentPlan' | translate"></eds-heading>
    <ng-content select="[currentPlan]"></ng-content>
  </div>
  <div class="content">
    <eds-heading as="h6" size="xs" [text]="'newPlan' | translate"></eds-heading>
    <ng-content></ng-content>
    <div class="effectiveDate">
      <div class="input">
        <eds-text [text]="'changeEffectiveDate' | translate"></eds-text>
        @if (showDateSelect) {
          <eds-select appearance="default" size="compact" (option-selected)="changeEffectiveDateClick($event)">
            @for (option of changeEffectiveDateOptions(); track $index) {
              <eds-select-option
                [value]="option.value"
                [label]="option.label"
                [isSelected]="option.isSelected"
                [isDisabled]="option.isDisabled"
              ></eds-select-option>
            }
          </eds-select>
        } @else {
          <eds-text class="date" [text]="date()"></eds-text>
        }
      </div>
      <eds-button [appearance]="'link'" (button-click)="changeDateClick()">Change Date</eds-button>
    </div>
  </div>
  <div class="legal-information">
    <ng-content select="[legalInformation]"></ng-content>
  </div>
</div>
