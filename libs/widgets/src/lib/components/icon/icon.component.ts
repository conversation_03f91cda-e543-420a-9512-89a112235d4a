import { NgComponentOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { Icon } from '../../model';

@Component({
  selector: 'widget-icon',
  templateUrl: './icon.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgComponentOutlet],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class IconComponent {
  icons = input<(Icon | string)[]>([]);

  icon = input<Icon | string>();

  computedIcons = computed(() => [...this.icons(), this.icon()].filter(Boolean));

  isStringType(icon: Icon | string) {
    return typeof icon === 'string';
  }
}
