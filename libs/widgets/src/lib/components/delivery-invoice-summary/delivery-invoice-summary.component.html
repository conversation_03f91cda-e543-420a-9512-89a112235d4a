<div class="base">
  @if (deliveryAddress() && showDeliveryInfo()) {
    <div class="delivery-address">
      <eds-icon name="location" class="icon"></eds-icon>
      <eds-text class="label" as="p" size="lg" weight="medium" text="{{ 'deliveryAddress' | translate }}:"></eds-text>
      <eds-text class="value" as="p" size="lg" weight="regular" [text]="deliveryAddress()"></eds-text>
    </div>
  }

  @if (deliveryOption() && showDeliveryInfo()) {
    <div class="delivery-options">
      <eds-icon name="shippingTruck" class="icon"></eds-icon>
      <eds-text class="label" as="p" size="lg" weight="medium" text="{{ 'deliveryOptions' | translate }}:"></eds-text>
      <eds-text class="value" as="p" size="lg" weight="regular" [text]="deliveryOption().offerName"></eds-text>
      <div class="delivery-option-details">
        <div class="delivery-option-detail">
          <eds-text
            class="delivery-option-detail-key"
            size="sm"
            weight="regular"
            text="{{ 'estimatedDeliveryDate' | translate }}:"
          ></eds-text>
          <eds-text size="sm" weight="regular" [text]="deliveryOption().deliveryDays"></eds-text>
        </div>
        <div class="delivery-option-detail">
          <eds-text
            class="delivery-option-detail-key"
            size="sm"
            weight="regular"
            text="{{ 'estimatedDeliveryTime' | translate }}:"
          ></eds-text>
          <eds-text size="sm" weight="regular" [text]="deliveryOption().deliveryTime"></eds-text>
        </div>
      </div>
    </div>
  }
  <widget-invoice-billing-summary [summary]="invoiceAccount()"></widget-invoice-billing-summary>
</div>
