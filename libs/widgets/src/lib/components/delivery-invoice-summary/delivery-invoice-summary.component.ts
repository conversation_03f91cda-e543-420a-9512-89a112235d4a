import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { TranslatePipe } from '@libs/plugins';
import { Delivery } from '../../model';
import { InvoiceBillingSummaryComponent } from './invoice-billling-summary/invoice-billing-summary.component';

@Component({
  selector: 'widget-delivery-invoice-summary',
  templateUrl: './delivery-invoice-summary.component.html',
  styleUrls: ['./delivery-invoice-summary.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [TranslatePipe, InvoiceBillingSummaryComponent],
})
export class DeliveryInvoiceSummaryComponent {
  showDeliveryInfo = input(true);
  deliveryAddress = input<string>();
  deliveryOption = input<Delivery.DeliverOptionSummaryCard>();
  invoiceAccount = input<Delivery.InvoiceAccountSummaryCard>();
}
