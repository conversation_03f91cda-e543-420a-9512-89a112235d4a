<div class="invoice-account">
  <eds-icon name="location" class="icon"></eds-icon>
  <eds-text class="label" as="p" size="lg" weight="medium" text="{{ summary().label | translate }}:"></eds-text>
  <eds-text class="value" as="p" size="lg" weight="regular" [text]="summary().title"></eds-text>
  <div class="invoice-account-details">
    <div class="invoice-account-detail">
      <eds-text
        class="invoice-account-detail-key"
        size="sm"
        weight="regular"
        [text]="'contactPerson' | translate"
      ></eds-text>
      <eds-text size="sm" weight="regular" [text]="summary().contactPerson"></eds-text>
    </div>
    @if (summary().phoneNo) {
      <div class="invoice-account-detail">
        <eds-text
          class="invoice-account-detail-key"
          size="sm"
          weight="regular"
          [text]="'phoneNo' | translate"
        ></eds-text>
        <eds-text size="sm" weight="regular" [text]="summary().phoneNo | phoneNumber"></eds-text>
      </div>
    }
    @if (summary().invoiceId) {
      <div class="invoice-account-detail">
        <eds-text
          class="invoice-account-detail-key"
          size="sm"
          weight="regular"
          [text]="'invoiceId' | translate"
        ></eds-text>
        <eds-text size="sm" weight="regular" [text]="summary().invoiceId"></eds-text>
      </div>
    }
  </div>
</div>
