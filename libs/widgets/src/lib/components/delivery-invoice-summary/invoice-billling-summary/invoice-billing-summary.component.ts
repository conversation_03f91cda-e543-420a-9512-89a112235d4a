import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { Delivery } from '@libs/widgets';
import { TranslatePipe } from '@libs/plugins';
import { PhoneNumberPipe } from '@libs/core';

@Component({
  selector: 'widget-invoice-billing-summary',
  templateUrl: './invoice-billing-summary.component.html',
  styleUrls: ['./invoice-billing-summary.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [TranslatePipe, PhoneNumberPipe],
})
export class InvoiceBillingSummaryComponent {
  summary = input<Delivery.InvoiceAccountSummaryCard>();
}
