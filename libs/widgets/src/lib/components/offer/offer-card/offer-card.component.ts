import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';

@Component({
  selector: 'widget-offer-card',
  templateUrl: './offer-card.component.html',
  styleUrls: ['offer-card.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OfferCardComponent {
  title = input('');
  description = input('');
  linkText = input('');
  image = input('');
  fallbackImage = input('');
  headingSize = input('');
  descriptionMaxLines = input<number>();
  link = input('');
  linkSrc = input('');
}
