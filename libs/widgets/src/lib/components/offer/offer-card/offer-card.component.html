<div class="base">
  <eds-image width="100%" [src]="image()" [fallbackSrc]="fallbackImage()" [alt]="title()" class="image"></eds-image>
  <div class="content">
    <eds-heading as="h3" size="headingSize()" [text]="title()" class="title"></eds-heading>
    <eds-text
      as="span"
      size="lg"
      [maxLines]="descriptionMaxLines()"
      [text]="description()"
      class="description"
    ></eds-text>
    <eds-button [appearance]="link()" [href]="linkSrc()" class="link">
      <eds-link>
        {{ linkText() }}
      </eds-link>
    </eds-button>
  </div>
</div>
