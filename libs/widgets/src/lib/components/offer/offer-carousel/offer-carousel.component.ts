import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { SwiperOptions } from '../../../model/swiper.model';
import { OfferCarousel } from '../../../model/offer-carousel.model';
import { OfferCardComponent } from '../offer-card/offer-card.component';
import { SwiperComponent } from '../../swiper/swiper.component';

@Component({
  selector: 'widget-offer-carousel',
  templateUrl: './offer-carousel.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  styleUrls: ['./offer-carousel.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [OfferCardComponent, SwiperComponent],
})
export class OfferCarouselComponent {
  title = input('');

  items = input<OfferCarousel[]>([]);

  swiperConfig = input<SwiperOptions>({
    spaceBetween: 16,
    slidesPerView: 1,
    loop: false,
    pagination: true,
  });

  pagination = input<boolean>(false);

  config = computed<SwiperOptions>(() => ({
    ...this.swiperConfig(),
    pagination: this.pagination(),
  }));
}
