:host {
  --eds-carousel-pagination-bottom: var(--carousel-pagination-bottom, calc(var(--eds-size-multiplier) * -4));
  --eds-carousel-bottom-spacing: var(--carousel-bottom-spacing, var(--eds-spacing-600));
  --eds-carousel-pagination-height: var(--carousel-pagination-height, var(--eds-sizing-200));
  --eds-carousel-pagination-color: var(--carousel-pagination-color, var(--eds-colors-primary-dark));
  --eds-carousel-slide-box-shadow: var(--carousel-slide-box-shadow, var(--eds-shadow-sm));
  --eds-carousel-slide-border-radius: var(--carousel-slide-border-radius, var(--eds-radius-600));
}

.base {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-400);
}

swiper-container::part(container) {
  overflow: visible;
  padding-bottom: var(--eds-carousel-bottom-spacing);
}

swiper-container::part(pagination) {
  height: var(--eds-carousel-pagination-height);
}

swiper-container::part(bullet-active) {
  --swiper-pagination-color: var(--eds-carousel-pagination-color);
}

swiper-slide {
  overflow: hidden;
  box-shadow: var(--eds-carousel-slide-box-shadow);
  border-radius: var(--eds-carousel-slide-border-radius);
}
