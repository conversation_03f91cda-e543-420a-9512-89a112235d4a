<div class="base" #base>
  <div class="language-preference-container">
    <ng-content class="language-preference" select="[languagePreference]"></ng-content>
  </div>
  <div class="content">
    <a class="brand" [href]="logoHref()">
      <eds-image [src]="logoSrc()" [alt]="brandName()"></eds-image>
    </a>
    @if (loggedIn()) {
      <div class="actions">
        <ng-content select="[search]"> ></ng-content>
        <ng-content select="[notification]"></ng-content>
        <eds-button
          class="language-item"
          appearance="subtle"
          iconOnly
          iconLeading="internet"
          (button-click)="openLanguagePreference()"
        ></eds-button>
        <ng-content select="[login]"> ></ng-content>
      </div>
    }
  </div>
</div>
