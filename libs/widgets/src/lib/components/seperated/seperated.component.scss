::ng-deep .seperated {
  display: flex;
  flex-direction: column;

  > * {
    padding-top: var(--eds-spacing-400);
    padding-bottom: var(--eds-spacing-400);
    border-bottom: 1px solid var(--eds-border-color-default);

    @media (min-width: 834px) {
      padding-top: var(--eds-spacing-600);
      padding-bottom: var(--eds-spacing-600);
    }

    &:last-child {
      padding-bottom: 0;
      border-bottom: none;
    }
    &:first-child {
      padding-top: 0;
    }
  }
}
