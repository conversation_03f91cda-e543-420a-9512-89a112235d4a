:host {
  --eds-language-preference-background-color: var(
    --language-preference-background-color,
    var(--eds-colors-primary-default)
  );
  --eds-language-preference-text-color: var(--language-preference-text-color, var(--eds-colors-text-white));
  --eds-language-preference-close-icon-hover-color: var(
    --language-preference-close-icon-hover-color,
    var(--eds-colors-text-default)
  );
  --eds-language-preference-container-size: var(
    --language-preference-container-size,
    calc(var(--eds-size-multiplier) * 288)
  );

  --eds-language-preference-border-radius: var(--language-preference-border-radius, 0);
  --eds-language-preference-border-properties: var(--language-preference-border-properties, 0);
}

.base {
  display: flex;
  justify-content: center;
  background-color: var(--eds-language-preference-background-color);
  border-radius: var(--eds-language-preference-border-radius);
  border: var(--eds-language-preference-border-properties);
}

.content {
  display: grid;
  align-items: center;
  grid-template-areas:
    'text close'
    'actions actions';
  grid-template-columns: minmax(0, 100%) 1fr;
  width: 100%;
  gap: var(--eds-spacing-400);
  max-width: var(--eds-language-preference-container-size);
  padding: var(--eds-spacing-400);
}

.actions {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-400);
  grid-area: actions;
}

eds-text {
  color: var(--eds-language-preference-text-color);
  grid-area: text;
}

.language-preference-close {
  grid-area: close;
}

.language-preference-close::part(base) {
  color: var(--eds-language-preference-text-color);
}

.language-preference-close:hover::part(base) {
  color: var(--eds-language-preference-close-icon-hover-color);
}

@media (min-width: 834px) {
  .content {
    padding: var(--eds-spacing-400) var(--eds-spacing-600);
    grid-template-areas: 'text actions close';
    grid-template-columns: minmax(0, 100%) auto 1fr;
  }

  .actions {
    flex-direction: row;
    justify-content: center;
  }
}

@media (min-width: 1440px) {
  eds-select {
    flex-shrink: 0;
  }
}
