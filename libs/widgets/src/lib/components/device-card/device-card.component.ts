import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { DataList, Media } from '../../model';

@Component({
  selector: 'widget-device-card',
  templateUrl: './device-card.component.html',
  styleUrls: ['./device-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class DeviceCardComponent {
  deviceMedia = input<Media>({
    imageSrc: '',
    imageAlt: '',
    upperText: '',
    text: '',
    description: '',
  });

  dataList = input<DataList>({
    itemsSize: 5,
    trim: true,
    expandedText: '',
    unexpandedText: '',
    items: [],
  });
}
