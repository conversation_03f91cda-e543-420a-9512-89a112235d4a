:host {
  --eds-device-details-display: var(--device-details-display, flex);
  --eds-device-details-gap: var(--device-details-gap, var(--eds-spacing-600));

  --eds-device-details-media-object-image-border: var(
    --device-details-media-object-image-border,
    var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default)
  );
  --eds-device-details-media-object-image-background-color: var(
    --device-details-media-object-image-background-color,
    var(--eds-colors-surface-default)
  );
  --eds-device-details-media-object-image-width: var(--device-details-media-object-image-width, var(--eds-sizing-900));
  --eds-device-details-media-object-image-height: var(
    --device-details-media-object-image-height,
    var(--eds-sizing-900)
  );
  --eds-device-details-media-object-image-padding: var(
    --device-details-media-object-image-padding,
    var(--eds-spacing-200)
  );
  --eds-device-details-media-object-image-border-radius: var(
    --device-details-media-object-image-border-radius,
    var(--eds-radius-200)
  );
  --eds-device-details-media-object-gap: var(--device-details-media-object-gap, var(--eds-spacing-400));

  --eds-device-details-data-list-wrapper-gap: var(--device-details-data-list-wrapper-gap, var(--eds-spacing-600));
  --eds-device-details-data-list-wrapper-align-items: var(--device-details-data-list-wrapper-align-items, normal);
}

.base {
  display: var(--eds-device-details-display);
  flex-direction: column;
  gap: var(--eds-device-details-gap);
}

.device {
  --eds-media-object-gap: var(--eds-device-details-media-object-gap);
}

.device::part(image-wrapper) {
  border: var(--eds-device-details-media-object-image-border);
  border-radius: var(--eds-device-details-media-object-image-border-radius);
  background-color: var(--eds-device-details-media-object-image-background-color);
  width: var(--eds-device-details-media-object-image-width);
  height: var(--eds-device-details-media-object-image-height);
  padding: var(--eds-device-details-media-object-image-padding);
}

eds-data-list {
  --eds-data-list-wrapper-gap: var(--eds-device-details-data-list-wrapper-gap);

  &::part(item) {
    gap: var(--eds-spacing-400);
  }

  @media (max-width: 834px) {
    &::part(item) {
      flex-direction: column;
      align-items: flex-start;
    }
  }
}
