import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule, ValidationErrors } from '@angular/forms';
import {
  createPhoneNumberMask,
  FormFieldComponent,
  FormFieldErrorMessageComponent,
  TranslatePipe,
} from '@libs/plugins';
import { Phonenumber, SelectOption } from '../../model';

@Component({
  selector: 'widget-phonenumber-temp',
  templateUrl: './phone-number-component-temp.component.html',
  styleUrls: ['./phone-number-component-temp.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [ReactiveFormsModule, FormFieldComponent, TranslatePipe, FormFieldErrorMessageComponent],
})
export class PhoneNumberTempComponent {
  countryOptions = input<SelectOption[]>([]);
  required = input<boolean>(false);
  errors = input<ValidationErrors>(null);

  formEmitter = output<Phonenumber>();

  phoneNumberMask = createPhoneNumberMask();

  form = new FormGroup({
    country: new FormControl(''),
    phoneNumber: new FormControl('', []),
  });

  emitForm() {
    this.formEmitter.emit(this.form?.value);
  }

  constructor() {
    // effect(() => {
    //   if (this.required()) {
    //     this.form.get('country').setValidators([Validators.required]);
    //     this.form.get('country').updateValueAndValidity();
    //     this.form.get('phoneNumber').setValidators([Validators.required, Validators.minLength(10)]);
    //     this.form.get('phoneNumber').updateValueAndValidity();
    //   }
    // });
  }
}
