:host {
  --eds-navigation-border-properties: var(
    --navigation-border-properties,
    var(--eds-stroke-025) 0 0 0 var(--eds-border-style-base) var(--eds-border-color-default)
  );
  --eds-navigation-display: var(--navigation-display, flex);
  --eds-navigation-gap: var(--navigation-gap, var(--eds-spacing-200));
  --eds-navigation-justify-content: var(--navigation-justify-content, space-between);
  --eds-navigation-align-items: var(--navigation-align-items, center);
  --eds-navigation-flex-direction: var(--navigation-flex-direction, row);
  --eds-navigation-item-display: var(--navigation-item-display, flex);
  --eds-navigation-item-align-items: var(--navigation-item-align-items, center);
  --eds-navigation-item-justify-content: var(--navigation-item-justify-content, center);
  --eds-navigation-item-flex-direction: var(--navigation-item-flex-direction, column);
  --eds-navigation-item-gap: var(--navigation-item-gap, var(--eds-spacing-200));
  --eds-navigation-item-padding: var(--navigation-item-padding, var(--eds-spacing-100));
  --eds-navigation-item-min-width: var(--navigation-item-min-width, var(--eds-sizing-900));
  --eds-navigation-item-border-radius: var(--navigation-item-border-radius, var(--eds-radius-200));
  --eds-navigation-item-icon-color: var(--navigation-item-icon-color, var(--eds-colors-icon-default));
  --eds-navigation-item-border: var(
    --navigation-item-border,
    var(--eds-stroke-025) var(--eds-border-style-base) transparent
  );
  --eds-navigation-item-text-color: var(--navigation-item-text-color, var(--eds-colors-text-dark));
  --eds-navigation-item-active-text-color: var(--navigation-item-active-text-color, var(--eds-colors-icon-default));
  --eds-navigation-item-active-background: var(--navigation-item-active-background, transparent);
}

@media (min-width: 1440px) {
  :host {
    --eds-navigation-flex-direction: var(--navigation-flex-direction, column);

    --eds-navigation-item-flex-direction: var(--navigation-item-flex-direction, row);
    --eds-navigation-item-justify-content: var(--navigation-item-justify-content, flex-start);
    --eds-navigation-item-icon-color: var(--navigation-item-icon-color, var(--eds-colors-text-default));
    --eds-navigation-item-min-width: var(--navigation-item-min-width, 168px);
    --eds-navigation-item-padding: var(--navigation-item-padding, var(--eds-spacing-400) var(--eds-spacing-600));
    --eds-navigation-item-active-background: var(
      --navigation-item-active-background,
      var(--eds-colors-primary-default)
    );
    --eds-navigation-item-active-text-color: var(--navigation-item-active-text-color, var(--eds-colors-text-white));
  }

  .navigation {
    flex-direction: var(--eds-navigation-flex-direction);
    align-items: flex-start;
    gap: var(--eds-navigation-gap);
  }

  .navigation-items {
    flex-direction: row;
    min-width: var(--eds-navigation-item-min-width);
    justify-content: var(--eds-navigation-item-justify-content);
    padding: var(--eds-navigation-item-padding);
    gap: var(--eds-navigation-item-gap);
  }

  .navigation .navigation-items.active {
    border: var(--eds-navigation-item-border);
    border-radius: var(--eds-navigation-item-border-radius);
    background: var(--eds-navigation-item-active-background);
    color: var(--eds-navigation-item-active-text-color);

    eds-text {
      color: var(--eds-navigation-item-active-text-color);
    }
  }

  .navigation .navigation-items.active .navigation-items-icon {
    padding: 0;
  }
}

.navigation {
  display: var(--eds-navigation-display);
  align-items: var(--eds-navigation-align-items);
  justify-content: var(--eds-navigation-justify-content);
  flex-direction: var(--eds-navigation-flex-direction);
  gap: var(--eds-navigation-gap);
}

.navigation-items {
  display: var(--eds-navigation-item-display);
  align-items: var(--eds-navigation-item-align-items);
  justify-content: var(--eds-navigation-item-justify-content);
  text-decoration: none;
  flex-direction: var(--eds-navigation-item-flex-direction);
  gap: var(--eds-navigation-item-gap);
  min-width: var(--eds-navigation-item-min-width);
}

.navigation-items-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--eds-navigation-item-border-radius);
}

.navigation-items-icon [part='icon'] {
  color: var(--eds-navigation-item-icon-color);
}

.navigation-items-text {
  color: var(--eds-navigation-item-text-color);
}

.navigation-items.active .navigation-items-icon [part='icon'] {
  color: var(--eds-navigation-item-active-text-color);
  opacity: 1;
}
