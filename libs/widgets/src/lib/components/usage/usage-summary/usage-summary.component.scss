:host {
  --eds-usage-segmented-control-background: var(--usage-segmented-control-background, var(--eds-colors-base-50));
  --eds-usage-summary-gap: var(--usage-summary-gap, var(--eds-spacing-400));
  --eds-usages-display: var(--usages-display, grid);
  --eds-usages-grid-template: var(--usages-grid-template, none / none);
  --eds-usages-gap: var(--usages-gap, var(--eds-spacing-400));
}

[part='base'] {
  --eds-segmented-area-gap: var(--eds-usage-summary-gap);
  --eds-segmented-area-segment-background: var(--segmented-control-background, var(--eds-colors-base-50));
}

[part='base']::part(usages) {
  display: var(--eds-usages-display);
  grid-template: var(--eds-usages-grid-template);
  gap: var(--eds-usages-gap);
}

.deactivated {
  opacity: 0.5;
}
