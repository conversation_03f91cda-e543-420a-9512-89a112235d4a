import { ChangeDetectionStrategy, Component, effect, input, InputSignal, OnInit, output } from '@angular/core';
import { TitledSectionComponent } from '@libs/widgets';
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { createICCIDMask, FormFieldComponent, TranslatePipe } from '@libs/plugins';
import { debounceTime } from 'rxjs';

@Component({
  selector: 'widget-enter-sim-details',
  imports: [TitledSectionComponent, FormsModule, FormFieldComponent, ReactiveFormsModule, TranslatePipe],
  templateUrl: './enter-sim-details.component.html',
  styleUrl: './enter-sim-details.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EnterSimDetailsComponent implements OnInit {
  protected readonly iccidMask = createICCIDMask();
  validationStatus = input('');
  iccidChange = output<string>();

  form: InputSignal<FormGroup> = input();

  constructor() {
    effect(() => {
      if (this.validationStatus() === 'fail') {
        this.form().controls.iccid.setErrors({
          invalidICCID: true,
        });
      } else if (this.validationStatus() === 'success') {
        this.form().controls.iccid.setErrors(null);
      }
    });
  }

  ngOnInit() {
    this.form()
      .controls.iccid.valueChanges.pipe(debounceTime(400))
      .subscribe((value) => {
        this.iccidChange.emit(value);
      });
  }

  protected readonly status = status;
}
