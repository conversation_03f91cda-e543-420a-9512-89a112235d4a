// Variables
$primary-color: #41427a;
$spacing-xs: 4px;
$spacing-md: 16px;

// Section styles
.section {
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
}

.section-header {
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;
}

.section-title {
  font-size: 18px;
  color: $primary-color;
  letter-spacing: -0.4px;
  margin: 0;
  font-weight: 600;
}

.section-description {
  font-size: 14px;
  color: $primary-color;
  margin: 0;
  line-height: 1.4;
}
