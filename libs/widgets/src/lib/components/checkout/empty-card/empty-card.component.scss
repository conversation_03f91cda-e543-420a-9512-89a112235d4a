.base {
  display: flex;
  padding: 48px;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  align-self: stretch;
  border-radius: 24px;
  border: 1px dashed var(--eds-color-grey-200, #cacae2);

  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-space-100, 4px);
  }

  .icon {
    padding: 12px;
    border-radius: 8px;
    border: 1px solid #dedeed;
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
  }
}
