import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { TranslatePipe } from '@libs/plugins';
@Component({
  selector: 'widget-empty-card',
  templateUrl: './empty-card.component.html',
  styleUrls: ['./empty-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [TranslatePipe],
})
export class EmptyCardComponent {
  contentTitle = input<string>('yourCartIsEmpty');
  contentSubtitle = input<string>('stayConnectedWithTheBestPlansAndDevices');
  actionButtonText = input<string>('exploreProducts');
  actionButtonHref = input<string>('.');
}
