import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';
import { Radio } from '../../../model';

@Component({
  selector: 'widget-select-delivery-method',
  templateUrl: './select-delivery-method.component.html',
  styleUrl: './select-delivery-method.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class SelectDeliveryMethodComponent {
  titleText = input<string>('');
  deliveryMethods = input<Radio[]>([]);

  radioChange = output<number>();

  selectRadio(event: Event) {
    this.radioChange.emit(Number((event.target as HTMLInputElement).value));
  }
}
