<div class="section">
  <div class="summary-container">
    @if (completed() && isDone()) {
      <div class="icon-column">
        <eds-icon name="checkmarkCircle" size="sm"></eds-icon>
      </div>
    }
    <div class="content-column">
      <ng-content select="[offer]"></ng-content>
    </div>
    @if (completed() && isDone()) {
      <div class="action-column">
        <eds-button shouldFitContainer appearance="link" size="sm" (button-click)="onEdit()">
          {{ 'Edit' }}
        </eds-button>
      </div>
    }
  </div>

  @if (completed() && plan()?.device) {
    <div class="device-container">
      <div class="content-column-full">
        <ng-content select="[device]"></ng-content>
      </div>
    </div>
  }
</div>
