.section {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-600);
}

.summary-container {
  display: grid;
  grid-template-columns: min-content 1fr;
  align-items: flex-start;
  gap: var(--eds-spacing-200);

  &:has(.icon-column) {
    .content-column {
      grid-column: span 1;
    }
  }

  .content-column {
    grid-column: span 2;
  }

  @media (min-width: 834px) {
    grid-template-columns: min-content 1fr auto;
    gap: var(--eds-spacing-400);
  }

  .icon-column {
    eds-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: var(--eds-sizing-600);
      height: var(--eds-sizing-600);
    }
  }

  .action-column {
    flex-shrink: 0;
    grid-column: span 2;

    @media (min-width: 834px) {
      grid-column: span 1;
    }
  }
}

.device-container {
  display: grid;
  grid-template-columns: var(--eds-sizing-600) 1fr;
  gap: var(--eds-spacing-400);

  .content-column-full {
    grid-column: span 2;

    @media (min-width: 834px) {
      grid-column: 2 / span 1;
    }
  }
}
