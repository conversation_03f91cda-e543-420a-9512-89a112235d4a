import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';
import { PlanData } from '@libs/bss';

@Component({
  selector: 'widget-sim-summary-card',
  templateUrl: './sim-summary-card.component.html',
  styleUrl: './sim-summary-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class SimSummaryCardComponent {
  plan = input<PlanData>();
  completed = input<boolean>(false);
  isDone = input<boolean>(false);

  edit = output<PlanData>();

  onEdit() {
    this.edit.emit(this.plan());
  }
}
