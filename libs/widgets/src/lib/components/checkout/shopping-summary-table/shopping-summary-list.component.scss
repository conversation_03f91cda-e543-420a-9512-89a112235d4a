.list {
  padding: 16px;

  @media (min-width: 1440px) {
    padding: 0 24px 16px;
  }
}
.list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}
.list-item:last-child {
  margin-bottom: 0;
}
.list-item p {
  font-size: 12px;
  font-weight: 500;
  color: #242442;
}
.list-item .highlight {
  color: #c56b1c;
}
.list-item .due {
  color: #167e4a;
}

.divider {
  height: 1px;
  background-color: #dedeea;
  margin: 12px 0;
}
.footer {
  display: none;
  padding: 16px 24px;
  background-color: #fff;
  border-top: 1px solid #dedeea;

  @media (min-width: 1440px) {
    display: block;
  }
}
.footer .total {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.footer .total p {
  font-size: 18px;
  font-weight: 500;
  color: #242442;
}
.footer .total .amount {
  font-size: 32px;
  font-weight: 500;
  color: #242442;
}
