.base {
  display: flex;
  border: 1px solid var(--eds-border-color-default);
  border-radius: var(--eds-radius-400);

  &::part(base) {
    width: 100%;
  }

  &::part(items) {
    gap: 0;
  }

  eds-radio {
    display: flex;
    align-items: center;
    gap: var(--eds-spacing-200);
    padding: var(--eds-spacing-300);

    @media (min-width: 834px) {
      padding: var(--eds-spacing-400);
    }

    &:not(:last-child) {
      border-bottom: 1px solid var(--eds-border-color-default);
    }

    &::part(base) {
      align-items: center;
    }
  }
}
