<eds-radio-group
  class="base"
  [id]="'radio' + inputName()"
  name="{{ 'radio' + inputName() }}"
  [isRequired]="true"
  [isInvalid]="false"
>
  @if (showCreditCards()) {
    <eds-radio
      [id]="'method' + bankPaymentMethodType.CREDIT_CARD + uuid"
      [value]="bankPaymentMethodType.CREDIT_CARD"
      [isChecked]="latestPaymentMethod() === bankPaymentMethodType.CREDIT_CARD"
      [isDisabled]="false"
      (radio-change)="handleInputChange(bankPaymentMethodType.CREDIT_CARD)"
    >
      <label [for]="'method' + bankPaymentMethodType.CREDIT_CARD + uuid">
        <eds-text [size]="'lg'" [weight]="'medium'" text="Credit card"></eds-text>
      </label>
    </eds-radio>

    @if (latestPaymentMethod() === bankPaymentMethodType.CREDIT_CARD) {
      <widget-payment-method-credit-card
        [savedCreditCards]="savedCreditCards()"
        (createCreditCard)="createCreditCard.emit($event)"
        (onDelete)="onDeleteCreditCard($event)"
        (selectionChange)="onSelectionChange({ type: bankPaymentMethodType.CREDIT_CARD, id: $event })"
      ></widget-payment-method-credit-card>
    }
  }

  @if (showBankAccounts()) {
    <eds-radio
      [id]="'method' + bankPaymentMethodType.BANK_ACCT + uuid"
      [value]="bankPaymentMethodType.BANK_ACCT"
      [isChecked]="latestPaymentMethod() === bankPaymentMethodType.BANK_ACCT"
      [isDisabled]="false"
      (radio-change)="handleInputChange(bankPaymentMethodType.BANK_ACCT)"
    >
      <label [for]="'method' + bankPaymentMethodType.BANK_ACCT + uuid">
        <eds-text [size]="'lg'" [weight]="'medium'" text="Bank account"></eds-text>
      </label>
    </eds-radio>

    @if (latestPaymentMethod() === bankPaymentMethodType.BANK_ACCT) {
      <widget-payment-method-bank-account
        [savedBankAccounts]="savedBankAccounts()"
        (createBankAccount)="createBankAccount.emit($event)"
        (onDelete)="onDeleteBankAccount($event)"
        (selectionChange)="onSelectionChange({ type: bankPaymentMethodType.BANK_ACCT, id: $event })"
      ></widget-payment-method-bank-account>
    }
  }

  @if (!authorized()) {
    <eds-radio
      [id]="bankPaymentMethodType.PAY_IN_STORE"
      [value]="bankPaymentMethodType.PAY_IN_STORE"
      [isChecked]="latestPaymentMethod() === bankPaymentMethodType.PAY_IN_STORE"
      [isDisabled]="false"
      (radio-change)="handleInputChange(bankPaymentMethodType.PAY_IN_STORE)"
    >
      <label [for]="bankPaymentMethodType.PAY_IN_STORE">
        <eds-text [size]="'lg'" [weight]="'medium'" text="Pay in store"></eds-text>
      </label>
    </eds-radio>

    @if (latestPaymentMethod() === bankPaymentMethodType.PAY_IN_STORE) {
      <widget-payment-method-pay-in-store></widget-payment-method-pay-in-store>
    }

    <eds-radio
      [id]="bankPaymentMethodType.PAYPAL"
      [value]="bankPaymentMethodType.PAYPAL"
      [isChecked]="latestPaymentMethod() === bankPaymentMethodType.PAYPAL"
      [isDisabled]="false"
      (radio-change)="handleInputChange(bankPaymentMethodType.PAYPAL)"
    >
      <label [for]="bankPaymentMethodType.PAYPAL">
        <eds-text [size]="'lg'" [weight]="'medium'" text="PayPal"></eds-text>
      </label>
    </eds-radio>

    @if (latestPaymentMethod() === bankPaymentMethodType.PAYPAL) {
      <widget-payment-method-paypal></widget-payment-method-paypal>
    }
  }
</eds-radio-group>
