.card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: var(--eds-spacing-300);
  background-color: var(--eds-colors-surface-level-1);
  border-radius: var(--eds-radius-300);
  gap: var(--eds-spacing-300);

  .info {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  eds-button {
    &::part(base) {
      padding: 0;
      height: unset;
    }
  }

  @media (min-width: 834px) {
    padding: var(--eds-spacing-400);
  }
}
