import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';

@Component({
  selector: 'widget-changeable-info-card',
  imports: [],
  templateUrl: './changeable-info-card.component.html',
  styleUrl: './changeable-info-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class ChangeableInfoCardComponent {
  phoneNumber = input<string>();
  title = input<string>('Your new number');
  changeNumberText = input<string>('Change number');
  update = output();

  onChangeClick() {
    this.update.emit();
  }
}
