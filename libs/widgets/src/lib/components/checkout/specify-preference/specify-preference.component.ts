import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';
import { Radio } from '@libs/widgets';
import { TranslatePipe } from '@libs/plugins';

@Component({
  selector: 'widget-specify-preference',
  templateUrl: './specify-preference.component.html',
  styleUrl: './specify-preference.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [TranslatePipe],
})
export class SpecifyPreferenceComponent {
  titleText = input<string>('');
  specifyPreferences = input<Radio[]>([]);
  radioChange = output<string>();

  selectRadio(event: Event) {
    this.radioChange.emit((event.target as HTMLInputElement).value);
  }
}
