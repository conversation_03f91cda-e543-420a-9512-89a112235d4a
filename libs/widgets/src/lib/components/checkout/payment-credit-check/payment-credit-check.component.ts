import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, output } from '@angular/core';

@Component({
  selector: 'widget-payment-credit-check',
  templateUrl: './payment-credit-check.component.html',
  styleUrls: ['./payment-credit-check.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class PaymentCreditCheckComponent {
  selectionChange = output<string>();

  onRadioChange(event: Event) {
    this.selectionChange.emit((event.target as HTMLInputElement).value);
  }
}
