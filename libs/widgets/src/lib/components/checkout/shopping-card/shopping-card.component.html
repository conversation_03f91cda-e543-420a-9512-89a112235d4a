@if (shoppingSummaryList()) {
  <widget-shopping-summary-list [items]="items()" [total]="total()"></widget-shopping-summary-list>
}

<div class="footer" [attr.click]="!isDesktop() ? null : undefined" (click)="onFooterClick()">
  <div class="content">
    <eds-icon name="arrowDown" [class.rotate]="shoppingSummaryList()"></eds-icon>
    @if (total()) {
      <div class="total">
        <eds-text size="sm" [text]="total()?.key"></eds-text>
        <eds-heading size="sm" [text]="total()?.amount"></eds-heading>
      </div>
    }
  </div>
  <eds-button
    shouldFitContainer
    appearance="primary"
    size="default"
    (button-click)="onButtonClick()"
    [disabled]="actionButtonDisabled()"
  >
    {{ actionButtonText() }}
  </eds-button>
</div>
