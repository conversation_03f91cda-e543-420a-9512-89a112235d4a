:host {
  --eds-shopping-card-border: var(
    --card-border,
    var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default)
  );
}

.shopping-cart {
  width: 100%;
  margin: 0 auto;
  background-color: #fff;
  border-radius: 24px;
  box-shadow: 0 3px 0 rgba(0, 0, 0, 0.04);
  overflow: hidden;
  border: var(--eds-shopping-card-border);
}
.header {
  display: flex;
  align-items: center;
  padding: 16px 24px;
  background-color: #fff;
}
.header h1 {
  font-size: 20px;
  font-weight: 500;
  color: #242442;
  letter-spacing: -0.4px;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 16px;
  padding: 16px;
  background-color: #fff;
  border-top: 1px solid #dedeea;

  .content {
    display: flex;
    align-items: center;
    gap: 8px;

    eds-icon {
      width: 20px;
      height: 20px;
      flex-shrink: 0;
      transition: transform 444ms cubic-bezier(0.075, 0.82, 0.165, 1);

      &.rotate {
        transform: rotate(180deg);
      }
    }
  }

  @media (min-width: 1440px) {
    display: block;
    border-top: 0;
    padding: 16px 24px;
    .content {
      display: none;
    }
  }
}

eds-button {
  &::part(wrapper) {
    text-align: center;
  }
}
