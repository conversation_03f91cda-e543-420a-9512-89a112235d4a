<div class="base">
  <eds-heading as="h4" size="sm" [text]="titleText()" class="title"></eds-heading>

  <eds-radio-group name="delivery-method" (radio-change)="selectRadio($event)">
    @for (item of deliveryOptions(); track item.value) {
      <eds-radio
        [id]="'method-' + item.id"
        [value]="item.value"
        [name]="'method-' + item.name"
        [isChecked]="item.isChecked"
      >
        <label [for]="'method-' + item.id">
          <div class="label-wrapper">
            <eds-text class="label-title" [size]="'md'" [weight]="'bold'" [text]="item.label"></eds-text>
            <div class="label-description">
              <eds-text
                [size]="'sm'"
                [color]="'secondary'"
                [text]="'estimatedDelivery' | translate: { date: item.deliveryDays }"
              ></eds-text>
              <eds-text [size]="'sm'" [color]="'secondary'" [text]="item.deliveryTime"></eds-text>
            </div>
          </div>
          <eds-text class="label-price" weight="bold" [text]="item.price | currency"></eds-text>
        </label>
      </eds-radio>
    }
  </eds-radio-group>
</div>
