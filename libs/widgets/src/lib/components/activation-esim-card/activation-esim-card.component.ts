import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';
import { ICCID_MASK, TranslatePipe } from '@libs/plugins';
import { IMaskPipe } from '@libs/core';

@Component({
  selector: 'widget-activation-esim-card',
  templateUrl: './activation-esim-card.component.html',
  styleUrls: ['./activation-esim-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [TranslatePipe, IMaskPipe],
})
export class ActivationESimCardComponent {
  eSim = input<string>();
  eSimMask = ICCID_MASK;

  displayQrCodeOutput = output<void>();

  displayQrCode() {
    this.displayQrCodeOutput.emit();
  }
}
