<div class="base">
  <div class="title">
    <div class="title-wrapper">
      <eds-heading size="md" as="h6" [text]="'activateESim' | translate"></eds-heading>
    </div>
  </div>
  <div class="content">
    <div class="steps">
      <eds-text
        size="lg"
        weight="medium"
        [text]="'activateESimTitle' | translate: { eSim: (eSim() | imask: { mask: eSimMask }) }"
      ></eds-text>
      <eds-text size="lg" weight="regular" [text]="'activateESimStepTitle' | translate"></eds-text>
      <eds-text size="lg" weight="regular" [text]="'activateESimStep1' | translate"></eds-text>
      <eds-text size="lg" weight="regular" [text]="'activateESimStep2' | translate"></eds-text>
      <eds-text size="lg" weight="regular" [text]="'activateESimStep3' | translate"></eds-text>
    </div>
    <eds-button class="button" appearance="primary" size="default" shouldFitContainer (button-click)="displayQrCode()">
      {{ 'displayQrCode' | translate }}
    </eds-button>
  </div>
</div>
