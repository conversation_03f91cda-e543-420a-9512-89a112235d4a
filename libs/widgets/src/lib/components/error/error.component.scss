.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: var(--eds-spacing-600);
  gap: var(--eds-spacing-400);
  min-height: 300px;
}

.error-icon {
  font-size: 64px;
  color: var(--eds-colors-danger-default);
  margin-bottom: var(--eds-spacing-400);
}

.error-content {
  margin-bottom: var(--eds-spacing-400);
}

.error-actions {
  margin-top: var(--eds-spacing-400);
}
