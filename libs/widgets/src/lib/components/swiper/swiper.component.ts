import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  ElementRef,
  HostListener,
  input,
  signal,
  viewChild,
  effect,
  computed,
  OnInit,
} from '@angular/core';
import { SwiperOptions } from '../../model/swiper.model';

@Component({
  selector: 'widget-swiper',
  standalone: true,
  templateUrl: './swiper.component.html',
  styleUrls: ['swiper.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SwiperComponent implements AfterViewInit, OnInit {
  private window = window;
  protected Math = Math;

  config = input<SwiperOptions>();

  container = viewChild<ElementRef>('container');

  current = signal(0);
  total = signal(0);
  startX = signal(0);
  locked = signal(false);
  bullets = computed(() => {
    const activeBreakpointKey = this.findActiveBreakpoint();
    const slidesPerView =
      activeBreakpointKey !== null
        ? this.config().breakpoints[activeBreakpointKey]?.slidesPerView
        : this.config().slidesPerView;

    const effectiveSlidesPerView = Math.max(1, slidesPerView || 1);

    return this.total() > 0 ? Math.ceil(this.total() / effectiveSlidesPerView) : 0;
  });
  windowWidth = signal(this.window.innerWidth);

  @HostListener('window:resize')
  onResize() {
    this.windowWidth.set(this.window.innerWidth);
  }

  @HostListener('mousedown', ['$event'])
  onMouseDown(event: MouseEvent) {
    this.lockInteraction(event);
    document.addEventListener('mousemove', this.drag);
    document.addEventListener('mouseup', this.endInteraction);
  }

  @HostListener('touchstart', ['$event'])
  onTouchStart(event: TouchEvent) {
    this.lockInteraction(event);
  }

  @HostListener('touchmove', ['$event'])
  onTouchMove(event: TouchEvent) {
    this.drag(event);
  }

  @HostListener('touchend', ['$event'])
  onTouchEnd(event: TouchEvent) {
    this.endInteraction(event);
  }

  constructor() {
    effect(() => {
      this.container().nativeElement.style.setProperty('--n', this.total().toString());
      this.container().nativeElement.style.setProperty('--spw', this.breakpoint().slidesPerView);
    });
  }

  ngOnInit() {
    this.windowWidth.set(this.window.innerWidth);
  }

  breakpoint = computed(() => {
    if (this.config()?.breakpoints) {
      const activeBreakpoint = this.findActiveBreakpoint();
      if (activeBreakpoint) {
        return {
          ...this.config(),
          ...this.config().breakpoints[activeBreakpoint],
        };
      }
      return activeBreakpoint ? this.config().breakpoints[activeBreakpoint] : this.config();
    }
    return this.config();
  });

  private findActiveBreakpoint(): number | null {
    if (!this.config().breakpoints) return null;

    const breakpoints = Object.keys(this.config().breakpoints)
      .map(Number)
      .sort((a, b) => b - a);

    for (const breakpoint of breakpoints) {
      if (this.windowWidth() >= breakpoint) {
        return breakpoint;
      }
    }

    return null;
  }

  ngAfterViewInit() {
    this.total.set(this.container().nativeElement.children[0].children.length);
  }

  lockInteraction(event: TouchEvent | MouseEvent) {
    this.startX.set(this.getEventX(event));
    this.locked.set(true);
  }

  drag = (event: TouchEvent | MouseEvent) => {
    event.preventDefault();
    if (!this.locked()) {
      return;
    }

    const deltaX = Math.round(this.getEventX(event) - this.startX());
    this.container().nativeElement.style.setProperty('--tx', `${deltaX}px`);
  };

  endInteraction = (event: TouchEvent | MouseEvent) => {
    if (!this.locked()) {
      return;
    }

    const deltaX = this.getEventX(event) - this.startX();
    const direction = Math.sign(deltaX);
    const movedFraction = +(Math.abs(deltaX) / this.window.innerWidth).toFixed(2);
    const maxPage = Math.ceil(this.total() / this.breakpoint().slidesPerView) - 1;

    if (
      movedFraction > 0.01 &&
      ((direction < 0 && this.current() < maxPage) || (direction > 0 && this.current() > 0))
    ) {
      this.current.update(
        (curr) => curr - (this.breakpoint().slidesPerStep || this.breakpoint().slidesPerView) * direction,
      );
      this.container().nativeElement.style.setProperty('--i', this.current().toString());
    }

    this.resetInteraction(movedFraction);
  };

  private resetInteraction(movedFraction: number) {
    this.container().nativeElement.style.setProperty('--tx', '0px');
    this.container().nativeElement.style.setProperty('--f', (1 - movedFraction).toString());
    this.locked.set(false);
    this.startX.set(0);

    document.removeEventListener('mousemove', this.drag);
    document.removeEventListener('mouseup', this.endInteraction);
  }

  private getEventX(event: TouchEvent | MouseEvent): number {
    return 'changedTouches' in event ? event.changedTouches[0].clientX : event.clientX;
  }
}
