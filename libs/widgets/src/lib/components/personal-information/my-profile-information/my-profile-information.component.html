<div class="base">
  <eds-data-list
    [itemsSize]="dataList().itemsSize"
    [trim]="dataList().trim"
    [expandedText]="dataList().expandedText"
    [unexpandedText]="dataList().unexpandedText"
    [items]="dataList().items"
  ></eds-data-list>

  <widget-interaction-list [actions]="interactions()"></widget-interaction-list>

  @if (hasPermissionToEdit()) {
    <eds-button [iconLeading]="'pencilEdit'" [appearance]="'default'" shouldFitContainer (button-click)="onEdit.emit()">
      {{ 'edit' | translate }}
    </eds-button>
  }
</div>
