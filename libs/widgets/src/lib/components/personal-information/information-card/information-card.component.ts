import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'widget-information-card',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './information-card.component.html',
  styleUrls: ['./information-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class InformationCardComponent {
  title = input<string>();
  addLabel = input<string>('Add');
  add = output<void>();
  canAdd = input<boolean>(true);
  showContentArea = input<boolean>(true);

  onAdd() {
    this.add.emit();
  }
}
