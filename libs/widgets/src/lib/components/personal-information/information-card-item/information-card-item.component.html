<div class="card-item">
  <div class="item-icon-area">
    @if (isPrimary()) {
      <eds-icon name="checkmarkCircle"></eds-icon>
    }
  </div>
  <div class="item-content">
    <ng-content></ng-content>
  </div>
  @if (showEdit() || showDelete()) {
    <div class="item-actions">
      @if (showDelete()) {
        <widget-item-delete-action
          [deleteConfirmationText]="'yesDelete' | translate"
          (deleteItem)="delete.emit()"
        ></widget-item-delete-action>
      }
      @if (showEdit()) {
        <eds-button
          [size]="'compact'"
          [iconOnly]="true"
          [iconLeading]="'pencilEdit'"
          [appearance]="'default'"
          (button-click)="edit.emit()"
        ></eds-button>
      }
    </div>
  }
</div>
