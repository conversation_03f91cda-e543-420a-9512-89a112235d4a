.card-item {
  display: flex;
  padding: var(--eds-spacing-400);
  gap: var(--eds-spacing-200);
  align-items: start;

  .item-icon-area {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    width: var(--eds-sizing-500);
    height: var(--eds-sizing-500);
    margin-top: var(--eds-spacing-100);
  }

  .item-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .item-actions {
    display: flex;
    gap: var(--eds-spacing-100);
  }

  eds-icon::part(base) {
    display: flex;
    width: 100%;
    height: 100%;
  }
}
