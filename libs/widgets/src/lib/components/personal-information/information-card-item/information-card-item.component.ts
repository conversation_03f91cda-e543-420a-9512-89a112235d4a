import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  effect,
  inject,
  input,
  OnDestroy,
  output,
  ViewChild,
} from '@angular/core';
import { ItemDeleteActionComponent } from '../../item-delete-action/item-delete-action.component';
import { TranslatePipe } from '@libs/plugins';
import { ModalService } from '@libs/plugins';
import { CustomerProfileService } from '@libs/bss';
import { Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'widget-information-card-item',
  standalone: true,
  imports: [CommonModule, ItemDeleteActionComponent, TranslatePipe],
  templateUrl: './information-card-item.component.html',
  styleUrls: ['./information-card-item.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class InformationCardItemComponent implements OnDestroy {
  private modalService = inject(ModalService);
  private customerProfileService = inject(CustomerProfileService);
  private destroy$ = new Subject<void>();

  @ViewChild(ItemDeleteActionComponent) itemDeleteAction?: ItemDeleteActionComponent;

  isPrimary = input<boolean>(false);
  showEdit = input<boolean>(true);
  showDelete = input<boolean>(true);

  edit = output<void>();
  delete = output<void>();

  constructor() {
    this.modalService.modalOpened$.pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.itemDeleteAction?.cancelDeleteAction();
    });

    effect(() => {
      if (this.customerProfileService.isEditMode()) {
        this.itemDeleteAction?.cancelDeleteAction();
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onEdit(event: MouseEvent) {
    event.preventDefault();
    event.stopPropagation();
    this.edit.emit();
  }
}
