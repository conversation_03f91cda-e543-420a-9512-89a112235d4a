import { Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { ActionCardComponent, ActionListItem } from '@libs/widgets';

@Component({
  selector: 'widget-main-interactions',
  templateUrl: './main-interactions.component.html',
  styleUrls: ['./main-interactions.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [ActionCardComponent],
})
export class MainInteractionsComponent {
  ordersInteraction = input<ActionListItem>({
    text: 'Orders',
  });

  ticketsInteraction = input<ActionListItem>({
    text: 'Tickets',
  });
}
