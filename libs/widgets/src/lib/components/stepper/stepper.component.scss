.base {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-400);

  .step {
    border-radius: var(--eds-radius-500);
    background-color: var(--eds-colors-surface-default);
    border: 1px solid var(--eds-border-color-default);
    box-shadow: var(--eds-shadow-sm);

    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--eds-spacing-400);
      background-color: var(--eds-colors-surface-disabled);
      border-radius: var(--eds-radius-500);

      .title-wrapper {
        display: flex;
        align-items: center;
        gap: var(--eds-spacing-300);

        eds-heading::part(base) {
          color: var(--eds-colors-text-disabled);
        }

        eds-icon {
          width: var(--eds-sizing-600);
          height: var(--eds-sizing-600);
          border-radius: 50%;
          background-color: var(--eds-colors-success-default);

          &::part(base) {
            color: var(--eds-colors-text-white);
          }
        }
      }
    }

    &.active {
      .title {
        border-radius: var(--eds-radius-500) var(--eds-radius-500) 0 0;
        background-color: var(--eds-colors-surface-level-1);

        .title-wrapper {
          eds-heading::part(base) {
            color: var(--eds-colors-text-default);
          }
        }
      }
    }

    &.completed {
      .title {
        background-color: var(--eds-colors-surface-default);

        .title-wrapper {
          eds-heading::part(base) {
            color: var(--eds-colors-text-default);
          }
        }
      }
    }

    .content {
      display: flex;
      flex-direction: column;
      gap: var(--eds-spacing-600);
      border-top: 1px solid var(--eds-border-color-default);
    }

    &.inline {
      border-radius: unset;
      background-color: transparent;
      border: none;
      box-shadow: none;
      display: flex;
      flex-direction: column;
      gap: var(--eds-spacing-400);

      .title {
        background-color: transparent;
        padding: unset;

        .title-wrapper {
          eds-heading::part(base) {
            color: var(--eds-colors-text-default);
          }
        }
      }
      .content {
        border-top: none;
        padding: 0;
      }

      @media (min-width: 834px) {
        gap: var(--eds-spacing-600);
      }
    }

    &.inline.completed {
      .content {
        @media (min-width: 834px) {
          padding-inline-start: calc(var(--eds-size-multiplier) * 9);
        }
      }
    }
  }
}
