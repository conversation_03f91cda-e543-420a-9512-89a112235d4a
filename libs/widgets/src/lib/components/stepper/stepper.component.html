<div class="base">
  @for (step of steps(); track $index) {
    <div
      class="
      step
      {{ step.isActive ? 'active' : '' }}
      {{ step.isCompleted ? 'completed' : '' }}
      {{ step.inline ? 'inline' : '' }}"
    >
      <div class="title">
        <div class="title-wrapper">
          @if (step.isCompleted) {
            <eds-icon name="checkmarkCircle"></eds-icon>
          } @else if (!step.inline) {
            <eds-badge
              [label]="$index + 1"
              type="circle"
              size="medium"
              appearance="{{ step.isActive ? 'primary' : 'stroke' }}"
            ></eds-badge>
          }
          <eds-heading as="h6" size="md" text="{{ step.title }}"></eds-heading>
        </div>
        @if (step.isCompleted) {
          <eds-button size="compact" appearance="link" (button-click)="edit.emit(step)">Edit</eds-button>
        }
      </div>
      @if (step.isActive) {
        <div class="content">
          <ng-content></ng-content>
        </div>
      }
    </div>
  }
</div>
