<div #base>
  @if (loggedIn()) {
    <div class="profile-item" (click)="openProfileItemDropdown()">
      <eds-text class="profile-item-text desktop" [text]="name()"></eds-text>
      <eds-text class="profile-item-text mobile" [text]="name()?.slice(0, 1)"></eds-text>
      <eds-icon class="profile-item-icon" name="arrowDown"></eds-icon>
    </div>
    @if (profileItemDropdown()) {
      <div class="profile-item-dropdown">
        @for (item of profileItems(); track item) {
          <eds-button appearance="subtle" [iconLeading]="item.iconLeading" (button-click)="item.onClick()">
            {{ item.text }}
          </eds-button>
        }
      </div>
    }
  } @else {
    <eds-button (button-click)="loginClick.emit()" appearance="subtle" iconLeading="userSquare">Login</eds-button>
  }
  <div></div>
</div>
