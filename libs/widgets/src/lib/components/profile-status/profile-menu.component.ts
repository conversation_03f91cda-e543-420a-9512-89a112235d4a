import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  ElementRef,
  input,
  output,
  signal,
  ViewChild,
} from '@angular/core';
import { ActionListItem } from '../../model';

@Component({
  selector: 'widget-profile-menu',
  imports: [],
  templateUrl: './profile-menu.component.html',
  styleUrl: './profile-menu.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class ProfileMenuComponent {
  loggedIn = input(false);
  name = input('');
  loginClick = output<void>();
  profileItems = input<ActionListItem[]>([]);
  profileItemDropdown = signal(false);

  @ViewChild('base') base!: ElementRef<HTMLElement>;

  openProfileItemDropdown() {
    this.profileItemDropdown.update((prev) => !prev);
    setTimeout(() => {
      document.addEventListener('click', this.handleDocumentClick);
    });
  }

  closeProfileItemDropdown(): void {
    this.profileItemDropdown.set(false);
    document.removeEventListener('click', this.handleDocumentClick);
  }

  private handleDocumentClick = (event: MouseEvent): void => {
    if (this.base.nativeElement && !this.base.nativeElement.contains(event.target as Node)) {
      this.closeProfileItemDropdown();
    }
  };
}
