:host {
  --eds-page-heading-gap: var(--page-heading-gap, var(--eds-spacing-200));
  --eds-page-heading-content-gap: var(--page-heading-content-gap, var(--eds-spacing-200));
  --eds-page-heading-font-weight: var(--page-heading-font-weight, var(--eds-font-weight-medium));
  --eds-page-heading-color: var(--page-heading-text-color, var(--eds-colors-text-dark));
  --eds-page-heading-subheading-font-weight: var(--page-heading-subheading-font-weight, var(--eds-font-weight-regular));
  --eds-page-heading-subheading-color: var(--page-heading-subheading-color, var(--eds-colors-text-light));
  --eds-page-heading-icon-size: var(--page-heading-icon-size, var(--eds-sizing-700));
  --eds-page-heading-icon-color: var(--page-heading-icon-color, var(--eds-colors-secondary-default));
}

[part='base'] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--eds-page-heading-gap);
}

[part='icon'] {
  width: var(--eds-page-heading-icon-size);
  height: var(--eds-page-heading-icon-size);
  color: var(--eds-page-heading-icon-color);
  flex-shrink: 0;
}

[part='content'] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--eds-page-heading-content-gap);
}

[part='heading'] {
  --eds-heading-text-color: var(--eds-page-heading-color);
  --eds-heading-font-weight: var(--eds-page-heading-font-weight);
}

[part='subheading'] {
  --eds-heading-text-color: var(--eds-page-heading-subheading-color);
  --eds-heading-font-weight: var(--eds-page-heading-subheading-font-weight);
}

eds-heading::part(base) {
  text-align: center;
}
