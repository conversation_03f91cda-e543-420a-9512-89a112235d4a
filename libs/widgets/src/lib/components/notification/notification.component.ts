import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, signal } from '@angular/core';
import { DatePipe, NgClass } from '@angular/common';

@Component({
  selector: 'widget-notification',
  templateUrl: './notification.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [NgClass, DatePipe],
})
export class NotificationComponent {
  header = input('');

  text = input('');

  date = input('');

  isViewed = input(false);

  userNotificationId = input<number>();

  dropdownItems = input([]);

  tippyInstance = signal(undefined);

  onTriggered(e: unknown) {
    if (this.tippyInstance) {
      return;
    }
    this.tippyInstance.set(e);
  }
}
