.section {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-400);

  &.padding-x {
    padding-inline: var(--eds-spacing-300);
  }

  &.padding-y {
    padding-block: var(--eds-spacing-300);
  }

  &.padding-xy {
    padding: var(--eds-spacing-300);
  }

  @media (min-width: 834px) {
    gap: var(--eds-spacing-600);

    &.padding-x {
      padding-inline: var(--eds-spacing-600);
    }

    &.padding-y {
      padding-block: var(--eds-spacing-600);
    }

    &.padding-xy {
      padding: var(--eds-spacing-600);
    }
  }
}
