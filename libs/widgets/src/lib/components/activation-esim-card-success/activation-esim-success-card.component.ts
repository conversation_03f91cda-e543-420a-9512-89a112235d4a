import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';
import { ICCID_MASK, TranslatePipe } from '@libs/plugins';
import { IMaskPipe } from '@libs/core';
import { SuccessIconComponent } from '../success-icon';
@Component({
  selector: 'widget-activation-esim-success-card',
  templateUrl: './activation-esim-success-card.component.html',
  styleUrls: ['./activation-esim-success-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [TranslatePipe, IMaskPipe, SuccessIconComponent],
})
export class ActivationESimSuccessCardComponent {
  eSim = input<string>();
  eSimMask = ICCID_MASK;

  goToHomepage = output<void>();

  goToHomepageClick() {
    this.goToHomepage.emit();
  }
}
