eds-card {
  --eds-card-gap: calc(var(--eds-size-multiplier) * 8);
}

.body {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--eds-spacing-600);

  .header {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1;
    gap: var(--eds-spacing-200);
    eds-text::part(base) {
      text-align: center;
    }
  }

  .content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--eds-spacing-600);
    border-radius: var(--eds-radius-400);
    border: var(--eds-stroke-025) solid var(--eds-border-color-default);
    width: 100%;
    z-index: 1;
  }
}
