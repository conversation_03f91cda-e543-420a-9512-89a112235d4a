<div class="base">
  @if (!deleteActionTriggered()) {
    @if (buttonText()) {
      <eds-button (button-click)="deleteAction()" appearance="default" size="compact" iconTrailing="delete">
        {{ buttonText() }}
      </eds-button>
    } @else {
      <eds-button
        appearance="default"
        size="compact"
        iconLeading="delete"
        iconOnly
        (button-click)="deleteAction()"
      ></eds-button>
    }
  }
  @if (deleteActionTriggered()) {
    <div class="delete-action-container">
      <eds-button appearance="default" size="compact" iconLeading="delete" (button-click)="confirmDeleteAction()">
        {{ deleteConfirmationText() }}
      </eds-button>
      <eds-button
        appearance="subtle"
        size="compact"
        iconLeading="cancel"
        iconOnly
        (button-click)="cancelDeleteAction()"
      ></eds-button>
    </div>
  }
</div>
