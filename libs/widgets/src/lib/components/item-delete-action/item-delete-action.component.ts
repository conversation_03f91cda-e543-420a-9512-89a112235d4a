import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, output, signal } from '@angular/core';

@Component({
  selector: 'widget-item-delete-action',
  templateUrl: './item-delete-action.component.html',
  styleUrls: ['./item-delete-action.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [],
})
export class ItemDeleteActionComponent {
  deleteConfirmationText = input<string>();

  buttonText = input<string>();

  deleteItem = output();

  deleteActionTriggered = signal(false);

  deleteAction() {
    this.deleteActionTriggered.set(true);
  }

  confirmDeleteAction() {
    this.deleteActionTriggered.set(false);
    this.deleteItem.emit();
  }

  cancelDeleteAction() {
    this.deleteActionTriggered.set(false);
  }
}
