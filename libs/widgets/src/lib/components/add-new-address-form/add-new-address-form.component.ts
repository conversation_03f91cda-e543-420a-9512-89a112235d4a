import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  effect,
  inject,
  input,
  output,
  signal,
} from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { FormFieldComponent, TranslatePipe, TranslateService } from '@libs/plugins';
import { Address, AddressType, City, Country, State } from '@libs/types';
import { AddNewAddressForm, eAddressSelectionMode, Phonenumber, Radio, SelectOption } from '../../model';
import { PhoneNumberComponent } from '../phonenumber/phone-number.component';
import { RadioGroupDirective } from '@libs/core';
import { CustomerAddressListData } from '@libs/bss';

@Component({
  selector: 'widget-add-new-address-form',
  templateUrl: './add-new-address-form.component.html',
  styleUrls: ['./add-new-address-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [ReactiveFormsModule, FormFieldComponent, TranslatePipe, PhoneNumberComponent, RadioGroupDirective],
})
export class AddNewAddressFormComponent {
  private translate = inject(TranslateService);

  formAddressType = input<AddressType>(null);
  addressForm = input<FormGroup<AddNewAddressForm>>(null);

  countryOptions = input<SelectOption[]>([]);
  countryOptionsForPhoneNumber = input<SelectOption[]>([]);
  stateOptions = input<SelectOption[]>([]);
  cityOptions = input<SelectOption[]>([]);

  selectedAddressSelectionType = signal<keyof typeof eAddressSelectionMode>(eAddressSelectionMode.NEW_ADDRESS);
  countryChangeKey = signal<number>(0);
  readonly eAddressSelectionMode = eAddressSelectionMode;
  addressSelectionTypes: Radio[] = Object.values(eAddressSelectionMode).map((mode, index) => ({
    id: `address-selection-type-${String(index)}`,
    name: mode,
    value: mode,
    label: this.translate.translate(mode),
    isChecked: false,
    isDisabled: false,
  }));
  addressList = input<SelectOption[]>([]);
  deliveryAddressList = input<CustomerAddressListData>();
  selectedExistAddressId = signal<number>(null);

  onCountrySelected = output<number>();
  onStateSelected = output<number>();

  constructor() {
    effect(() => {
      if (this.formAddressType() === AddressType.Billing) {
        this.selectedAddressSelectionType.set(eAddressSelectionMode.EXIST_ADDRESS);
        this.addressSelectionTypes[0].isChecked = true;
      }
    });

    effect(() => {
      const selectedId = this.addressList().find((address) => address.isSelected)?.value;
      this.selectedExistAddressId.set(+selectedId);
    });
  }

  selectCountry(event: Event) {
    const value = (event.target as HTMLInputElement).value as unknown as Country.Country;
    this.addressForm().patchValue({ country: value });
    this.countryChangeKey.update((key) => key + 1);
    this.onCountrySelected.emit(value.id);
  }

  selectProvince(event: Event) {
    const value = (event.target as HTMLInputElement).value as unknown as State.State;
    this.addressForm().patchValue({ state: value });
    this.onStateSelected.emit(+value.id);
  }

  selectCity(event: Event) {
    const value = (event.target as HTMLInputElement).value as unknown as City.City;
    this.addressForm().patchValue({ city: value });
  }

  get eAddressType(): typeof AddressType {
    return AddressType;
  }

  addressLabel(): string {
    switch (this.formAddressType()) {
      case AddressType.Shipment:
        return this.translate.translate('addressLabel');
      case AddressType.Billing:
        return this.translate.translate('accountName');
      default:
        return this.translate.translate('addressTitle');
    }
  }

  getAddress(customerId?: number): Address {
    if (this.selectedAddressSelectionType() === eAddressSelectionMode.EXIST_ADDRESS) {
      return {
        ...this.deliveryAddressList().findCustomerAddressById(this.selectedExistAddressId()),
        id: undefined,
        rowId: customerId,
        addressType: this.formAddressType(),
      };
    } else {
      const country = this.addressForm().get('country')?.value as Country.Country;
      const state = this.addressForm().get('state')?.value as State.State;
      const city = this.addressForm().get('city')?.value as City.City;

      return {
        addressLabel: this.addressForm().get('addressLabel')?.value || '',
        countryId: country?.id,
        countryName: country?.name,
        countryCode: country?.countryCode,
        stateId: +state?.id,
        stateName: state?.name,
        stateCode: state?.stateCode,
        cityId: +city?.id,
        cityName: city?.name,
        postalCode: this.addressForm().get('postalCode')?.value || '',
        phoneNumber: (this.addressForm().get('phoneNumber')?.value as Phonenumber).phoneNumber || '',
        addressContact: this.addressForm().get('fullName')?.value || '',
        rowId: customerId,
        addressType: this.formAddressType(),
        addressDescription: this.addressForm().get('addressDescription')?.value || '',
      };
    }
  }

  addressTypeChange(event: Event) {
    const value = (event.target as HTMLInputElement).value;
    this.selectedAddressSelectionType.set(value as keyof typeof eAddressSelectionMode);
  }

  addressSelect(event: Event) {
    const value = +(event.target as HTMLInputElement).value;
    this.addressList().forEach((address) => (address.isSelected = address.value === value));
    this.selectedExistAddressId.set(value);
  }
}
