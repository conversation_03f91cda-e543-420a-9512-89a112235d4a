<eds-button appearance="subtle" iconOnly iconLeading="search" (button-click)="openSearch()"></eds-button>
<div #base class="wrapper">
  <div class="base">
    <widget-form-field [(ngModel)]="searchText" placeholder="Search"></widget-form-field>
    <eds-button appearance="primary" (button-click)="onSearch()">Search</eds-button>
    <eds-button
      class="search-close"
      appearance="subtle"
      iconOnly
      iconLeading="cancel"
      (button-click)="onClose()"
    ></eds-button>
  </div>
</div>
