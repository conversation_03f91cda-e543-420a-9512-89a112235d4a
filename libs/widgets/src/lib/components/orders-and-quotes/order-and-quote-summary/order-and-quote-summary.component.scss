:host {
  --eds-segmented-area-gap: var(--segmented-area-gap, var(--eds-spacing-600));
  --eds-segmented-area-content-gap: var(--segmented-area-content-gap, var(--eds-spacing-600));
  --eds-segmented-area-segment-background: var(--segmented-area-segment-background, var(--eds-colors-base-50));
}

:host ::ng-deep {
  [part='base'] {
    display: flex;
    flex-direction: column;
    gap: var(--eds-segmented-area-gap);
  }

  [part='segment'] {
    --eds-segmented-control-background: var(--eds-segmented-area-segment-background);
  }

  [part='content'] {
    display: flex;
    flex-direction: column;
    gap: var(--eds-segmented-area-content-gap);
  }
}

.card-container {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-600);
}
