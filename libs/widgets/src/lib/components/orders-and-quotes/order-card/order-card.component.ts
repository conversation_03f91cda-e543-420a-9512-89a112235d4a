import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  input,
  output,
  signal,
  ViewChild,
} from '@angular/core';
import { CustomerOrder, SelectOption } from '@libs/types';
import { TranslatePipe } from '@libs/plugins';
import { DatePipe } from '@angular/common';
import { getColorByProductStatus } from '@libs/core';
import { SuborderCardComponent } from '../suborder-card';
import { OrderFilterComponent } from '../order-filter';
import { EmptyFilterComponent, EmptyOrderComponent } from '@libs/widgets';

@Component({
  selector: 'widget-order-card',
  templateUrl: './order-card.component.html',
  styleUrls: ['./order-card.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [
    TranslatePipe,
    DatePipe,
    SuborderCardComponent,
    OrderFilterComponent,
    EmptyOrderComponent,
    EmptyFilterComponent,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OrderCardComponent {
  @ViewChild(OrderFilterComponent) orderFilterComponent: OrderFilterComponent;

  orders = input<Partial<CustomerOrder.InquireCustomerOrder[]>>([]);

  orderTypes = input<SelectOption[]>([]);
  orderStatuses = input<SelectOption[]>([]);

  appliedFilter = signal<boolean>(false);
  applyOrderFilter = output<CustomerOrder.InquireCustomerOrdersParams>();

  hasMore = input<boolean>(false);
  loading = input<boolean>(false);
  totalCount = input<number>(0);
  showCount = input<number>(0);
  loadMore = output<void>();

  goToDetail = output<number>();

  getOrderStatusAppearance(shortCode: string) {
    return getColorByProductStatus(shortCode);
  }

  renderDetailLink(shortCode: string) {
    return shortCode === CustomerOrder.OrderStatus.ESB || shortCode === CustomerOrder.OrderStatus.FULL_FINISHED;
  }

  onApplyFilter(params: CustomerOrder.InquireCustomerOrdersParams) {
    this.appliedFilter.set(!Object.values(params).every((value) => value));
    this.applyOrderFilter.emit(params);
  }
}
