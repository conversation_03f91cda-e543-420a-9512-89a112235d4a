import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, output, signal } from '@angular/core';
import { FormFieldComponent, TranslatePipe } from '@libs/plugins';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { CustomerOrder, SelectOption } from '@libs/types';

@Component({
  selector: 'widget-order-filter',
  templateUrl: './order-filter.component.html',
  styleUrls: ['./order-filter.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [TranslatePipe, ReactiveFormsModule, FormFieldComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OrderFilterComponent {
  orderTypes = input<SelectOption[]>([]);
  orderStatuses = input<SelectOption[]>([]);

  applyOrderFilter = output<CustomerOrder.InquireCustomerOrdersParams>();

  openFilterCard = signal<boolean>(false);

  filterForm = new FormGroup({
    orderId: new FormControl(''),
    orderType: new FormControl(''),
    status: new FormControl(''),
    phoneNumber: new FormControl(''),
    startDate: new FormControl(''),
    endDate: new FormControl(''),
  });

  totalFilteredControl = signal<number>(0);

  constructor() {
    this.filterForm.valueChanges.subscribe(() => {
      const filteredCount = Object.values(this.filterForm.controls).filter((control) => !!control.value).length;
      this.totalFilteredControl.set(filteredCount);
    });
  }

  collapseFilterCard() {
    this.openFilterCard.set(!this.openFilterCard());
  }

  clearForm() {
    this.filterForm.reset();

    this.totalFilteredControl.set(0);
    this.onApplyFilter();
  }

  onApplyFilter() {
    this.applyOrderFilter.emit(this.getFilterParams());
  }

  getFilterParams(): CustomerOrder.InquireCustomerOrdersParams {
    return {
      customerOrderId: +this.filterForm.get('orderId')?.value || null,
      orderTypeShortCode: this.filterForm.get('orderType')?.value || null,
      statusShortCode: this.filterForm.get('status')?.value || null,
      subscriptionIdentifier: this.filterForm.get('phoneNumber')?.value || null,
      submitDateFilterInputStartDate: this.formatDate(this.filterForm.get('startDate')?.value),
      submitDateFilterInputEndDate: this.formatDate(this.filterForm.get('endDate')?.value),
    };
  }

  private formatDate(dateString: string | null): string | null {
    if (!dateString) return null;
    const dateParts = dateString.split('/');
    return `${dateParts[2]}-${dateParts[0]}-${dateParts[1]}`;
  }
}
