.base {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: var(--eds-spacing-400);
  align-self: stretch;
}

.order {
  display: flex;
  padding: var(--eds-spacing-0);
  justify-content: space-between;
  align-items: center;
  align-self: stretch;

  .order-info {
    display: flex;
    align-items: center;
    gap: var(--eds-spacing-100);

    eds-text:first-of-type {
      color: var(--eds-colors-text-light);
    }
  }

  a {
    cursor: pointer;
  }

  eds-icon::part(base) {
    color: var(--eds-colors-icon-light);
    width: 20px;
    height: 20px;
    flex-shrink: 0;
  }
}

.business-interaction-spec {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: var(--eds-spacing-100);
  align-self: stretch;

  .date-info {
    display: flex;
    gap: var(--eds-spacing-100);

    eds-text:first-of-type {
      color: var(--eds-colors-text-light);
    }
  }

  .spec-info {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    align-self: stretch;
  }
}

.line {
  width: 100%;
  height: 1px;
  background: var(--eds-border-color-default);
}

.suborder-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
}

.suborder-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
  gap: var(--eds-spacing-200);

  .suborder-card {
    display: flex;
    align-items: center;
    align-self: stretch;
    padding: var(--eds-spacing-400);
    gap: var(--eds-spacing-600);

    border-radius: var(--eds-radius-300);
    border: 1px solid var(--eds-border-color-default);

    .suborder {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: var(--eds-spacing-400);
      flex: 1 0 0;

      .suborder-product-info {
        display: flex;
        align-items: flex-start;
        gap: var(--eds-spacing-200);
        align-self: stretch;

        eds-icon {
          color: var(--eds-colors-secondary-default);
        }
      }

      .device-card {
        display: flex;
        padding: var(--eds-spacing-300);
        flex-direction: column;
        align-items: flex-start;
        gap: var(--eds-spacing-300);
        align-self: stretch;

        border-radius: var(--eds-radius-200);
        border: 1px solid var(--eds-border-color-default);
      }
    }
  }
}

.alert {
  &::part(wrapper) {
    align-items: center;
  }

  &::part(icon) {
    width: var(--eds-sizing-400);
    height: var(--eds-sizing-400);
  }

  &.no-description {
    &::part(content) {
      display: none;
    }

    &::part(title) {
      --eds-font-size-heading-sm: var(--eds-font-size-body-md);
      --eds-heading-font-weight: regular;
    }
  }
}

.buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
