import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, inject, input } from '@angular/core';
import { CustomerOrder } from '@libs/types';
import { TranslatePipe } from '@libs/plugins';
import { CurrencyPipe, DatePipe } from '@angular/common';
import { SuborderCardComponent } from '../suborder-card';
import { QuotePriceData } from '@libs/bss';
import { EmptyQuoteComponentComponent } from '@libs/widgets';

@Component({
  selector: 'widget-quote-card',
  templateUrl: './quote-card.component.html',
  styleUrls: ['./quote-card.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [TranslatePipe, DatePipe, SuborderCardComponent, EmptyQuoteComponentComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [CurrencyPipe],
})
export class QuoteCardComponent {
  currencyPipe = inject(CurrencyPipe);

  quotes = input<Partial<CustomerOrder.InquireCustomerOrder[]>>([]);
  quotesPriceDetailMap = input<Record<number, QuotePriceData>>({});

  totalAmountByCustomerOrderId(id: number): string | number {
    return this.currencyPipe.transform(this.quotesPriceDetailMap()?.[id]?.price?.total) ?? 0;
  }
}
