import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  effect,
  forwardRef,
  input,
  OnDestroy,
} from '@angular/core';
import {
  AbstractControl,
  ControlValueAccessor,
  FormControl,
  FormGroup,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
  ValidationErrors,
  Validator,
  Validators,
} from '@angular/forms';
import { createPhoneNumberMask, FormFieldComponent, TranslatePipe } from '@libs/plugins';
import { SelectOption, Phonenumber } from '@libs/widgets';
import { Subscription } from 'rxjs';

@Component({
  selector: 'widget-phonenumber',
  templateUrl: './phone-number.component.html',
  styleUrls: ['./phone-number.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [ReactiveFormsModule, FormFieldComponent, TranslatePipe],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => PhoneNumberComponent),
      multi: true,
    },
    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => PhoneNumberComponent),
      multi: true,
    },
  ],
})
export class PhoneNumberComponent implements ControlValueAccessor, OnDestroy, Validator {
  countryOptions = input<SelectOption[]>([]);
  required = input<boolean>(false);

  phoneNumberMask = createPhoneNumberMask();

  form = new FormGroup({
    country: new FormControl(''),
    phoneNumber: new FormControl('', [Validators.minLength(10)]),
  });

  private valueSub: Subscription;

  onChange: (value: Phonenumber | null) => void = () => {};
  onTouched: () => void = () => {};

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  validate(_control: AbstractControl): ValidationErrors | null {
    if (this.form.valid) {
      return null;
    }

    const errors: ValidationErrors = {};
    if (this.form.controls.country.errors) {
      errors['country'] = this.form.controls.country.errors;
    }
    if (this.form.controls.phoneNumber.errors) {
      errors['phoneNumber'] = this.form.controls.phoneNumber.errors;
    }
    return errors;
  }

  writeValue(obj: Phonenumber | null): void {
    if (obj) {
      this.form.patchValue(obj, { emitEvent: false });
    } else {
      this.form.reset(undefined, { emitEvent: false });
    }
  }

  registerOnChange(fn: (value: Phonenumber | null) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState?(isDisabled: boolean): void {
    if (isDisabled) {
      this.form.disable();
    } else {
      this.form.enable();
    }
  }

  constructor() {
    this.valueSub = this.form.valueChanges.subscribe((value) => {
      const { country, phoneNumber } = value;
      if ((!country || country === '') && (!phoneNumber || phoneNumber === '')) {
        this.onChange(null);
      } else {
        this.onChange(value as Phonenumber);
      }
    });

    effect(() => {
      if (this.required()) {
        this.form.get('country')?.setValidators([Validators.required]);
        this.form.get('country')?.updateValueAndValidity({ emitEvent: false });
        this.form.get('phoneNumber')?.setValidators([Validators.required, Validators.minLength(10)]);
        this.form.get('phoneNumber')?.updateValueAndValidity({ emitEvent: false });
      } else {
        this.form.get('country')?.clearValidators();
        this.form.get('country')?.updateValueAndValidity({ emitEvent: false });
        this.form.get('phoneNumber')?.setValidators([Validators.minLength(10)]);
        this.form.get('phoneNumber')?.updateValueAndValidity({ emitEvent: false });
      }
    });
  }

  ngOnDestroy(): void {
    this.valueSub?.unsubscribe();
  }
}
