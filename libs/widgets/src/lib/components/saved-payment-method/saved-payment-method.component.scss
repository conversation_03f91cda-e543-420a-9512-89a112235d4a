.base {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--eds-spacing-200);
  border: 1px solid var(--eds-border-color-default);
  border-radius: var(--eds-radius-200);
  width: 100%;

  input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    margin: 0;
    z-index: -1;
  }

  &:has(input:checked) {
    background-color: var(--eds-colors-surface-level-2);
  }

  .content {
    display: flex;
    flex-direction: column;
    gap: var(--eds-spacing-300);
    flex: 1;

    .network {
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--eds-colors-surface-default);
      flex-shrink: 0;
      width: calc(var(--eds-size-multiplier) * 15);
      height: calc(var(--eds-size-multiplier) * 10);
      padding: var(--eds-spacing-100);
      border-radius: var(--eds-radius-200);
      border: 1px solid var(--eds-border-color-default);
    }

    .card-details {
      display: flex;
      flex-direction: column;
      gap: var(--eds-spacing-100);

      .bank-name {
        eds-text {
          padding-right: calc(var(--eds-size-multiplier));
        }
      }

      eds-text {
        display: flex;
        align-items: center;
        min-height: var(--eds-sizing-500);
        padding-right: calc(var(--eds-size-multiplier) * 11);

        &::part(base) {
          word-break: break-all;
        }
      }

      .card-number {
        display: flex;
        flex-direction: column;
        gap: var(--eds-spacing-100);

        eds-text {
          display: flex;
          align-items: center;
          min-height: var(--eds-sizing-400);

          &::part(base) {
            white-space: nowrap;
          }
        }

        @media (min-width: 834px) {
          flex-direction: row;
          align-items: flex-end;
          gap: var(--eds-spacing-400);
        }
      }
    }

    @media (min-width: 834px) {
      flex-direction: row;
      align-items: center;
    }
  }

  widget-item-delete-action {
    position: absolute;
    top: 50%;
    right: 0;
    padding: var(--eds-spacing-200);
    transform: translateY(-50%);
  }

  @media (min-width: 834px) {
    padding: var(--eds-spacing-300);

    widget-item-delete-action {
      padding: var(--eds-spacing-300);
    }
  }
}
