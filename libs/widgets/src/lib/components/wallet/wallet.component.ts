import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { InteractionListComponent } from '../interaction-list';
import { Interaction } from '../../model';

@Component({
  selector: 'widget-wallet',
  templateUrl: './wallet.component.html',
  styleUrls: ['./wallet.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [InteractionListComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class WalletComponent {
  actionListItems = input<Interaction[]>([]);
  price = input<string>('');
  subTitle = input<string>('');
  widgetHeaderName = input<string>('');
}
