:host {
  --eds-wallet-card-gap: var(--wallet-card-gap, var(--eds-spacing-600));

  --eds-wallet-card-content-background-color: var(
    --wallet-card-content-background-color,
    var(--eds-colors-surface-level-1)
  );
  --eds-wallet-card-content-padding: var(--wallet-card-content-padding, var(--eds-spacing-400));
  --eds-wallet-card-content-gap: var(--wallet-card-content-gap, var(--eds-spacing-100));
  --eds-wallet-card-content-border-properties: var(
    --wallet-card-content-border-properties,
    var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default)
  );
  --eds-wallet-card-content-border-radius: var(--wallet-card-content-border-radius, var(--eds-radius-300));
}
.base,
[part='base'] {
  display: flex;
  flex-direction: column;
  row-gap: var(--eds-wallet-card-gap);
}
.content,
[part='content'] {
  display: flex;
  flex-direction: column;
  padding: var(--eds-wallet-card-content-padding);
  background-color: var(--eds-wallet-card-content-background-color);
  gap: var(--eds-wallet-card-content-gap);
  border: var(--eds-wallet-card-content-border-properties);
  border-radius: var(--eds-wallet-card-content-border-radius);
}
