<div class="base">
  <div class="items" #navigationItems>
    @for (item of items(); track $index) {
      @if (item.items) {
        <div class="item">
          <eds-button
            appearance="subtle"
            iconTrailing="arrowDown"
            shouldFitContainer
            (button-click)="openMegaMenu($index)"
          >
            {{ item.label }}
          </eds-button>
          <eds-accordion
            [caption]="item.label"
            [isOpen]="isAccordionOpen($index)"
            (accordion-click)="toggleAccordion($index)"
          >
            <div class="accordion-header" slot="header">
              <eds-text [text]="item.label"></eds-text>
              <eds-icon name="arrowDown"></eds-icon>
            </div>
            @for (menuItem of item.items; track $index) {
              <div class="menu-topic">
                <div class="menu-topic-header">
                  <eds-text size="lg" [text]="menuItem.label"></eds-text>
                  <eds-icon name="arrowRight"></eds-icon>
                </div>
                <div class="topic-links">
                  @for (link of menuItem.items; track $index) {
                    @if (link.isExternal) {
                      <eds-link [href]="link.href" target="_blank">{{ link.label }}</eds-link>
                    } @else {
                      <eds-text [routerLink]="link.href" [text]="link.label"></eds-text>
                    }
                  }
                </div>
              </div>
            }
          </eds-accordion>
        </div>
      } @else {
        <eds-button appearance="subtle" [href]="item.href">{{ item.label }}</eds-button>
      }
    }
  </div>
  @if (quickLinks().length) {
    <div class="quick-links">
      @for (item of quickLinks(); track $index) {
        <eds-button appearance="subtle" iconTrailing="arrowDown" [href]="item.href">{{ item.label }}</eds-button>
      }
    </div>
  }
</div>
<div class="mega-menu" [class.open]="isMegaMenuOpen()" #megaMenu>
  @for (item of items(); track $index) {
    @if (item.items && isActiveMenuIndex($index)) {
      <div class="menus-wrapper active">
        <div class="menus">
          <div class="menu">
            @for (menuItem of item.items; track $index) {
              <div class="menu-topic">
                <div class="menu-topic-header">
                  <eds-text size="lg" [text]="menuItem.label"></eds-text>
                  <eds-icon name="arrowRight"></eds-icon>
                </div>
                <div class="topic-links">
                  @for (link of menuItem.items; track $index) {
                    @if (link.isExternal) {
                      <eds-link [href]="link.href" target="_blank">{{ link.label }}</eds-link>
                    } @else {
                      <eds-text [routerLink]="link.href" [text]="link.label"></eds-text>
                    }
                  }
                </div>
              </div>
            }
          </div>
        </div>
        @if (item.promotion) {
          <div class="promotion" style="--background-image: url('{{ item.promotion.image }}')">
            <eds-heading size="md" text="Promotion"></eds-heading>
            <eds-text size="md" [text]="item.promotion.description"></eds-text>
            <eds-button appearance="default" iconLeading="arrowRight" [href]="item.promotion.href"
              >Go to promotion
            </eds-button>
          </div>
        }
      </div>
    }
  }
</div>
