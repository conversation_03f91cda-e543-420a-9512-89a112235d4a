<widget-segment-area part="base" [segmentedOptions]="productSummaryData().productOptions">
  <div class="top-info">
    <div>
      {{ 'productCountOfTotal' | translate: { count: showCount(), total: totalCount() } }}
    </div>
    <div>
      <eds-checkbox id="includeInactive" [(ngModel)]="includeCancelled">
        <label for="includeInactive">
          {{ 'includeCancelledProducts' | translate }}
        </label>
      </eds-checkbox>
    </div>
  </div>
  @for (item of productSummaryData().productSummary; track $index) {
    <div segment [id]="item.id" part="content">
      <eds-card [title]="item.title">
        @for (product of item.data; track $index) {
          <widget-product-card [product]="product"></widget-product-card>
        }
      </eds-card>
    </div>
  }
  @if (hasMore()) {
    <button class="load-more-button" [class.loading]="loading()" (click)="loadMore.emit()" [disabled]="loading()">
      <span>Load More</span>
      <eds-icon name="arrowDown"></eds-icon>
    </button>
  }
</widget-segment-area>
