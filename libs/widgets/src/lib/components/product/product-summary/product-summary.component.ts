import { Component, computed, CUSTOM_ELEMENTS_SCHEMA, input, model, output } from '@angular/core';
import {
  ProductSummary,
  ProductSummaryData,
  SegmentAreaComponent,
  SegmentDirective,
  SegmentedAreaKnowledgeKeys,
  UsageOption,
} from '@libs/widgets';
import { ProductCardComponent } from '../product-card';
import { CheckboxDirective, groupByUniqueKey } from '@libs/core';
import { TranslatePipe } from '@libs/plugins';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'widget-product-summary',
  templateUrl: './product-summary.component.html',
  styleUrls: ['./product-summary.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [
    ProductCardComponent,
    SegmentAreaComponent,
    SegmentDirective,
    TranslatePipe,
    FormsModule,
    CheckboxDirective,
  ],
})
export class ProductSummaryComponent {
  products = input<ProductSummary[]>([]);

  allText = input<string>('All');
  hasMore = input<boolean>(false);
  loading = input<boolean>(false);
  totalCount = input<number>(0);
  showCount = input<number>(0);
  includeCancelled = model<boolean>(false);

  loadMore = output<void>();

  productSummaryData = computed(() => {
    const groupedDataEntries = Object.entries(groupByUniqueKey(this.products(), 'category'));

    return {
      productOptions: [
        {
          text: this.allText(),
          ariaControls: SegmentedAreaKnowledgeKeys.ALL,
        },
        ...groupedDataEntries.map(([key]) => ({
          text: key,
          ariaControls: key,
        })),
      ],
      productSummary: groupedDataEntries.map(([key, value]) => ({
        id: key,
        title: key,
        data: value,
      })),
    } as { productOptions: UsageOption[]; productSummary: ProductSummaryData[] };
  });
}
