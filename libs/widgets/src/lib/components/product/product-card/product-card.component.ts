import { Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import type { ProductSummary } from '../../../model';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'widget-product-card',
  templateUrl: './product-card.component.html',
  styleUrls: ['./product-card.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [RouterLink],
})
export class ProductCardComponent {
  product = input<Partial<ProductSummary>>();
}
