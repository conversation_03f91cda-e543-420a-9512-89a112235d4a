:host {
  --eds-product-card-border-properties: var(
    --product-card-border-properties,
    var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default)
  );
  --eds-product-card-border-radius: var(--product-card-border-radius, var(--eds-radius-300));
  --eds-product-card-background-color: var(--product-card-background-color, var(--eds-colors-surface-default));
  --eds-product-card-disabled-background-color: var(
    --product-card-disabled-background-color,
    var(--eds-colors-surface-disabled)
  );
  --eds-product-card-disabled-text-color: var(
    --product-card-disabled-background-color,
    var(--eds-colors-text-disabled)
  );
  --eds-product-card-gap: var(--product-card-gap, var(--eds-spacing-400));
  --eds-product-card-padding: var(--product-card-padding, var(--eds-spacing-400));

  --eds-product-card-info-gap: var(--product-card-info-gap, var(--eds-spacing-200));
  --eds-product-card-owner-text-color: var(--product-card-owner-text-color, var(--eds-colors-text-default));
  --eds-product-card-details-gap: var(--product-card-details-gap, var(--eds-spacing-300));
  --eds-product-card-details-content-gap: var(--product-card-details-content-gap, var(--eds-spacing-100));
  --eds-product-card-details-align-items: var(--product-card-details-align-items, flex-start);
  --eds-product-card-details-icon-size: var(--product-card-details-icon-size, var(--eds-sizing-600));
  --eds-product-card-details-icon-color: var(--product-card-details-icon-color, var(--eds-colors-secondary-default));

  --eds-product-card-product-name-color: var(--product-card-product-name-color, var(--eds-colors-text-dark));
  --eds-product-card-product-number-color: var(--product-card-product-number-color, var(--eds-colors-text-default));

  --eds-product-card-device-border-properties: var(--product-card-border-properties, none);
  --eds-product-card-device-border-radius: var(--product-card-border-radius, var(--eds-radius-200));
  --eds-product-card-device-padding: var(--product-card-device-padding, var(--eds-spacing-300));
  --eds-product-card-device-background: var(--product-card-device-background, var(--eds-colors-surface-level-1));

  --eds-product-card-devices-gap: var(--product-card-devices-gap, var(--eds-spacing-200));

  --eds-product-card-media-object-image-border: var(
    --product-card-media-object-image-border,
    var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default)
  );
  --eds-product-card-media-object-image-border-radius: var(
    --product-card-media-object-image-border-radius,
    var(--eds-radius-200)
  );
  --eds-product-card-media-object-image-background-color: var(
    --product-card-media-object-image-background-color,
    var(--eds-colors-surface-default)
  );
  --eds-product-card-media-object-image-width: var(--product-card-media-object-image-width, var(--eds-sizing-900));
  --eds-product-card-media-object-image-height: var(--product-card-media-object-image-height, var(--eds-sizing-900));
  --eds-product-card-media-object-image-padding: var(--product-card-media-object-image-padding, var(--eds-spacing-200));

  --eds-product-card-device-details-gap: var(--product-card-device-details-gap, var(--eds-spacing-400));
  --eds-product-card-device-text-color: var(--product-card-device-text-color, var(--eds-colors-text-default));

  @media (min-width: 834px) {
    --eds-product-card-padding: var(--product-card-padding, var(--eds-spacing-600));
  }
}

[part='base'] {
  text-decoration: none;
  border: var(--eds-product-card-border-properties);
  border-radius: var(--eds-product-card-border-radius);
  padding: var(--eds-product-card-padding);
  background-color: var(--eds-product-card-background-color);
  display: flex;
  flex-direction: column;
  gap: var(--eds-product-card-gap);
  user-select: none;

  &.grey {
    background-color: var(--eds-product-card-disabled-background-color);

    & [part='owner'],
    & [part='details']::part(text),
    & [part='details']::part(description),
    & [part='device-details']::part(text) {
      --eds-text-color: var(--eds-product-card-disabled-text-color);
    }

    & [part='details'] {
      --eds-media-object-icon-color: var(--eds-product-card-disabled-text-color);
    }
  }
}

[part='info'] {
  display: flex;
  justify-content: space-between;
  gap: var(--eds-product-card-info-gap);
  flex-direction: column-reverse;

  @media (min-width: 834px) {
    flex-direction: row;
  }
}

[part='owner'] {
  --eds-text-color: var(--eds-product-card-owner-text-color);
}

[part='details'] {
  --eds-media-object-gap: var(--eds-product-card-details-gap);
  --eds-media-object-align-items: var(--eds-product-card-details-align-items);
  --eds-media-object-icon-size: var(--eds-product-card-details-icon-size);
  --eds-media-object-icon-color: var(--eds-product-card-details-icon-color);
  --eds-media-object-content-gap: var(--eds-product-card-details-content-gap);

  &::part(text) {
    --eds-text-color: var(--eds-product-card-product-name-color);
  }

  &::part(description) {
    --eds-text-color: var(--eds-product-card-product-number-color);
  }
}

[part='devices'] {
  display: flex;
  flex-direction: column;
  gap: var(--eds-product-card-devices-gap);

  &:not(:has([part='device'])) {
    display: none;
  }
}

[part='device'] {
  padding: var(--eds-product-card-device-padding);
  background-color: var(--eds-product-card-device-background);
  border: var(--eds-product-card-device-border-properties);
  border-radius: var(--eds-product-card-device-border-radius);
}

[part='device-details'] {
  --eds-media-object-gap: var(--eds-product-card-device-details-gap);
}

[part='device-details']::part(text) {
  --eds-text-color: var(--eds-product-card-device-text-color);
}

[part='status'] {
  display: flex;
  gap: var(--eds-spacing-200);
  flex-shrink: 0;
  justify-content: space-between;

  @media (min-width: 834px) {
    flex-direction: column;
    align-items: flex-end;
    justify-content: flex-start;
  }
}

.date {
  display: flex;
  align-items: center;
  gap: var(--eds-spacing-100);

  eds-icon {
    width: var(--eds-sizing-400);
    height: var(--eds-sizing-400);
    flex-shrink: 0;
    &::part(base) {
      color: var(--eds-colors-text-default);
    }
  }

  eds-text::part(base) {
    text-align: right;
  }
}

eds-media-object::part(image-wrapper) {
  border: var(--eds-product-card-media-object-image-border);
  border-radius: var(--eds-product-card-media-object-image-border-radius);
  background-color: var(--eds-product-card-media-object-image-background-color);
  width: var(--eds-product-card-media-object-image-width);
  height: var(--eds-product-card-media-object-image-height);
  padding: var(--eds-product-card-media-object-image-padding);
}
