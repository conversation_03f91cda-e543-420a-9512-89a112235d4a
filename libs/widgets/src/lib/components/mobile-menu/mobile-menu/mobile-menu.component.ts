import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  ViewChild,
  ElementRef,
  OnDestroy,
  NgZone,
  ChangeDetectorRef,
  model,
  input,
} from '@angular/core';

@Component({
  selector: 'widget-mobile-menu',
  templateUrl: './mobile-menu.component.html',
  styleUrls: ['./mobile-menu.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MobileMenuComponent implements OnDestroy {
  mobileUserTypeMenu = input<boolean>(true);

  @ViewChild('base') base!: ElementRef<HTMLElement>;

  isMenuOpen = false;

  userType = model<string>('personal');
  languagePreferences = model<boolean>();

  private resizeListener: () => void;

  constructor(
    private ngZone: NgZone,
    private cdr: ChangeDetectorRef,
  ) {
    this.resizeListener = this.handleResize.bind(this);
  }

  ngOn<PERSON><PERSON>roy(): void {
    this.removeResizeListener();
    document.body.removeAttribute('style');
  }

  private handleResize(): void {
    this.ngZone.run(() => {
      this.closeMenu();
      this.cdr.markForCheck();
    });
  }

  private addResizeListener(): void {
    this.ngZone.runOutsideAngular(() => {
      window.addEventListener('resize', this.resizeListener);
    });
  }

  private removeResizeListener(): void {
    window.removeEventListener('resize', this.resizeListener);
  }

  toggleMenu(): void {
    this.isMenuOpen = !this.isMenuOpen;

    if (this.isMenuOpen) {
      document.body.style.overflow = 'hidden';
      document.body.style.maxHeight = '100svh';
      this.addResizeListener();
    } else {
      this.removeResizeListener();
    }
  }

  closeMenu(): void {
    if (this.isMenuOpen) {
      this.isMenuOpen = false;
      this.removeResizeListener();
    }
    document.body.removeAttribute('style');
    this.languagePreferences.set(false);
  }

  changeUserType(event: Event): void {
    const clickedButton = event.currentTarget as HTMLElement;
    const newUserType = clickedButton.textContent?.toLowerCase() || 'personal';
    this.userType.set(newUserType);
  }

  openLanguagePreference(): void {
    this.languagePreferences.set(true);
  }
}
