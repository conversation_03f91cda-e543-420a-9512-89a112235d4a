.base {
  max-height: 100svh;
  .menu {
    position: fixed;
    inset: 0;
    max-height: 100svh;
    z-index: 10000;
    display: grid;
    grid-template-rows: 1fr var(--header-language-preference-row) minmax(0, 100%);
    gap: var(--eds-spacing-600);
    padding: var(--eds-spacing-400);
    background-color: var(--eds-colors-body);
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;

    &.open {
      transform: translateX(0);
    }

    .language-container {
      --language-preference-background-color: var(--eds-colors-surface-default);
      --language-preference-text-color: var(--eds-colors-text-dark);
      --language-preference-border-radius: var(--eds-radius-300);
      --language-preference-border-properties: var(--eds-stroke-025) solid var(--eds-border-color-light);

      width: 100%;
      height: 100%;
      overflow: var(--header-language-preference-overflow);
    }

    .menu-header {
      display: flex;
      flex-direction: column;
      gap: var(--eds-spacing-400);

      .actions {
        position: relative;
        display: flex;
        justify-content: flex-end;
      }

      .user-type {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--eds-spacing-200);
        padding: var(--eds-spacing-200) var(--eds-spacing-400);
        background-color: var(--eds-colors-surface-default);
        border-radius: var(--eds-radius-200);

        eds-button::part(base) {
          --eds-button-padding: 0;
        }

        eds-button.active::part(base) {
          --eds-button-subtle-text-color: var(--eds-colors-secondary-default);
        }
      }
    }

    .menu-content {
      display: flex;
      flex-direction: column;
      gap: var(--eds-spacing-400);
      max-height: 100%;

      .navigation {
        overflow-y: auto;
        flex-shrink: 0;

        &::-webkit-scrollbar {
          display: none;
        }
      }

      .login {
        overflow-y: hidden;
      }
    }

    @media (min-width: 834px) {
      display: none;
    }
  }
}

.hamburger-menu {
  @media (min-width: 834px) {
    display: none;
  }
}
