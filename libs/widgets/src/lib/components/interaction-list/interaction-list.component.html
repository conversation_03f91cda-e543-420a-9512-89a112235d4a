<div class="action-list" part="base">
  @for (item of displayedItems; track $index) {
    <a (click)="onClick(item)" class="action">
      <div part="label">
        @if (item.elemBefore) {
          <span part="elem-before" [innerHTML]="item.elemBefore"></span>
        }
        <eds-text as="p" [text]="item.text" weight="medium" size="lg"></eds-text>
      </div>
      <eds-icon name="arrowRight" class="icon"></eds-icon>
    </a>
  }

  @if (itemsSize() && actions().length > itemsSize()) {
    <div class="action-show-more" [class.open]="isExpanded()" (click)="toggleShowMore()">
      <eds-text as="p" [text]="isExpanded() ? expandedText() : unexpandedText()" weight="medium" size="md"></eds-text>
      <eds-icon name="arrowDown" class="icon"></eds-icon>
    </div>
  }
</div>
