import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, output, signal } from '@angular/core';
import { Interaction } from '../../model';

@Component({
  selector: 'widget-interaction-list',
  templateUrl: './interaction-list.component.html',
  styleUrls: ['./interaction-list.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InteractionListComponent {
  itemsSize = input(0);
  expandedText = input('');
  unexpandedText = input('');
  actions = input<Interaction[]>([]);

  isExpanded = signal(false);

  itemClick = output<Interaction>();

  toggleShowMore(): void {
    this.isExpanded.update((expanded) => !expanded);
  }

  get displayedItems(): Interaction[] {
    return this.isExpanded() || !this.itemsSize() ? this.actions() : this.actions().slice(0, this.itemsSize());
  }

  onClick(item: Interaction) {
    this.itemClick.emit(item);
    if (item.onClick) {
      item.onClick();
      return false;
    }
    return true;
  }
}
