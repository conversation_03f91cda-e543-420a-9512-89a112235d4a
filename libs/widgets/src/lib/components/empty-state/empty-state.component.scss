:host {
  --eds-empty-state-border-color: var(--empty-state-border-color, var(--eds-border-color-default));
  --eds-empty-state-text-color: var(--empty-state-text-color, var(--eds-colors-text-light));
  --eds-empty-state-icon-color: var(--empty-state-icon-color, var(--eds-colors-icon-light));
  --eds-empty-state-padding: var(--empty-state-padding, calc(var(--eds-size-multiplier) * 28) 0);
  --eds-empty-state-border-radius: var(--empty-state-border-radius, var(--eds-sizing-600));
  --eds-empty-state-row-gap: var(--empty-state-row-gap, var(--eds-spacing-400));
  --eds-empty-state-font-size: var(--empty-state-font-size, var(--font-sizing-body-md));
}

.empty-state {
  display: flex;
  flex-direction: column;
  row-gap: var(--eds-empty-state-row-gap);
  align-items: center;
  border: 1px dashed var(--eds-empty-state-border-color);
  padding: var(--eds-empty-state-padding);
  border-radius: var(--eds-empty-state-border-radius);

  .empty-state__icons {
    display: flex;
    gap: var(--eds-empty-state-gap);
    color: var(--eds-empty-state-icon-color);
  }

  eds-text {
    font-size: var(--eds-empty-state-font-size);
    color: var(--eds-empty-state-text-color);
  }
}
