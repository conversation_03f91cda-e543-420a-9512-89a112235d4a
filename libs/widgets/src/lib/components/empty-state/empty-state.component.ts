import { Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import {
  AtomIconCellularNetworkComponent,
  AtomIconSimcardComponent,
  AtomIconSmartPhoneComponent,
  AtomIconTabletComponent,
} from '../svg-icons';
import { IconComponent } from '../icon';
import { Icon } from '../../model';

@Component({
  selector: 'widget-empty-state',
  templateUrl: './empty-state.component.html',
  styleUrls: ['./empty-state.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [IconComponent],
})
export class EmptyStateComponent {
  icons = input<Icon[]>([
    AtomIconTabletComponent,
    AtomIconCellularNetworkComponent,
    AtomIconSmartPhoneComponent,
    AtomIconSimcardComponent,
  ]);

  text = input<string>();
}
