import { Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { NgClass } from '@angular/common';

@Component({
  selector: 'widget-checkout-card',
  templateUrl: './checkout-card.component.html',
  styleUrls: ['./checkout-card.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [NgClass],
})
export class CheckoutCardComponent {
  title = input('');
  icon = input('');
  itemAmount = input<number>(0);
  isCompleted = input(false);
  isActive = input(false);
}
