import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { Alert, DataList, Tile } from '../../model';

@Component({
  selector: 'widget-plan-card',
  imports: [],
  templateUrl: './plan-card.component.html',
  styleUrls: ['./plan-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class PlanCardComponent {
  dataList = input<DataList>({
    itemsSize: 5,
    trim: true,
    expandedText: '',
    unexpandedText: '',
    items: [],
  });

  tileItems = input<Tile[]>([]);

  alert = input<Alert>({
    showIcon: false,
    iconName: '',
    title: '',
    description: '',
    appearance: 'info',
  });
}
