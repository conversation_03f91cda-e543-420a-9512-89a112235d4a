<div class="base">
  <eds-data-list
    class="details"
    [itemsSize]="dataList().itemsSize"
    [trim]="dataList().trim"
    [expandedText]="dataList().expandedText"
    [unexpandedText]="dataList().unexpandedText"
    [items]="dataList().items"
  ></eds-data-list>

  @if (alert()) {
    <eds-alert
      [showIcon]="alert().showIcon"
      [iconName]="alert().iconName"
      [title]="alert().title"
      [description]="alert().description"
      [appearance]="alert().appearance"
      descriptionWeight="medium"
      class="alert"
    ></eds-alert>
  }

  @if (tileItems()?.length) {
    <div class="tile-container">
      @for (item of tileItems(); track $index) {
        <eds-tile appearance="primary" [iconName]="item.iconName" [title]="item.title" [text]="item.text"></eds-tile>
      }
    </div>
  }
</div>
