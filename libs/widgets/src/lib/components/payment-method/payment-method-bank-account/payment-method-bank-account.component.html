<div class="payment-method_bank-account">
  @if (savedBankAccounts().length > 0) {
    <div class="payment-method_saved-payment-methods">
      <div class="payment-method_saved-payment-methods_default">
        <eds-text [size]="'sm'" [weight]="'medium'" text="Default Payment Option"></eds-text>
        @for (savedPaymentMethod of savedBankAccounts(); track savedPaymentMethod.id) {
          @if (savedPaymentMethod.default) {
            <widget-saved-payment-method
              [id]="savedPaymentMethod.id + uuid"
              [nameOnBankAccount]="savedPaymentMethod.nameOnBankAccount"
              [name]="savedPaymentMethod.name"
              [inputName]="'defaultBankAccount' + uuid"
              [value]="savedPaymentMethod.value"
              [isChecked]="savedPaymentMethod.isChecked"
              [networkLogo]="savedPaymentMethod.networkLogo"
              [cardNumber]="savedPaymentMethod.cardNumber"
              [cardExpiry]="savedPaymentMethod.cardExpiry"
              [isRemovable]="savedPaymentMethod.isRemovable"
              (delete)="handleDelete(savedPaymentMethod.id)"
              (selectionChange)="selectionChange.emit($event)"
            ></widget-saved-payment-method>
          }
        }
      </div>
      <div class="payment-method_saved-payment-methods_saved">
        <eds-text [size]="'sm'" [weight]="'medium'" text="Saved Payment Options"></eds-text>
        @for (savedPaymentMethod of savedBankAccounts(); track savedPaymentMethod.id) {
          @if (!savedPaymentMethod.default) {
            <widget-saved-payment-method
              [id]="savedPaymentMethod.id + uuid"
              [nameOnBankAccount]="savedPaymentMethod.nameOnBankAccount"
              [name]="savedPaymentMethod.name"
              [inputName]="'savedBankAccount' + uuid"
              [value]="savedPaymentMethod.value"
              [isChecked]="savedPaymentMethod.isChecked"
              [networkLogo]="savedPaymentMethod.networkLogo"
              [cardNumber]="savedPaymentMethod.cardNumber"
              [cardExpiry]="savedPaymentMethod.cardExpiry"
              [isRemovable]="savedPaymentMethod.isRemovable"
              (delete)="handleDelete(savedPaymentMethod.id)"
              (selectionChange)="selectionChange.emit($event)"
            ></widget-saved-payment-method>
          }
        }
      </div>
      <eds-button
        [size]="'compact'"
        [appearance]="'default'"
        iconLeading="plusCircle"
        (button-click)="createBankAccount.emit()"
        >Add bank account</eds-button
      >
    </div>
  } @else {
    <widget-bank-account-form (addBankAccount)="createBankAccount.emit($event)"></widget-bank-account-form>
  }
</div>
