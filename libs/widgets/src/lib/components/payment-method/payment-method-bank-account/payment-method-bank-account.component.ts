import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';
import { SavedPaymentMethod } from '../../../model/payment-method.model';
import { SavedPaymentMethodComponent } from '../../saved-payment-method';
import { BankAccountFormComponent } from '../../../forms/bank-account-form/bank-account-form.component';
import { BankAccountFormFields } from '@libs/widgets';
import { uuid } from '@libs/bss';
@Component({
  selector: 'widget-payment-method-bank-account',
  templateUrl: './payment-method-bank-account.component.html',
  styleUrls: ['./payment-method-bank-account.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [SavedPaymentMethodComponent, BankAccountFormComponent],
})
export class PaymentMethodBankAccountComponent {
  savedBankAccounts = input<SavedPaymentMethod[]>([]);

  createBankAccount = output<BankAccountFormFields | void>();
  onDelete = output<string | number>();
  selectionChange = output<string>();

  uuid = uuid();

  handleDelete(id: string | number) {
    this.onDelete.emit(id);
  }
}
