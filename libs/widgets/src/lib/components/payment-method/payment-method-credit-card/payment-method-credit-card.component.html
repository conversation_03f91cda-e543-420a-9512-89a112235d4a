<div class="payment-method_credit-card">
  @if (savedCreditCards().length > 0) {
    <div class="payment-method_saved-payment-methods">
      <div class="payment-method_saved-payment-methods_default">
        <eds-text [size]="'sm'" [weight]="'medium'" text="Default Payment Option"></eds-text>
        @for (savedPaymentMethod of savedCreditCards(); track savedPaymentMethod.id) {
          @if (savedPaymentMethod.default) {
            <widget-saved-payment-method
              [id]="savedPaymentMethod.id + uuid"
              [name]="savedPaymentMethod.name"
              [inputName]="'defaultCreditCard' + uuid"
              [value]="savedPaymentMethod.value"
              [isChecked]="savedPaymentMethod.isChecked"
              [networkLogo]="savedPaymentMethod.networkLogo"
              [cardNumber]="savedPaymentMethod.cardNumber"
              [cardExpiry]="savedPaymentMethod.cardExpiry"
              [isRemovable]="savedPaymentMethod.isRemovable"
              (delete)="handleDelete(savedPaymentMethod.id)"
              (selectionChange)="selectionChange.emit($event)"
            ></widget-saved-payment-method>
          }
        }
      </div>
      <div class="payment-method_saved-payment-methods_saved">
        <eds-text [size]="'sm'" [weight]="'medium'" text="Saved Payment Options"></eds-text>
        @for (savedPaymentMethod of savedCreditCards(); track savedPaymentMethod.id) {
          @if (!savedPaymentMethod.default) {
            <widget-saved-payment-method
              [id]="savedPaymentMethod.id + uuid"
              [name]="savedPaymentMethod.name"
              [inputName]="'savedCreditCard' + uuid"
              [value]="savedPaymentMethod.value"
              [isChecked]="savedPaymentMethod.isChecked"
              [networkLogo]="savedPaymentMethod.networkLogo"
              [cardNumber]="savedPaymentMethod.cardNumber"
              [cardExpiry]="savedPaymentMethod.cardExpiry"
              [isRemovable]="savedPaymentMethod.isRemovable"
              (delete)="handleDelete(savedPaymentMethod.id)"
              (selectionChange)="selectionChange.emit($event)"
            ></widget-saved-payment-method>
          }
        }
      </div>
      <eds-button
        [size]="'compact'"
        [appearance]="'default'"
        iconLeading="plusCircle"
        (button-click)="createCreditCard.emit()"
        >Add credit card</eds-button
      >
    </div>
  } @else {
    <widget-credit-card-form (addCreditCard)="createCreditCard.emit($event)"></widget-credit-card-form>
  }
</div>
