import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { Media } from '@libs/widgets';

@Component({
  selector: 'widget-payment-method-card',
  templateUrl: './payment-method-card.component.html',
  styleUrls: ['./payment-method-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [],
})
export class PaymentMethodCardComponent {
  paymentMethodMedia = input<Media>({
    imageSrc: '',
    imageAlt: '',
    upperText: '',
    text: '',
    description: '',
  });
}
