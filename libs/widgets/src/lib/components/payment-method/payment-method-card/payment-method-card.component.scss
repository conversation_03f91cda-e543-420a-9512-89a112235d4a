:host {
  --eds-payment-method-gap: var(--payment-method-gap, var(--eds-spacing-600));

  --eds-payment-method-image-border: var(
    --payment-method-image-border,
    var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default)
  );
  --eds-payment-method-image-border-radius: var(--payment-method-image-border-radius, var(--eds-radius-200));
  --eds-payment-method-image-background-color: var(
    --payment-method-image-background-color,
    var(--eds-colors-surface-default)
  );
  --eds-payment-method-image-width: var(--payment-method-image-width, 5.25rem);
  --eds-payment-method-image-height: var(--payment-method-image-height, 3.5rem);
  --eds-payment-method-image-padding: var(--payment-method-image-padding, var(--eds-spacing-300));
}

.base {
  display: flex;
  flex-direction: column;
  row-gap: var(--eds-payment-method-gap);
}

.payment::part(image-wrapper) {
  border: var(--eds-payment-method-image-border);
  border-radius: var(--eds-payment-method-image-border-radius);
  background-color: var(--eds-payment-method-image-background-color);
  width: var(--eds-payment-method-image-width);
  height: var(--eds-payment-method-image-height);
  padding: var(--eds-payment-method-image-padding);
}
