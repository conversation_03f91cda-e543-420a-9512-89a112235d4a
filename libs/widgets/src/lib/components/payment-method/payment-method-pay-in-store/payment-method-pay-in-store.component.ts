import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

@Component({
  selector: 'widget-payment-method-pay-in-store',
  templateUrl: './payment-method-pay-in-store.component.html',
  styleUrls: ['./payment-method-pay-in-store.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class PaymentMethodPayInStoreComponent {}
