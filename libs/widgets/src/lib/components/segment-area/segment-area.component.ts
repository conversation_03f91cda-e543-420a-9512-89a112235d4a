import {
  AfterViewInit,
  Component,
  contentChildren,
  CUSTOM_ELEMENTS_SCHEMA,
  effect,
  input,
  output,
} from '@angular/core';
import { SegmentedAreaKnowledgeKeys, SegmentOptions } from '../../model';
import { SegmentDirective } from '../../directives';

@Component({
  selector: 'widget-segment-area',
  templateUrl: './segment-area.component.html',
  styleUrls: ['./segment-area.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class SegmentAreaComponent implements AfterViewInit {
  segmentedOptions = input<SegmentOptions[]>([]);
  changeSegment = output<string>();

  segments = contentChildren(SegmentDirective);

  constructor() {
    effect(() => {
      if (this.segments()) {
        this.updateSlottedEls();
      }
    });
  }

  ngAfterViewInit(): void {
    this.updateSlottedEls();
  }

  private updateSlottedEls(): void {
    const defaultSelectedOption =
      this.segmentedOptions().find((option) => option.defaultSelected) ?? this.segmentedOptions().find(Boolean);

    if (defaultSelectedOption) {
      this.filterElements(defaultSelectedOption.ariaControls);
    }
  }

  private filterElements(value: string): void {
    this.segments().forEach((element) => {
      if (element.id === value || value === SegmentedAreaKnowledgeKeys.ALL) {
        return element.show();
      }
      element.hide();
    });
  }

  handleSelection(event: CustomEvent<{ target: { ariaControls: string } }> | unknown): void {
    const ariaControls = (event as CustomEvent).detail.target.ariaControls as string;
    this.filterElements(ariaControls);
    this.changeSegment.emit(ariaControls);
  }
}
