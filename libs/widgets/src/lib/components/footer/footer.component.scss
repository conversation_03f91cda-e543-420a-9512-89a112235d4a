:host {
  --eds-footer-background: var(--footer-background, var(--eds-colors-primary-dark));
  --eds-footer-text-color: var(--footer-text-color, var(--eds-colors-text-white));
  --eds-footer-container-size: var(--footer-container-size, calc(var(--eds-size-multiplier) * 288));
  --eds-footer-items-border-properties: var(
    --footer-items-border-properties,
    var(--eds-stroke-025) var(--eds-border-style-base) oklch(from var(--eds-border-color-default) l c h / 0.1)
  );
  --eds-footer-content-wrapper-background: var(--footer-content-wrapper-background, var(--eds-colors-primary-default));
  --eds-footer-area-padding: var(--footer-area-padding, var(--eds-spacing-400));
  --eds-footer-content-area-grid-template-columns: var(
    --footer-content-area-grid-template-columns,
    repeat(2, minmax(0, 1fr))
  );
  --eds-footer-content-area-gap: var(--footer-content-area-gap, var(--eds-spacing-200));
  --eds-footer-content-area-item-padding: var(--footer-content-area-item-padding, var(--eds-spacing-400) 0);
  --eds-footer-content-area-item-gap: var(--footer-content-area-item-gap, var(--eds-spacing-200));
  --eds-footer-content-area-item-icon-size: var(--footer-content-area-item-icon-size, var(--eds-sizing-600));
  --eds-footer-content-area-item-icon-color: var(--footer-content-area-item-icon-color, var(--eds-colors-icon-light));
  --eds-footer-links-gap: var(--footer-links-gap, var(--eds-spacing-300));
  --eds-footer-links-item-gap: var(--footer-links-item-gap, var(--eds-spacing-400));
  --eds-footer-links-item-heading-color: var(--footer-links-item-heading-color, var(--eds-colors-text-light));
  --eds-footer-links-item-link-decoration: var(--footer-links-item-link-decoration, none);
  --eds-footer-links-list-gap: var(--footer-links-list-gap, var(--eds-spacing-300));
  --eds-footer-links-list-padding-bottom: var(--footer-links-list-padding-bottom, var(--eds-spacing-300));
}

.base {
  display: flex;
  flex-direction: column;
  background-color: var(--eds-footer-background);
  color: var(--eds-footer-text-color);

  eds-text::part(base) {
    color: var(--eds-footer-text-color);
  }
}

.content-wrapper,
.links-wrapper {
  display: flex;
  align-items: center;
  flex-direction: column;
  width: 100%;
}

.content-wrapper {
  background-color: var(--eds-footer-content-wrapper-background);
}

.content,
.links,
.information-area {
  width: 100%;
  max-width: var(--eds-footer-container-size);
  padding: var(--eds-footer-area-padding);
}

.content {
  display: grid;
  grid-template-columns: var(--eds-footer-content-area-grid-template-columns);
  gap: var(--eds-footer-content-area-gap);
  text-align: center;

  .item {
    display: flex;
    align-items: center;
    flex-direction: column;
    padding: var(--eds-footer-content-area-item-padding);
    gap: var(--eds-footer-content-area-item-gap);

    eds-icon {
      display: block;
      width: var(--eds-footer-content-area-item-icon-size);
      height: var(--eds-footer-content-area-item-icon-size);
      color: var(--eds-footer-content-area-item-icon-color);
    }
  }
}

.links {
  display: flex;
  flex-direction: column;
  gap: var(--eds-footer-links-gap);

  .item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: var(--eds-footer-links-item-gap);

    eds-heading::part(base) {
      color: var(--eds-footer-links-item-heading-color);
    }

    eds-link::part(base),
    eds-text::part(base) {
      color: var(--eds-footer-text-color);
      text-decoration: var(--eds-footer-links-item-link-decoration);
      cursor: pointer;
    }
  }

  details {
    position: relative;
    width: 100%;
    border-bottom: var(--eds-footer-items-border-properties);

    summary::-webkit-details-marker,
    summary::marker {
      display: none;
      content: '';
    }

    summary {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: var(--eds-spacing-200);
      width: 100%;
      cursor: pointer;
    }

    .links-list {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: var(--eds-footer-links-list-gap);
      padding-bottom: var(--eds-footer-links-list-padding-bottom);
    }

    eds-icon {
      transition: all 0.2s ease-in-out;
    }

    &[open] {
      eds-icon {
        transform: rotate(180deg);
      }
    }
  }
}

@media (min-width: 834px) {
  :host {
    --eds-footer-content-area-item-icon-size: var(--footer-content-area-item-icon-size, var(--eds-sizing-800));
    --eds-footer-content-area-grid-template-columns: var(--footer-content-area-grid-template-columns, repeat(4, auto));
  }

  .content {
    justify-content: space-between;
    .item {
      align-items: flex-start;

      eds-icon {
        width: var(--eds-footer-content-area-item-icon-size);
        height: var(--eds-footer-content-area-item-icon-size);
      }
    }
  }
}

@media (min-width: 1440px) {
  :host {
    --eds-footer-area-padding: var(--footer-area-padding, var(--eds-spacing-700) 0);
    --eds-footer-content-area-padding: var(--footer-content-area-padding, var(--eds-spacing-400) 0);
  }

  .content {
    padding: var(--eds-footer-content-area-padding);
  }

  .links,
  .information-area {
    padding: var(--eds-footer-area-padding);
  }

  .links {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    border-bottom: var(--eds-footer-items-border-properties);

    details {
      border-bottom: 0;
      width: unset;

      summary {
        pointer-events: none;
        cursor: default;
      }

      eds-icon {
        display: none;
      }
    }
  }
}
