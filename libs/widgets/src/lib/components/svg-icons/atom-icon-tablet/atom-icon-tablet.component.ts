import { ChangeDetectionStrategy, Component, forwardRef } from '@angular/core';
import { NgClass } from '@angular/common';
import { AtomIconComponent } from '../atom-icon';

@Component({
  selector: 'atom-icon-tablet',
  templateUrl: './atom-icon-tablet.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: AtomIconComponent,
      useExisting: forwardRef(() => AtomIconTabletComponent),
    },
  ],
  imports: [NgClass],
})
export class AtomIconTabletComponent extends AtomIconComponent {}
