import { ChangeDetectionStrategy, Component, forwardRef } from '@angular/core';
import { NgClass } from '@angular/common';
import { AtomIconComponent } from '../atom-icon';

@Component({
  selector: 'atom-icon-simcard',
  templateUrl: './atom-icon-simcard.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: AtomIconComponent,
      useExisting: forwardRef(() => AtomIconSimcardComponent),
    },
  ],
  imports: [NgClass],
})
export class AtomIconSimcardComponent extends AtomIconComponent {}
