import { ChangeDetectionStrategy, Component, forwardRef } from '@angular/core';
import { NgClass } from '@angular/common';
import { AtomIconComponent } from '../atom-icon';

@Component({
  selector: 'atom-icon-arrow-right',
  templateUrl: './atom-icon-arrow-right.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: AtomIconComponent,
      useExisting: forwardRef(() => AtomIconArrowRightComponent),
    },
  ],
  imports: [NgClass],
})
export class AtomIconArrowRightComponent extends AtomIconComponent {}
