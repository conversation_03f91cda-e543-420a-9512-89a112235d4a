import { ChangeDetectionStrategy, Component, forwardRef } from '@angular/core';
import { NgClass } from '@angular/common';
import { AtomIconComponent } from '../atom-icon';

@Component({
  selector: 'atom-icon-credit-card',
  templateUrl: './atom-icon-credit-card.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: AtomIconComponent,
      useExisting: forwardRef(() => AtomIconCreditCardComponent),
    },
  ],
  imports: [NgClass],
})
export class AtomIconCreditCardComponent extends AtomIconComponent {}
