import { ChangeDetectionStrategy, Component, forwardRef } from '@angular/core';
import { NgClass } from '@angular/common';
import { AtomIconComponent } from '../atom-icon';

@Component({
  selector: 'atom-icon-cellular-network',
  templateUrl: './atom-icon-cellular-network.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: AtomIconComponent,
      useExisting: forwardRef(() => AtomIconCellularNetworkComponent),
    },
  ],
  imports: [NgClass],
})
export class AtomIconCellularNetworkComponent extends AtomIconComponent {}
