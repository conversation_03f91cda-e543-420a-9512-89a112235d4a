import { ChangeDetectionStrategy, Component, forwardRef } from '@angular/core';
import { NgClass } from '@angular/common';
import { AtomIconComponent } from '../atom-icon';

@Component({
  selector: 'atom-icon-internet-antenna',
  templateUrl: './atom-icon-internet-antenna.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: AtomIconComponent,
      useExisting: forwardRef(() => AtomIconInternetAntennaComponent),
    },
  ],
  imports: [NgClass],
})
export class AtomIconInternetAntennaComponent extends AtomIconComponent {}
