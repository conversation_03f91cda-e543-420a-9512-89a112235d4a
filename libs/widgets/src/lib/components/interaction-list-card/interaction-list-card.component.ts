import { Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { Interaction, InteractionListComponent } from '@libs/widgets';

@Component({
  selector: 'widget-interaction-list-card',
  templateUrl: './interaction-list-card.component.html',
  styleUrls: ['./interaction-list-card.component.scss'],
  imports: [InteractionListComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class InteractionListCardComponent {
  titleText = input<string>('Title');

  interactionListItems = input<Interaction[]>([
    {
      text: 'Personal Information',
    },
  ]);
}
