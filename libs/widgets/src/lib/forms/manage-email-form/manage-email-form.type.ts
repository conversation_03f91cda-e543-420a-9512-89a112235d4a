import { FormArray, FormControl, FormGroup } from '@angular/forms';

export interface ManageEmailForm {
  email: FormControl<string | null>;
  isPrimary: FormControl<boolean | null>;
  privacySpecs: FormArray<
    FormGroup<{
      isChecked: FormControl<boolean | null>;
      sortId: FormControl<string | null>;
    }>
  >;
}

export type ManageEmailFormValues = {
  email?: string | null;
  isPrimary?: boolean | null;
  privacySpecs?: {
    isChecked?: boolean | null;
    sortId?: string | null;
  }[];
};
