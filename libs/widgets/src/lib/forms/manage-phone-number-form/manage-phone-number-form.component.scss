.form {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-800);

  .input-container {
    display: flex;
    flex-direction: column;
    gap: var(--eds-spacing-400);
  }

  .communication-preferences {
    display: flex;
    flex-direction: column;
    gap: var(--eds-spacing-400);
  }

  .optionalAgreements {
    margin: var(--eds-create-account-form-optional-agreements-margin) 0;
  }

  .agreement-container {
    display: flex;
    flex-direction: column;
    gap: var(--eds-spacing-100);
  }

  .agreement-label {
    display: flex;
    flex-direction: column;

    eds-text::part(base) {
      font-size: calc(var(--eds-size-multiplier) * 2.5);
    }
  }

  eds-checkbox-group {
    --eds-checkbox-gap: var(--eds-spacing-400);
  }
}
