import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, effect, input, output } from '@angular/core';
import { FormArray, FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { FormFieldComponent, TranslatePipe } from '@libs/plugins';
import { ManagePhoneNumberForm } from './manage-phone-number-form.type';
import { CapturedPartyPrivacy, SelectOption } from '@libs/types';
import { PhoneNumberComponent } from '../../components/phonenumber/phone-number.component';
import { LowerCasePipe } from '@angular/common';

@Component({
  selector: 'widget-manage-phone-number-form',
  templateUrl: './manage-phone-number-form.component.html',
  styleUrls: ['./manage-phone-number-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [ReactiveFormsModule, TranslatePipe, FormFieldComponent, PhoneNumberComponent, LowerCasePipe],
  standalone: true,
})
export class ManagePhoneNumberFormComponent {
  phoneNumberForm = input.required<FormGroup<ManagePhoneNumberForm>>();
  privacyList = input.required<CapturedPartyPrivacy.Content[]>();
  isPrimaryLocked = input<boolean>(false);
  isEdit = input<boolean>(false);
  countryOptionsForPhoneNumber = input<SelectOption[]>([]);
  phoneTypeOptions = input<SelectOption[]>([]);
  phoneTypeSelected = output<string>();
  privacyListChange = output<CapturedPartyPrivacy.Content[]>();

  constructor() {
    effect(() => {
      if (this.isEdit() && this.phoneTypeOptions().length > 0) {
        const phoneType = this.phoneNumberForm().get('phoneType')?.value;
        if (phoneType) {
          const phoneTypeToSet = this.phoneTypeOptions().find((o) => o.value === phoneType);
          if (this.phoneNumberForm().get('phoneType')?.value !== phoneTypeToSet?.value) {
            this.phoneNumberForm()
              .get('phoneType')
              ?.setValue(phoneTypeToSet?.value as string, { emitEvent: false });
          }
        }
      }
    });

    effect((onCleanup) => {
      this.initializeForm();

      const sub = this.phoneNumberForm()
        .get('privacySpecs')
        ?.valueChanges.subscribe(
          (values: ({ isChecked?: boolean | null; sortId?: string | null } | undefined)[] | undefined) => {
            const mutablePrivacyList = JSON.parse(JSON.stringify(this.privacyList()));
            values?.forEach((value, index) => {
              if (!value) {
                return;
              }
              const spec = mutablePrivacyList[index];
              if (spec?.items && typeof value.isChecked === 'boolean') {
                const phoneNumberItem = spec.items.find(
                  (item: CapturedPartyPrivacy.Item) => item.notificationChannelType === 'SMS',
                );
                if (phoneNumberItem) {
                  phoneNumberItem.authorizedFlag = value.isChecked;
                }
              }
            });
            this.privacyListChange.emit(mutablePrivacyList);
          },
        );
      onCleanup(() => {
        sub?.unsubscribe();
      });
    });
  }

  selectPhoneType(event: Event) {
    const value = (event.target as HTMLSelectElement).value;
    this.phoneNumberForm().patchValue({ phoneType: value });
    this.phoneTypeSelected.emit(value);
  }

  get privacySpecsArray(): FormArray {
    return this.phoneNumberForm().get('privacySpecs') as FormArray;
  }

  initializeForm(): void {
    const formArray = this.phoneNumberForm().get('privacySpecs') as FormArray;
    formArray.clear();
    this.privacyList().forEach((spec) => {
      const phoneNumberItem = spec.items?.find((item) => item.notificationChannelType === 'SMS');
      formArray.push(
        new FormGroup({
          isChecked: new FormControl(phoneNumberItem?.authorizedFlag ?? false, { nonNullable: true }),
          sortId: new FormControl(spec.sortId),
        }),
      );
    });
  }

  onCheckboxChange(event: Event, index: number): void {
    const isChecked = (event.target as HTMLInputElement).checked;
    const control = (this.phoneNumberForm().get('privacySpecs') as FormArray).at(index);
    control.get('isChecked')?.setValue(isChecked);
  }
}
