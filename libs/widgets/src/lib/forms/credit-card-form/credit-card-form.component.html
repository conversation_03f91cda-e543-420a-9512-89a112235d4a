<form class="base" [formGroup]="form">
  <widget-form-field
    [label]="'nameOnCard' | translate"
    [placeholder]="'nameOnCardPlaceholder' | translate"
    formControlName="cardHolder"
    [mask]="alphabeticMask"
  >
  </widget-form-field>

  <widget-form-field
    [label]="'cardNumber' | translate"
    [placeholder]="'cardNumberPlaceholder' | translate"
    formControlName="creditCardNumber"
    [mask]="creditCardMask"
    [errorBounce]="500"
  >
  </widget-form-field>

  <widget-form-field
    [label]="'expiryDate' | translate"
    formControlName="exprDate"
    [placeholder]="'expiryDatePlaceholder' | translate"
    [mask]="exprDateMask"
  >
  </widget-form-field>

  <widget-form-field
    [label]="'cvc' | translate"
    formControlName="cvc"
    [placeholder]="'cvcPlaceholder' | translate"
    [mask]="cvcMask"
  >
  </widget-form-field>
  @if (currentNetwork) {
    <div class="network">
      @if (currentNetwork === 'visa') {
        <eds-image class="network" [src]="'assets/images/payment/visa.png'" [alt]="'Visa Logo'" width="40"></eds-image>
      }
      @if (currentNetwork === 'mastercard') {
        <eds-image
          class="network"
          [src]="'assets/images/payment/mastercard.png'"
          [alt]="'Mastercard Logo'"
          width="40"
        ></eds-image>
      }
      @if (currentNetwork === 'amex') {
        <eds-image
          class="network"
          [src]="'assets/images/payment/american-express.png'"
          [alt]="'American Express Logo'"
          width="40"
        ></eds-image>
      }
    </div>
  }

  <div class="actions">
    <eds-button appearance="primary" [disabled]="!form.valid" shouldFitContainer (button-click)="submit()">{{
      'add' | translate
    }}</eds-button>
  </div>
</form>
