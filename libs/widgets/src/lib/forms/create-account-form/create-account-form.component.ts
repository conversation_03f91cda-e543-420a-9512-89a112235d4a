import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  effect,
  input,
  OnInit,
  output,
} from '@angular/core';
import { FormArray, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import {
  createAlphabeticMask,
  createBirthDateMask,
  Form<PERSON>ieldComponent,
  FormFieldErrorMessageComponent,
  REGEX_EMAIL,
  REGEX_NO_EMOJI,
  REGEX_PASSWORD_LOWER,
  REGEX_PASSWORD_NUMERIC,
  REGEX_PASSWORD_SPECIAL,
  REGEX_PASSWORD_UPPER,
  REGEX_UNICODE_NAME,
  TranslatePipe,
} from '@libs/plugins';
import { Checkbox, Phonenumber, SelectOption } from '../../model';
import { AgeValidator, MinDateValidator, regexValidator, DateFormatValidator } from '../../validators';
import { PhoneNumberTempComponent } from '../../components/phonenumberTemp';
import { REGISTER_DATE_FORMAT } from '@libs/core';

@Component({
  selector: 'widget-create-account-form',
  templateUrl: './create-account-form.component.html',
  styleUrls: ['./create-account-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [
    ReactiveFormsModule,
    FormFieldComponent,
    TranslatePipe,
    PhoneNumberTempComponent,
    FormFieldErrorMessageComponent,
  ],
})
export class CreateAccountFormComponent implements OnInit {
  languageTypes = input<SelectOption[]>();

  countryOptions = input<SelectOption[]>([]);

  partyPrivacySpecification = input<Checkbox[]>([]);

  customerMinAge = input<string>();

  onCreateAccountClick = output<object>();
  onTermsOfUseClick = output<number>();
  onGoToLoginClick = output<void>();
  registerDateFormat = REGISTER_DATE_FORMAT;

  form = new FormGroup({
    firstName: new FormControl('', [
      Validators.required,
      Validators.minLength(1),
      Validators.maxLength(100),
      regexValidator(REGEX_UNICODE_NAME, 'required'),
      regexValidator(REGEX_NO_EMOJI, 'noEmoji'),
    ]),
    lastName: new FormControl('', [
      Validators.required,
      Validators.minLength(1),
      Validators.maxLength(100),
      regexValidator(REGEX_UNICODE_NAME, 'required'),
      regexValidator(REGEX_NO_EMOJI, 'noEmoji'),
    ]),
    email: new FormControl('', [
      Validators.required,
      regexValidator(REGEX_EMAIL, 'email'),
      Validators.maxLength(64),
      regexValidator(REGEX_NO_EMOJI, 'noEmoji'),
    ]),
    birthDate: new FormControl('', [
      Validators.required,
      new AgeValidator().validate.bind({ minAge: this.customerMinAge() }),
      new MinDateValidator().validate(),
      new DateFormatValidator().validate(this.registerDateFormat),
      regexValidator(REGEX_NO_EMOJI, 'noEmoji'),
    ]),
    password: new FormControl('', [
      Validators.required,
      Validators.minLength(8),
      Validators.maxLength(32),
      regexValidator(REGEX_PASSWORD_UPPER, 'invalidPasswordUppercase'),
      regexValidator(REGEX_PASSWORD_LOWER, 'invalidPasswordLowercase'),
      regexValidator(REGEX_PASSWORD_NUMERIC, 'invalidPasswordNumeric'),
      regexValidator(REGEX_PASSWORD_SPECIAL, 'invalidPasswordSpecial'),
      regexValidator(REGEX_NO_EMOJI, 'noEmoji'),
    ]),
    langShortCode: new FormControl('', [Validators.required]),
    country: new FormControl('', [Validators.required]),
    phoneNumber: new FormControl('', [Validators.required, Validators.minLength(10)]),
    privacySpecs: new FormArray([]),
  });

  alphabeticMask = createAlphabeticMask();
  dateMask = createBirthDateMask(Number(this.customerMinAge()));
  maxDate: Date;
  minDate: Date;

  constructor() {
    effect(() => {
      if (this.customerMinAge()) {
        this.form
          .get('birthDate')
          .setValidators([
            Validators.required,
            new AgeValidator().validate.bind({ minAge: this.customerMinAge() }),
            new MinDateValidator().validate(),
            new DateFormatValidator().validate(this.registerDateFormat),
          ]);
        this.form.get('birthDate').updateValueAndValidity();
      }
    });
  }

  getCheckboxValue(index: number): boolean {
    const control = (this.form.get('privacySpecs') as FormArray).at(index);
    return control.get('isChecked').value;
  }

  onCheckboxChange(event: Event, index: number): void {
    const isChecked = (event.target as HTMLInputElement).checked;
    const control = (this.form.get('privacySpecs') as FormArray).at(index);
    control.get('isChecked').setValue(isChecked);

    if (index < this.partyPrivacySpecification().length) {
      this.partyPrivacySpecification()[index].isChecked = isChecked;
      this.partyPrivacySpecification()[index].authorized = isChecked;
    }
    this.validatePrivacyRequirements();
  }

  ngOnInit() {
    this.maxDate = new Date(
      new Date().getFullYear() - Number(this.customerMinAge()),
      new Date().getMonth(),
      new Date().getDate(),
    );
    this.minDate = new Date('1900-01-01');
    this.updatePrivacySpecsFormArray();
  }

  public isFormGroupTouched(): boolean {
    return this.form.touched;
  }

  updateAllFormFields(formGroup: FormGroup | FormArray) {
    Object.keys(formGroup.controls).forEach((key) => {
      const control = formGroup.get(key);

      if (control instanceof FormControl) {
        control.updateValueAndValidity();
      } else if (control instanceof FormGroup || control instanceof FormArray) {
        this.updateAllFormFields(control);
      }
    });
  }

  register() {
    this.form.markAllAsTouched();
    this.form.updateValueAndValidity();
    this.updateAllFormFields(this.form);

    this.validatePrivacyRequirements();
    if (this.form.valid) {
      const partyPrivacySpecList = this.partyPrivacySpecification()
        .filter((spec) => spec.isChecked)
        .map((spec) => ({
          ...spec,
          authorized: true,
        }));

      const form = {
        ...this.form.value,
        partyPrivacySpecList: [...partyPrivacySpecList],
      };
      this.onCreateAccountClick.emit(form);
    }
  }

  termsOfUseClick(id: string) {
    this.onTermsOfUseClick.emit(+id);
  }

  goToLoginClick() {
    this.onGoToLoginClick.emit();
  }

  updatePrivacySpecsFormArray(): void {
    const privacySpecsArray = this.form.get('privacySpecs') as FormArray;
    privacySpecsArray.clear();

    this.partyPrivacySpecification().forEach((spec) => {
      privacySpecsArray.push(
        new FormGroup({
          id: new FormControl(spec.id),
          isChecked: new FormControl(spec.isChecked || false, spec.isRequired ? Validators.requiredTrue : null),
          value: new FormControl(spec.value),
          isRequired: new FormControl(spec.isRequired),
          authorized: new FormControl(spec.isChecked || false),
          isClickable: new FormControl(spec.isClickable || false),
        }),
      );
    });
  }

  validatePrivacyRequirements(): boolean {
    const privacySpecsArray = this.form.get('privacySpecs') as FormArray;
    let isValid = true;

    privacySpecsArray.controls.forEach((control) => {
      if (control.get('isRequired').value && !control.get('isChecked').value) {
        control.get('isChecked').setErrors({ required: true });
        isValid = false;
      }
    });

    if (!isValid) {
      this.form.setErrors({ ...this.form.errors, invalidPrivacyRequirements: true });
    } else if (this.form.errors) {
      const errors = { ...this.form.errors };
      delete errors.invalidPrivacyRequirements;
      this.form.setErrors(Object.keys(errors).length ? errors : null);
    }

    return isValid;
  }

  togglePrivacySpecification(event: Event) {
    const shortCode = (event.target as HTMLInputElement).value;
    const index = this.partyPrivacySpecification().findIndex((item) => item.value === shortCode);
    if (index !== -1) {
      const newCheckedState = !this.partyPrivacySpecification()[index].isChecked;
      this.partyPrivacySpecification()[index].isChecked = newCheckedState;
      this.partyPrivacySpecification()[index].authorized = newCheckedState;
      const control = (this.form.get('privacySpecs') as FormArray).at(index);
      control.get('isChecked').setValue(newCheckedState);
    }
  }

  setPhoneNumber(value: Phonenumber) {
    this.form.patchValue({ country: value.country });
    this.form.patchValue({ phoneNumber: value.phoneNumber });
  }
}
