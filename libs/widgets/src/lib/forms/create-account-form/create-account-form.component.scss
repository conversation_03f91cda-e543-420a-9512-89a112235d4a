:host {
  --eds-create-account-form-row-gap: var(--create-account-form-row-gap, var(--eds-spacing-400));
  --eds-create-account-form-font-size: var(--create-account-form-font-size, var(--eds-font-size-body-md));
  --eds-create-account-form-agreement-font-size: var(
    --create-account-form-agreement-font-size,
    var(--eds-font-size-body-sm)
  );
  --eds-create-account-form-agreement-border-color: var(
    --create-account-form-agreement-border-color,
    var(--eds-border-color-light)
  );
  --eds-create-account-form-agreement-border-width: var(
    --create-account-form-agreement-border-width,
    var(--eds-sizing-050)
  );
  --eds-create-account-form-agreement-padding-bottom: var(
    --create-account-form-agreement-padding-bottom,
    var(--eds-spacing-400)
  );
  --eds-create-account-form-agreement-border-style: var(
    --create-account-form-agreement-border-style,
    var(--eds-border-style-base)
  );
  --eds-create-account-form-optional-agreements-margin: var(
    --create-account-form-optional-agreements-margin,
    var(--eds-spacing-300)
  );
  --eds-create-account-form-login-color: var(--create-account-form-login-color, var(--eds-colors-text-light));
  --eds-create-account-form-login-and-aggrement-link-display: var(
    --create-account-form-login-and-aggrement-link-display,
    inline-flex
  );
}

.base {
  display: flex;
  flex-direction: column;
  row-gap: var(--eds-create-account-form-row-gap);
}

.optionalAgreements {
  margin: var(--eds-create-account-form-optional-agreements-margin) 0;
}

.agreement-container {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-100);
}

.agreement-label {
  display: flex;
  flex-direction: column;

  eds-text::part(base) {
    font-size: calc(var(--eds-size-multiplier) * 2.5);
  }
}

.agreement-link {
  color: var(--eds-colors-link);
  text-decoration: underline;
  cursor: pointer;
}

.defaultAgreements {
  border-bottom-width: var(--eds-create-account-form-agreement-border-width);
  border-bottom-color: var(--eds-create-account-form-agreement-border-color);
  border-bottom-style: var(--eds-create-account-form-agreement-border-style);
  padding-bottom: var(--eds-create-account-form-agreement-padding-bottom);
  font-size: var(--eds-create-account-form-agreement-font-size);
}

.defaultAgreementsLink {
  font-size: var(--eds-create-account-form-agreement-font-size);
}

.login::part(base) {
  color: var(--eds-create-account-form-login-color);
}

eds-form-field {
  position: relative;
}

.haveAnAccount {
  display: flex;
  align-items: baseline;
}
