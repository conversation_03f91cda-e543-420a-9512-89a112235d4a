import {
  ChangeDetectionStrategy,
  Component,
  computed,
  CUSTOM_ELEMENTS_SCHEMA,
  effect,
  input,
  output,
} from '@angular/core';
import { FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { createAlphabeticMask, createBirthDateMask, FormFieldComponent, TranslatePipe } from '@libs/plugins';
import { AgeValidator, MinDateValidator, DateFormatValidator } from '../../validators';
import { DEFAULT_DATE_FORMAT } from '@libs/core';
import { SelectOption } from '@libs/types';
import { UpdateCustomerInfoFormFields, UpdateCustomerInfoFormGroup } from './update-customer-info-form.type';

@Component({
  selector: 'widget-update-customer-info-form',
  templateUrl: './update-customer-info-form.component.html',
  styleUrls: ['./update-customer-info-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [ReactiveFormsModule, TranslatePipe, FormFieldComponent],
  standalone: true,
})
export class UpdateCustomerInfoFormComponent {
  editCustomerForm = input.required<FormGroup<UpdateCustomerInfoFormGroup>>();
  onSubmit = output<UpdateCustomerInfoFormFields>();
  onCancel = output<void>();
  defaultDateFormat = DEFAULT_DATE_FORMAT;
  customerMinAge = input.required<string>();
  languageTypes = input<SelectOption[]>([]);
  occupationTypes = input<SelectOption[]>([]);
  minDate = new Date('1900-01-01');
  maxDate = computed(() => {
    const minAge = Number(this.customerMinAge());
    if (isNaN(minAge)) {
      return new Date();
    }
    return new Date(new Date().getFullYear() - minAge, new Date().getMonth(), new Date().getDate());
  });

  alphabeticMask = createAlphabeticMask();
  dateMask = computed(() => createBirthDateMask(Number(this.customerMinAge())));

  constructor() {
    effect(() => {
      const minAge = this.customerMinAge();
      if (minAge) {
        this.editCustomerForm()
          .get('birthDate')
          .setValidators([
            Validators.required,
            new AgeValidator().validate.bind({ minAge: minAge }),
            new MinDateValidator().validate(),
            new DateFormatValidator().validate(this.defaultDateFormat),
          ]);
        this.editCustomerForm().get('birthDate').updateValueAndValidity();
      }
    });
  }

  submit() {
    this.editCustomerForm().markAllAsTouched();

    if (this.editCustomerForm().valid) {
      this.onSubmit.emit(this.editCustomerForm().getRawValue());
    }
  }
}
