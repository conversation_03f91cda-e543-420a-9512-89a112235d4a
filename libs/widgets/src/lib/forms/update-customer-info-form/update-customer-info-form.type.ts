import { FormControl } from '@angular/forms';

export type UpdateCustomerInfoFormFields = Partial<{
  customerId: string;
  userName: string;
  firstName: string;
  lastName: string;
  birthDate: string;
  occupation?: string;
  langShortCode: string;
  titleShortCode?: string;
}>;

export type UpdateCustomerInfoFormGroup = {
  customerId: FormControl<string>;
  userName: FormControl<string>;
  firstName: FormControl<string>;
  lastName: FormControl<string>;
  birthDate: FormControl<string>;
  occupation: FormControl<string>;
  langShortCode: FormControl<string>;
};
