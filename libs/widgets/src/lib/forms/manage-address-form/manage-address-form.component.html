<form [formGroup]="addressForm()" class="form">
  <widget-form-field
    [label]="'addressLabel' | translate"
    [placeholder]="'addressLabelPlaceholder' | translate"
    formControlName="addressLabel"
    maxLength="450"
  ></widget-form-field>

  <widget-form-field
    [label]="'countryRegion' | translate"
    formControlName="country"
    [placeholder]="'select' | translate"
    type="select"
    [options]="{ options: countryOptions() }"
    (option-selected)="selectCountry($any($event.target).value)"
  ></widget-form-field>

  @for (key of [countryChangeKey()]; track key) {
    <div class="province-city-area">
      <widget-form-field
        [label]="'province' | translate"
        formControlName="state"
        [placeholder]="'select' | translate"
        type="select"
        [options]="{ options: stateOptions() }"
        (option-selected)="selectProvince($any($event.target).value)"
      ></widget-form-field>

      <widget-form-field
        [label]="'city' | translate"
        formControlName="city"
        [placeholder]="'select' | translate"
        type="select"
        [options]="{ options: cityOptions() }"
        (option-selected)="selectCity($any($event.target).value)"
      ></widget-form-field>
    </div>
  }

  <widget-form-field
    [label]="'address' | translate"
    [placeholder]="'addressPlaceholder' | translate"
    formControlName="addressDescription"
    maxLength="200"
  ></widget-form-field>

  <widget-form-field
    [label]="'postalCode' | translate"
    [placeholder]="'postalCodePlaceholder' | translate"
    formControlName="postalCode"
    maxLength="20"
  ></widget-form-field>

  <eds-checkbox
    id="isPrimary"
    name="isPrimary"
    [attr.checked]="isPrimaryLocked() ? true : addressForm().get('isPrimary')?.value ? 'checked' : null"
    (change)="addressForm().get('isPrimary')?.setValue(!addressForm().get('isPrimary')?.value)"
    [isDisabled]="isPrimaryLocked()"
  >
    <label for="isPrimary">{{
      'setAsPrimary' | translate: { communicationType: 'address' | translate | lowercase }
    }}</label>
  </eds-checkbox>
</form>
