<form class="base" [formGroup]="form">
  <div class="existing-email-group">
    <eds-text class="label" size="lg" weight="regular" [text]="'existingEmail' | translate"></eds-text>
    <eds-text class="value" size="md" weight="medium" [text]="existingEmail()"></eds-text>
  </div>

  <div class="new-email-group">
    <widget-form-field
      [label]="'newEmail' | translate"
      [placeholder]="'enterYourEmail' | translate"
      formControlName="newEmail"
      type="email"
    ></widget-form-field>

    <eds-button appearance="primary" size="default" shouldFitContainer="true" (button-click)="submit()">
      {{ 'updateEmail' | translate }}
    </eds-button>
  </div>
</form>
