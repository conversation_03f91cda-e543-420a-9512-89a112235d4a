import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, effect, input, output } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { FormFieldComponent, REGEX_EMAIL, REGEX_NO_EMOJI, TranslatePipe } from '@libs/plugins';
import { regexValidator } from '../../validators';
import { UpdateMyEmailFormFields } from './update-my-email-form.type';

@Component({
  selector: 'widget-update-my-email-form',
  templateUrl: './update-my-email-form.component.html',
  styleUrls: ['./update-my-email-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [ReactiveFormsModule, FormFieldComponent, TranslatePipe],
})
export class UpdateMyEmailFormComponent {
  existingEmail = input<string>('');
  onSubmit = output<UpdateMyEmailFormFields>();

  form = new FormGroup({
    existingEmail: new FormControl({ value: '', disabled: true }),
    newEmail: new FormControl('', [
      Validators.required,
      regexValidator(REGEX_EMAIL, 'email'),
      Validators.maxLength(64),
      regexValidator(REGEX_NO_EMOJI, 'noEmoji'),
    ]),
  });

  constructor() {
    effect(() => {
      this.form.get('existingEmail')?.patchValue(this.existingEmail(), { emitEvent: false });
    });
  }

  submit() {
    this.form.markAllAsTouched();
    this.form.updateValueAndValidity();

    if (this.form.valid) {
      this.onSubmit.emit(this.form.getRawValue());
    }
  }
}
