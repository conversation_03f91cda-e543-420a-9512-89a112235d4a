import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  OnInit,
  output,
  ChangeDetectorRef,
} from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import {
  createAlphabeticMask,
  createIbanMask,
  FormFieldComponent,
  IBAN_CONFIGURATIONS,
  TranslatePipe,
} from '@libs/plugins';
import { BankAccountFormFields } from './bank-account-form.type';
import { regexValidator } from '../../validators';

@Component({
  selector: 'widget-bank-account-form',
  templateUrl: './bank-account-form.component.html',
  styleUrls: ['./bank-account-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [ReactiveFormsModule, TranslatePipe, FormFieldComponent],
})
export class BankAccountFormComponent implements OnInit {
  addBankAccount = output<BankAccountFormFields>();

  form = new FormGroup({
    nameOnBankAccount: new FormControl('', [Validators.required]),
    bankName: new FormControl('', [Validators.required]),
    iban: new FormControl('', [Validators.required, regexValidator(IBAN_CONFIGURATIONS.TR.regex, 'invalidFormat')]),
  });

  ibanMask: ReturnType<typeof createIbanMask>;
  alphabeticMask = createAlphabeticMask();

  private static readonly DEFAULT_IBAN_COUNTRY_CODE = 'TR';
  private static readonly IBAN_COUNTRY_CODE_LENGTH = 2;

  constructor(private cdr: ChangeDetectorRef) {
    this.ibanMask = createIbanMask(BankAccountFormComponent.DEFAULT_IBAN_COUNTRY_CODE);
  }

  submit(): void {
    this.form.markAllAsTouched();

    if (this.form.valid) {
      this.addBankAccount.emit(this.form.value as BankAccountFormFields);
    }
  }

  ngOnInit(): void {
    this.form.get('iban')?.valueChanges.subscribe((value: string | null) => {
      const rawValue = value || '';
      let effectiveCountryCode: keyof typeof IBAN_CONFIGURATIONS =
        BankAccountFormComponent.DEFAULT_IBAN_COUNTRY_CODE as keyof typeof IBAN_CONFIGURATIONS;

      if (rawValue.length >= BankAccountFormComponent.IBAN_COUNTRY_CODE_LENGTH) {
        const potentialCode = rawValue.slice(0, BankAccountFormComponent.IBAN_COUNTRY_CODE_LENGTH).toUpperCase();
        if (Object.prototype.hasOwnProperty.call(IBAN_CONFIGURATIONS, potentialCode)) {
          effectiveCountryCode = potentialCode as keyof typeof IBAN_CONFIGURATIONS;
        }
      }
      this.applyIbanSettings(effectiveCountryCode);
    });
  }

  private applyIbanSettings(countryCode: keyof typeof IBAN_CONFIGURATIONS): void {
    const ibanConfig = IBAN_CONFIGURATIONS[countryCode] || IBAN_CONFIGURATIONS.TR;

    this.ibanMask = createIbanMask(countryCode);

    const ibanControl = this.form.get('iban');
    if (ibanControl) {
      ibanControl.setValidators([Validators.required, regexValidator(ibanConfig.regex, 'invalidFormat')]);
      ibanControl.updateValueAndValidity({ emitEvent: false });
    }
    this.cdr.markForCheck();
  }
}
