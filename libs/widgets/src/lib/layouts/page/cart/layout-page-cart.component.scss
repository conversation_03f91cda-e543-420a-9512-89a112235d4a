:host {
  --eds-page-layout-cart-display: var(--page-layout-cart-display, flex);
  --eds-page-layout-cart-align-items: var(--page-layout-cart-align-items, center);
  --eds-page-layout-cart-flex-direction: var(--page-layout-cart-flex-direction, column);
  --eds-page-layout-cart-padding-bottom: var(--page-layout-cart-padding-bottom, calc(var(--eds-size-multiplier) * 24));
  --eds-page-layout-cart-min-height: var(--page-layout-cart-min-height, 100svh);
  --eds-page-layout-cart-background-color: var(--page-layout-cart-background-color, var(--eds-colors-surface-level-1));
  --eds-page-layout-cart-gap: var(--page-layout-cart-gap, var(--eds-spacing-800));

  --eds-page-layout-cart-header-position: var(--page-layout-cart-header-position, sticky);
  --eds-page-layout-cart-header-z-index: var(--page-layout-cart-header-z-index, 1000);
  --eds-page-layout-cart-header-width: var(--page-layout-cart-header-width, 100%);
  --eds-page-layout-cart-header-top: var(--page-layout-cart-header-top, 0);

  --eds-page-layout-cart-wrapper-display: var(--page-layout-cart-wrapper-display, grid);
  --eds-page-layout-cart-wrapper-grid-template: var(
    --page-layout-cart-wrapper-grid-template,
    none / minmax(0, calc(var(--eds-size-multiplier) * 186))
  );
  --eds-page-layout-cart-wrapper-padding: var(--page-layout-cart-wrapper-padding, 0 var(--eds-spacing-400));
  --eds-page-layout-cart-wrapper-gap: var(--page-layout-cart-wrapper-gap, var(--eds-sizing-600));

  --eds-page-layout-cart-aside-position: var(--page-layout-cart-aside-position, fixed);
  --eds-page-layout-cart-aside-background-color: var(
    --page-layout-cart-aside-background-color,
    var(--eds-colors-surface-level-1)
  );
  --eds-page-layout-cart-aside-padding: var(
    --page-layout-cart-aside-padding,
    var(--eds-spacing-200) var(--eds-spacing-400)
  );
  --eds-page-layout-cart-aside-border-top: var(
    --page-layout-cart-aside-border-top,
    var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default)
  );
}

@media (min-width: 1440px) {
  :host {
    --eds-page-layout-cart-wrapper-grid-template: var(
      --page-layout-cart-wrapper-grid-template,
      none / calc(var(--eds-size-multiplier) * 186) calc(var(--eds-size-multiplier) * 90)
    );

    --eds-page-layout-cart-aside-background-color: var(--page-layout-cart-aside-background-color, hsla(0, 0%, 100%, 0));
    --eds-page-layout-cart-aside-position: var(--page-layout-cart-aside-position, unset);
    --eds-page-layout-cart-aside-padding: var(--page-layout-cart-aside-padding, 0);
    --eds-page-layout-cart-aside-border-top: var(--page-layout-cart-aside-border-top, 0);
  }
}

.base {
  position: relative;
  display: var(--eds-page-layout-cart-display);
  align-items: var(--eds-page-layout-cart-align-items);
  flex-direction: var(--eds-page-layout-cart-flex-direction);
  background-color: var(--eds-page-layout-cart-background-color);
  min-height: var(--eds-page-layout-cart-min-height);
  padding-bottom: var(--eds-page-layout-cart-padding-bottom);
  gap: var(--eds-page-layout-cart-gap);
}

.header {
  display: block;
  position: var(--eds-page-layout-cart-header-position);
  top: var(--eds-page-layout-cart-header-top);
  z-index: var(--eds-page-layout-cart-header-z-index);
  width: var(--eds-page-layout-cart-header-width);
}
