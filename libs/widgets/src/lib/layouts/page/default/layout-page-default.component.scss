:host {
  --eds-page-layout-default-display: var(--page-layout-default-display, flex);
  --eds-page-layout-default-align-items: var(--page-layout-default-align-items, center);
  --eds-page-layout-default-flex-direction: var(--page-layout-default-flex-direction, column);
  --eds-page-layout-default-padding-bottom: var(
    --page-layout-default-padding-bottom,
    calc(var(--eds-size-multiplier) * 24)
  );
  --eds-page-layout-default-min-height: var(--page-layout-default-min-height, 100svh);
  --eds-page-layout-default-background-color: var(
    --page-layout-default-background-color,
    var(--eds-colors-surface-level-1)
  );
  --eds-page-layout-default-gap: var(--page-layout-default-gap, var(--eds-spacing-800));

  --eds-page-layout-default-header-position: var(--page-layout-default-header-position, sticky);
  --eds-page-layout-default-header-z-index: var(--page-layout-default-header-z-index, 1000);
  --eds-page-layout-default-header-width: var(--page-layout-default-header-width, 100%);
  --eds-page-layout-default-header-top: var(--page-layout-default-header-top, 0);

  --eds-page-layout-default-navigation-position: var(--page-layout-default-navigation-position, sticky);
  --eds-page-layout-default-navigation-top: var(
    --page-layout-default-navigation-top,
    calc(var(--eds-size-multiplier) * 26)
  );

  --eds-page-layout-default-wrapper-display: var(--page-layout-default-wrapper-display, grid);
  --eds-page-layout-default-wrapper-grid-template: var(
    --page-layout-default-wrapper-grid-template,
    none / minmax(0, calc(var(--eds-size-multiplier) * 138))
  );
  --eds-page-layout-default-wrapper-padding: var(--page-layout-default-wrapper-padding, 0 var(--eds-spacing-400));
  --eds-page-layout-default-wrapper-gap: var(--page-layout-default-wrapper-gap, calc(var(--eds-size-multiplier) * 30));

  --eds-page-layout-default-aside-position: var(--page-layout-default-aside-position, fixed);
  --eds-page-layout-default-aside-background-color: var(
    --page-layout-default-aside-background-color,
    var(--eds-colors-surface-level-1)
  );
  --eds-page-layout-default-aside-padding: var(
    --page-layout-default-aside-padding,
    var(--eds-spacing-200) var(--eds-spacing-400)
  );
  --eds-page-layout-default-aside-border-top: var(
    --page-layout-default-aside-border-top,
    var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default)
  );
}

@media (min-width: 1440px) {
  :host {
    --eds-page-layout-default-wrapper-grid-template: var(
      --page-layout-default-wrapper-grid-template,
      none / 1fr calc(var(--eds-size-multiplier) * 138) 1fr
    );

    --eds-page-layout-default-aside-background-color: var(
      --page-layout-default-aside-background-color,
      hsla(0, 0%, 100%, 0)
    );
    --eds-page-layout-default-aside-position: var(--page-layout-default-aside-position, unset);
    --eds-page-layout-default-aside-padding: var(--page-layout-default-aside-padding, 0);
    --eds-page-layout-default-aside-border-top: var(--page-layout-default-aside-border-top, 0);
  }
}

.base {
  position: relative;
  display: var(--eds-page-layout-default-display);
  align-items: var(--eds-page-layout-default-align-items);
  flex-direction: var(--eds-page-layout-default-flex-direction);
  background-color: var(--eds-page-layout-default-background-color);
  min-height: var(--eds-page-layout-default-min-height);
  padding-bottom: var(--eds-page-layout-default-padding-bottom);
  gap: var(--eds-page-layout-default-gap);
}

.header {
  display: block;
  position: var(--eds-page-layout-default-header-position);
  top: var(--eds-page-layout-default-header-top);
  z-index: var(--eds-page-layout-default-header-z-index);
  width: var(--eds-page-layout-default-header-width);
}

.navigation {
  display: block;
  position: var(--eds-page-layout-default-navigation-position);
  top: var(--eds-page-layout-default-navigation-top);
}

.page-layout-wrapper {
  display: var(--eds-page-layout-default-wrapper-display);
  grid-template: var(--eds-page-layout-default-wrapper-grid-template);
  gap: var(--eds-page-layout-default-wrapper-gap);
  padding: var(--eds-page-layout-default-wrapper-padding);
}

aside {
  position: var(--eds-page-layout-default-aside-position);
  inset: auto 0 0 0;
  z-index: 900;
  background-color: var(--eds-page-layout-default-aside-background-color);
  padding: var(--eds-page-layout-default-aside-padding);
  border-top: var(--eds-page-layout-default-aside-border-top);
}
