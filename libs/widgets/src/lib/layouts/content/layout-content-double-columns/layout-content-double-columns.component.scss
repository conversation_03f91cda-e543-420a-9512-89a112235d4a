:host {
  --eds-layout-content-double-columns-display: var(--layout-content-double-columns-display, grid);
  --eds-layout-content-double-columns-grid-template: var(
    --layout-content-double-columns-grid-template,
    'first ' minmax(0, auto) 'second' minmax(0, auto) 'third' minmax(0, auto) 'fourth' minmax(0, auto) / minmax(0, 100%)
  );
  --eds-layout-content-double-columns-gap: var(--layout-content-double-columns-gap, var(--eds-spacing-600));
}

@media (min-width: 1440px) {
  :host {
    --eds-layout-content-double-columns-grid-template: var(
      --layout-content-double-columns-grid-template,
      'first second' minmax(0, auto) 'third second' minmax(0, auto) 'fourth fourth' minmax(0, auto) / minmax(0, 50%)
        minmax(0, 50%)
    );
  }
}

.base {
  display: var(--eds-layout-content-double-columns-display);
  grid-template: var(--eds-layout-content-double-columns-grid-template);
  gap: var(--eds-layout-content-double-columns-gap);
  overflow: hidden;
}

.ga-first {
  grid-area: first;
}

.ga-second {
  grid-area: second;
}

.ga-third {
  grid-area: third;
}

.ga-fourth {
  grid-area: fourth;
}

.ga-extra {
  grid-column: 1 / -1;
}
