.base {
  display: grid;
  grid-template-columns: minmax(0, calc(var(--eds-size-multiplier) * 186));
  justify-content: center;
  gap: var(--eds-spacing-600);
  padding: var(--eds-spacing-400);
}

:host ::ng-deep main {
  display: flex;
  flex-direction: column;
  gap: var(--eds-spacing-400);
}

:host ::ng-deep aside {
  position: relative;

  .sticky {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    flex-direction: column;
    gap: var(--eds-spacing-600);

    @media (min-width: 1440px) {
      position: sticky;
      top: 112px;
    }
  }
}

@media (min-width: 1440px) {
  .base {
    grid-template-areas: 'main aside';
    grid-template-columns: calc(var(--eds-size-multiplier) * 186) calc(var(--eds-size-multiplier) * 90);
  }

  :host ::ng-deep main {
    grid-area: main;
    display: grid;
    gap: var(--eds-spacing-400);
  }

  :host ::ng-deep aside {
    grid-area: aside;
    gap: var(--eds-spacing-600);
  }
}
