:host {
  --eds-content-layout-default-display: var(--content-layout-default-display, flex);
  --eds-content-layout-default-flex-direction: var(--content-layout-default-flex-direction, column);
  --eds-content-layout-default-gap: var(--content-layout-default-gap, var(--eds-spacing-600));
  --eds-content-layout-default-overflow: var(--content-layout-default-overflow, hidden);
  --eds-content-layout-default-justify-content: var(--content-layout-default-justify-content, unset);
  --eds-content-layout-default-align-items: var(--content-layout-default-align-items, unset);
  --eds-content-layout-default-justify-items: var(--content-layout-default-justify-items, unset);
}
.base {
  display: var(--eds-content-layout-default-display);
  flex-direction: var(--eds-content-layout-default-flex-direction);
  gap: var(--eds-content-layout-default-gap);
  overflow: var(--eds-content-layout-default-overflow);
  justify-content: var(--eds-content-layout-default-justify-content);
  align-items: var(--eds-content-layout-default-align-items);
  justify-items: var(--eds-content-layout-default-justify-items);
}
