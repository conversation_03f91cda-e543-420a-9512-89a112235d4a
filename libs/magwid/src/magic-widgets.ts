import { Type } from '@angular/core';
import {
  CmsBannerMagicComponent,
  CmsCtaSupportMagicComponent,
  CmsFaqMagicComponent,
  CmsFeaturesStripMagicComponent,
  CmsGreetingMagicComponent,
  CmsHomeBannerMagicComponent,
  CmsHomeNavigationBarMagicComponent,
  CmsLatestArticlesMagicComponent,
  CmsMixAndMatchMagicComponent,
  CmsPlanCatalogDetailMagicComponent,
  CmsPlanCatalogMagicComponent,
  CmsPlanWithDeviceOptionsMagicComponent,
  CmsSubOffersMagicComponent,
  CmsTopOffersMagicComponent,
  ErrorMagicComponent,
  SearchMagicComponent,
} from '@libs/magwid';

export const MAGIC_WIDGETS: Record<string, Type<unknown>> = {
  'widget-cms-plan-catalog-detail': CmsPlanCatalogDetailMagicComponent,
  'widget-cms-plan-catalog': CmsPlanCatalogMagicComponent,
  'widget-cms-banner': CmsBannerMagicComponent,
  'widget-cms-features-strip': CmsFeaturesStripMagicComponent,
  'widget-cms-sub-offers': CmsSubOffersMagicComponent,
  'widget-cms-greeting': CmsGreetingMagicComponent,
  'widget-cms-faq': CmsFaqMagicComponent,
  'widget-cms-buy-with-devices': CmsPlanWithDeviceOptionsMagicComponent,
  'magic-widget-error': ErrorMagicComponent,
  'magic-widget-search': SearchMagicComponent,
  'widget-cms-top-offers': CmsTopOffersMagicComponent,
  'widget-cms-home-banner': CmsHomeBannerMagicComponent,
  'widget-cms-home-navigation-bar': CmsHomeNavigationBarMagicComponent,
  'widget-cms-latest-articles': CmsLatestArticlesMagicComponent,
  'widget-cms-cta-support': CmsCtaSupportMagicComponent,
  'widget-cms-mix-and-match': CmsMixAndMatchMagicComponent,
};
