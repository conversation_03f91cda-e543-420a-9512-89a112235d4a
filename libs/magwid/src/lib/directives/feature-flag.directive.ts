import { computed, Directive, effect, inject, input, TemplateRef, ViewContainerRef } from '@angular/core';
import { Store } from '@ngxs/store';
import { ConfigState } from '@libs/plugins';
import { FeatureFlagEnum } from '@libs/types';

@Directive({
  selector: '[featureFlag]',
  standalone: true,
})
export class FeatureFlagDirective {
  private store = inject(Store);
  private templateRef = inject(TemplateRef<object>);
  private viewContainer = inject(ViewContainerRef);

  featureFlag = input<FeatureFlagEnum>(null);
  private condition = computed(() => {
    return this.store.selectSnapshot(ConfigState.isFeatureEnabled(this.featureFlag()));
  });

  constructor() {
    effect(() => {
      if (this.condition()) {
        this.viewContainer.createEmbeddedView(this.templateRef);
      } else {
        this.viewContainer.clear();
      }
    });
  }
}
