import { CheckoutStep } from '@libs/widgets';
import { eBusinessFlow } from '@libs/types';
import {
  CheckoutPaymentMagicComponent,
  CheckoutSummaryMagicComponent,
  DeliveryInvoiceMagicComponent,
  SimConfigurationMagicComponent,
} from '../components';

export const checkoutSteps: CheckoutStep[] = [
  {
    path: '/cart',
    title: 'Cart Summary',
    shortCode: [eBusinessFlow.WorkflowStateType.PRE_VALIDATION],
    component: null,
  },
  {
    path: '/checkout/configuration',
    title: 'Configuration',
    shortCode: eBusinessFlow.WorkflowStateType.PRODUCT_CONF,
    component: SimConfigurationMagicComponent,
  },
  {
    path: '/checkout/delivery-invoice',
    title: 'Delivery&Invoice',
    shortCode: [
      eBusinessFlow.WorkflowStateType.BILLING_ADDRESS,
      eBusinessFlow.WorkflowStateType.INVOICE_SELECT,
      eBusinessFlow.WorkflowStateType.DEFAULT_DELIVERY_ADDRESS_SELECTION,
      eBusinessFlow.WorkflowStateType.DELIVERY,
    ],
    component: DeliveryInvoiceMagicComponent,
  },
  {
    path: '/checkout/payment',
    title: 'Payment',
    shortCode: [eBusinessFlow.WorkflowStateType.PAYMENT_BILLING, eBusinessFlow.WorkflowStateType.CREDIT_CHECK],
    component: CheckoutPaymentMagicComponent,
  },
  {
    path: '/checkout/summary',
    title: 'Summary',
    shortCode: [eBusinessFlow.WorkflowStateType.ORDER_REVIEW, eBusinessFlow.WorkflowStateType.ORDER_SUBMIT],
    component: CheckoutSummaryMagicComponent,
  },
  {
    path: '/checkout/order-success',
    title: 'Order Success',
    shortCode: eBusinessFlow.WorkflowStateType.ORDER_SUMMARY,
    component: null,
  },
];
