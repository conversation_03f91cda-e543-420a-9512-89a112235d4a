import { inject } from '@angular/core';
import { UserGetLoggedInUserAction, UserState } from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';
import { Store } from '@ngxs/store';
import { ResolveFn } from '@angular/router';

export const loggedInUserResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);

  return [
    {
      selector: store.selectSnapshot(UserState.getLoggedInUser)?.username,
      action: () => {
        return new UserGetLoggedInUserAction();
      },
    },
  ];
};
