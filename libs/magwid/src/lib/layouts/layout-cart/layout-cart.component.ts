import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, inject } from '@angular/core';
import { LayoutPageCartComponent } from '@libs/widgets';
import { HeaderMagicComponent } from '@libs/magwid';
import { Router } from '@angular/router';
import { TranslatePipe } from '@libs/plugins';
import { QuoteState } from '@libs/bss';
import { eBusinessFlow } from '@libs/types';
import { select } from '@ngxs/store';

@Component({
  selector: 'magic-widget-cart-page-layout',
  templateUrl: './layout-cart.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [LayoutPageCartComponent, HeaderMagicComponent, TranslatePipe],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class LayoutCartComponent {
  private router = inject(Router);

  private quote = select(QuoteState.quote);

  back() {
    switch (this.quote().currentBusinessFlowSpecShortCode) {
      case eBusinessFlow.Specification.PACKAGE_CHANGE:
        return this.router.navigate(['mobile-postpaid'], {
          queryParams: {
            flow: this.quote().currentBusinessFlowSpecShortCode,
            customerOrderId: this.quote().customerOrderId,
          },
        });
      default:
        return this.router.navigate(['/']);
    }
  }
}
