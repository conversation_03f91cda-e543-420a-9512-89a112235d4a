import { ChangeDetectionStrategy, Component } from '@angular/core';
import { LayoutPageDefaultComponent } from '@libs/widgets';
import { HeaderMagicComponent, NavigationMagicComponent, PageHeadingMagicComponent } from '@libs/magwid';

@Component({
  selector: 'magic-widget-default-page-layout',
  templateUrl: './layout-default.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [LayoutPageDefaultComponent, NavigationMagicComponent, HeaderMagicComponent, PageHeadingMagicComponent],
})
export class LayoutDefaultComponent {}
