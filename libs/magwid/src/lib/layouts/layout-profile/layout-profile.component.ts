import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { UserState } from '@libs/bss';
import { UserGreetingMagicComponent, HeaderMagicComponent, NavigationMagicComponent } from '@libs/magwid';
import { LayoutPageDefaultComponent } from '@libs/widgets';
import { select } from '@ngxs/store';

@Component({
  selector: 'magic-widget-layout-profile',
  templateUrl: './layout-profile.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NavigationMagicComponent, UserGreetingMagicComponent, HeaderMagicComponent, LayoutPageDefaultComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class LayoutProfileComponent {
  firstName = select(UserState.getFirstName);
}
