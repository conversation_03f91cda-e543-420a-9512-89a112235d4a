import { Location } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject } from '@angular/core';
import { HeaderMagicComponent, NavigationMagicComponent, ProductDetailGreetingMagicComponent } from '@libs/magwid';
import { LayoutPageDetailComponent } from '@libs/widgets';
import { RouteDataService } from '@libs/core';
import { TranslatePipe } from '@libs/plugins';

@Component({
  selector: 'magic-widget-layout-detail',
  templateUrl: './layout-detail.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [
    LayoutPageDetailComponent,
    HeaderMagicComponent,
    NavigationMagicComponent,
    ProductDetailGreetingMagicComponent,
    TranslatePipe,
  ],
})
export class LayoutDetailComponent {
  private location = inject(Location);
  private routeDataService = inject(RouteDataService);

  routeData = computed(() => this.routeDataService.data());

  back() {
    this.location.back();
  }
}
