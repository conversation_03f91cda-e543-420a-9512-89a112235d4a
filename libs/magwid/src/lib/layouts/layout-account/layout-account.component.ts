import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject } from '@angular/core';
import { AccountPageHeadingMagicComponent, HeaderMagicComponent, NavigationMagicComponent } from '@libs/magwid';
import { LayoutPageAccountComponent } from '@libs/widgets';
import { RouteDataService } from '@libs/core';
import { TranslatePipe } from '@libs/plugins';
import { Router } from '@angular/router';

@Component({
  selector: 'magic-widget-layout-detail',
  templateUrl: './layout-account.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [
    LayoutPageAccountComponent,
    HeaderMagicComponent,
    NavigationMagicComponent,
    AccountPageHeadingMagicComponent,
    TranslatePipe,
  ],
})
export class LayoutAccountComponent {
  private router = inject(Router);
  private routeDataService = inject(RouteDataService);

  routeData = computed(() => this.routeDataService.data());

  back() {
    this.router.navigate(['/my/products']);
  }
}
