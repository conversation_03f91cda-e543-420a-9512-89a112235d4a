import {
  Component,
  computed,
  CUSTOM_ELEMENTS_SCHEMA,
  effect,
  inject,
  input,
  TemplateRef,
  Type,
  viewChild,
  ViewContainerRef,
} from '@angular/core';
import { ConfigState } from '@libs/plugins';
import { SubLayoutTypeEnum } from '@libs/types';
import {
  LayoutContentDefaultComponent,
  LayoutContentDoubleColumnsComponent,
  LayoutContentWithAsideComponent,
} from '@libs/widgets';
import { Store } from '@ngxs/store';

@Component({
  selector: 'magic-widget-sub-layout',
  template: `
    <ng-template #layout> </ng-template>
    <ng-template #content>
      <ng-content></ng-content>
    </ng-template>
  `,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class SubLayoutMagicComponent {
  private store = inject(Store);

  section = input<string>('');

  layoutContainer = viewChild('layout', { read: ViewContainerRef });
  contentTemplate = viewChild('content', { read: TemplateRef });

  layoutConfig = computed<SubLayoutTypeEnum>(() => {
    return this.store.selectSnapshot(ConfigState.getDeep('layouts.' + this.section()));
  });

  subLayout = computed(() => {
    return this.subLayouts[this.layoutConfig()] ?? this.subLayouts[SubLayoutTypeEnum.DEFAULT];
  });

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  subLayouts: Record<SubLayoutTypeEnum, Type<any>> = {
    [SubLayoutTypeEnum.DEFAULT]: LayoutContentDefaultComponent,
    [SubLayoutTypeEnum.DOUBLE_COLUMNS]: LayoutContentDoubleColumnsComponent,
    [SubLayoutTypeEnum.WITH_ASIDE]: LayoutContentWithAsideComponent,
  };

  constructor() {
    effect(() => {
      if (this.subLayout() && this.layoutContainer) {
        this.layoutContainer().clear();

        this.layoutContainer().createComponent(this.subLayout(), {
          projectableNodes: [this.contentTemplate().createEmbeddedView({}).rootNodes],
        });
      }
    });
  }
}
