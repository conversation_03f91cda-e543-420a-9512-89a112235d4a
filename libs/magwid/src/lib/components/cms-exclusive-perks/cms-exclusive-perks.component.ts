import { Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CmsBlogCard, CmsExclusivePerksComponent as WidgetCmsExclusivePerksComponent } from '@libs/widgets';

@Component({
  selector: 'magic-widget-cms-exclusive-perks',
  templateUrl: './cms-exclusive-perks.component.html',
  imports: [WidgetCmsExclusivePerksComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class CmsExclusivePerksComponent {
  items: CmsBlogCard[] = [
    {
      title: 'The Best Way to Stay Connected!',
      helperText: 'Enjoy ultra-fast 5G and seamless coverage anywhere, anytime!',
      image: 'assets/images/perk-image-1.jpg',
    },
    {
      title: 'Exclusive Benefits Just for You',
      helperText: 'Customize your plan and save more with family and multi-line discounts.',
      image: 'assets/images/perk-image-2.jpg',
    },
    {
      title: 'Your Security is Our Priority',
      helperText: 'Stay safe online with free call filters and advanced internet protection.',
      image: 'assets/images/perk-image-3.jpg',
    },
  ];
}
