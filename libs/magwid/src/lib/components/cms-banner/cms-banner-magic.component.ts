import { Component, computed, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { CmsBannerComponent as WidgetCmsBannerComponent } from '@libs/widgets';

@Component({
  selector: 'magic-widget-cms-banner',
  templateUrl: './cms-banner-magic.component.html',
  imports: [WidgetCmsBannerComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class CmsBannerMagicComponent {
  title = input('');
  description = input<{ value: string }>();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  image = input<any>();

  imageUrl = computed(() => this.image()?.media_image?.data.meta.url);
}
