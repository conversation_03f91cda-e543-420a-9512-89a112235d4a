import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import { MyProductsState, WalletGetWalletAction, WalletState } from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';
import { Store } from '@ngxs/store';
import { myProductDetailResolverBase } from '@libs/magwid';

export const walletResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);

  return [
    {
      ...myProductDetailResolverBase()[0],
      next: [
        {
          selector: store.selectSnapshot(WalletState.wallets)?.length,
          action: () => {
            const billingId = store.selectSnapshot(MyProductsState.products).productBillingAccountIds;
            return new WalletGetWalletAction({
              billingAccountId: billingId,
            });
          },
        },
      ],
    },
  ];
};
