import { Component, computed, CUSTOM_ELEMENTS_SCHEMA, effect, inject, signal } from '@angular/core';
import { select, Store } from '@ngxs/store';
import { CurrencyPipe } from '@angular/common';
import { MagicConfig, TranslatePipe, TranslateService } from '@libs/plugins';
import { AtomIconArrowRightComponent, WalletComponent as WidgetWalletComponent } from '@libs/widgets';
import { CurrentState, WalletData, WalletState } from '@libs/bss';
import { walletResolver } from './wallet-resolver';

@Component({
  selector: 'magic-widget-wallet',
  templateUrl: './wallet-magic.component.html',
  imports: [WidgetWalletComponent, CurrencyPipe, TranslatePipe],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
@MagicConfig({
  resolve: [walletResolver],
})
export class WalletMagicComponent {
  private translateService = inject(TranslateService);
  private store = inject(Store);

  billingAccountId = select(CurrentState.currentBillingAccountId);

  walletData = signal<WalletData>(null);

  walletInfo = computed(() => ({
    balanceAmount: this.walletData()?.balance?.balanceAmount ?? 0,
    currency: this.walletData()?.balance?.currency,
    isWalletVisible: this.walletData()?.wallet?.billingAccountHasWallet,
    isVisible: this.walletData()?.wallet?.billingAccountHasWallet,
  }));

  actionListItems = [
    {
      text: this.translateService.translate('topUp'),
      iconTrailing: AtomIconArrowRightComponent,
      onClick: () => {
        console.log('top_up');
      },
    },
    {
      text: this.translateService.translate('walletTransfer'),
      iconTrailing: AtomIconArrowRightComponent,
      onClick: () => {
        console.log('wallet_transfer');
      },
    },
    {
      text: this.translateService.translate('walletTransactionHistory'),
      iconTrailing: AtomIconArrowRightComponent,
      onClick: () => {
        console.log('wallet_transaction_history');
      },
    },
  ];

  constructor() {
    effect(() => {
      this.walletData.set(this.store.selectSnapshot(WalletState.wallet(this.billingAccountId())));
    });
  }
}
