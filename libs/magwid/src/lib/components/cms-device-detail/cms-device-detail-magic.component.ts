import { Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import {
  CmsDeviceImagesComponent as WidgetCmsDeviceImagesComponent,
  CmsDevicePropertiesComponent as WidgetCmsDevicePropertiesComponent,
} from '@libs/widgets';
import { Cms } from '@libs/widgets';

@Component({
  selector: 'magic-widget-cms-device-detail',
  templateUrl: './cms-device-detail-magic.component.html',
  styleUrls: ['./cms-device-detail-magic.component.scss'],
  imports: [WidgetCmsDeviceImagesComponent, WidgetCmsDevicePropertiesComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class CmsDeviceDetailMagicComponent {
  deviceImages = input<Cms.DeviceImages>({
    productName: 'iPhone 16 Pro Max',
    image: 'assets/images/iphone16promax.png',
    images: [
      { src: 'assets/images/iphone16promax.png', id: '1' },
      { src: 'assets/images/iphone16promax-side.png', id: '2' },
      { src: 'assets/images/iphone16promax.png', id: '3' },
      { src: 'assets/images/iphone16promax.png', id: '4' },
      { src: 'assets/images/iphone16promax.png', id: '5' },
      { src: 'assets/images/iphone16promax.png', id: '6' },
    ],
  });
  deviceProperties = input<Cms.DeviceProperties>({
    tags: [
      {
        text: 'Up to 157.25 discount',
        appearance: 'green',
      },
    ],
    plan: {
      title: 'Mobile Postpaid Basic Plan',
      plan: 'Mobile Postpaid Basic Plan',
      properties: [
        {
          label: 'Data',
          value: '10 GB',
        },
        {
          label: 'Voice',
          value: '1000Min.',
        },
        {
          label: 'SMS',
          value: '1000SMS',
        },
      ],
      price: '1000',
    },
    name: 'Apple iPhone 16 Pro Max',
    price: '$60.50',
    period: 'months',
    availability: 'Available (Pre-order)',
    colors: [
      {
        colorCode: '#000000',
        name: 'Black',
        isSelected: true,
        isAvailable: true,
      },
      {
        colorCode: '#ffffff',
        name: 'White',
        isSelected: false,
        isAvailable: true,
      },
    ],
    storages: ['128GB', '256GB', '512GB', '1TB'],
    installments: ['36', '24', '12', 'One-time'],
    installmentInfo: [
      {
        icon: 'shieldTick',
        text: 'Secure payment',
      },
      {
        icon: 'shippingTruck',
        text: 'Free delivery',
      },
      {
        icon: 'invoice',
        text: '24 month warranty',
      },
    ],
  });
}
