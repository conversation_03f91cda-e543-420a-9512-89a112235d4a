import {
  ChangeDetectionStrategy,
  Component,
  computed,
  CUSTOM_ELEMENTS_SCHEMA,
  inject,
  output,
  signal,
} from '@angular/core';
import { Router } from '@angular/router';
import {
  GetInquirePartyPrivacyDocumentAction,
  LovState,
  PrivacySpecificationState,
  RegisterCreateCustomerAccountAction,
  UserCreateCredentialEcaTokenAction,
  UserState,
  UtilityState,
} from '@libs/bss';
import { REGISTER_REQUEST_DATE_FORMAT, StartLoginAction } from '@libs/core';
import { Config, ConfigState, MagicConfig, ModalService, ModalSize, TranslatePipe } from '@libs/plugins';
import { ContactMediumType, Register } from '@libs/types';
import {
  Checkbox,
  CreateAccountFormComponent,
  SuccessInfoCardComponent,
  TermsAndConditionsComponent,
} from '@libs/widgets';
import { select, Store } from '@ngxs/store';
import { format } from 'date-fns';
import { switchMap } from 'rxjs';
import { registerResolver } from './register.resolver';

@Component({
  selector: 'magic-widget-register',
  templateUrl: './register-magic.component.html',
  styleUrls: ['./register-magic.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [CreateAccountFormComponent, TranslatePipe, SuccessInfoCardComponent],
})
@MagicConfig({
  resolve: [registerResolver],
})
export class RegisterMagicComponent {
  private store = inject(Store);
  private modal = inject(ModalService);
  private router = inject(Router);

  registerDone = output<boolean>();

  languageTypes = select(LovState.languageType);
  mappedLanguageTypes = computed(() => this.languageTypes()?.mappedLanguageTypes());

  partyPrivacySpecificationInfo = select(PrivacySpecificationState.partyPrivacySpecificationInfo);
  mappedPartyPrivacySpecificationInfo = computed(() =>
    this.partyPrivacySpecificationInfo().mappedPartyPrivacySpecification(),
  );

  customerMinAgeParameter = select(UtilityState.generalParameter);

  countryTypes = select(LovState.countryType);
  countryOptions = computed(() => this.countryTypes()?.countryOptions('phoneNumber', 0));

  isRegistrationSuccessful = signal(false);
  isRegistrationRequestFailed = signal(false);

  partyPrivacyDocument = select(PrivacySpecificationState.partyPrivacyDocument);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  register(event: any) {
    const password = event.password as string;
    this.store
      .dispatch(new UserCreateCredentialEcaTokenAction({ password }))
      .pipe(switchMap(() => this.store.selectOnce(UserState.credentialToken)))
      .subscribe({
        next: (credentialToken) => {
          if (credentialToken) {
            this.store
              .dispatch(new RegisterCreateCustomerAccountAction(this.buildRequest(event, credentialToken)))
              .subscribe({
                next: () => {
                  this.isRegistrationSuccessful.set(true);
                  this.isRegistrationRequestFailed.set(false);
                  this.registerDone.emit(true);
                },
                error: () => {
                  // todo: how to handle error message management
                  this.isRegistrationSuccessful.set(false);
                  this.isRegistrationRequestFailed.set(true);
                  this.registerDone.emit(false);
                },
              });
          }
        },
      });
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  buildRequest(formModel: any, credentialToken: string): Register.RegisterRequest {
    const contactMedium: ContactMediumType.ContactMediumDTO[] = [
      {
        contactData: formModel?.phoneNumber,
        contactDataExtension: formModel?.country,
        isPrimary: true,
        contactMediumType: {
          shortCode: 'GSM',
        },
      } as ContactMediumType.ContactMediumDTO,
      {
        contactData: formModel?.email,
        contactDataExtension: formModel?.country,
        isPrimary: true,
        contactMediumType: {
          shortCode: 'EMAIL',
        },
      } as ContactMediumType.ContactMediumDTO,
    ];

    const partyPrivacySpecList = this.partyPrivacySpecificationInfo()?.partyPrivacySpecification.map((spec) => ({
      ...spec,
      authorized: !!formModel.partyPrivacySpecList.find((item: Checkbox) => +item.id === spec.id)?.isChecked,
    }));

    const {
      keycloak: { config },
      url,
    }: Config.State = this.store.selectSnapshot(ConfigState.getAll);

    return {
      partyTypeShortCode: 'INDV',
      customerTypeShortCode: 'RSDNTL',
      employeeNumber: null,
      langShortCode: formModel?.langShortCode,
      email: formModel?.email,
      firstName: formModel?.firstName,
      lastName: formModel?.lastName,
      maidenName: null,
      secretKeyword: null,
      birthDate: format(new Date(formModel.birthDate), REGISTER_REQUEST_DATE_FORMAT),
      contactMedium,
      address: null,
      consentEmail: false,
      consentSms: false,
      announcementConsent: false,
      newsletterConsent: false,
      idpVerifyEmailUserId: config.clientId,
      idpVerifyEmailRedirectUri: `${url['app']}/`,
      passtoken: credentialToken,
      partyPrivacySpecList,
    } as Register.RegisterRequest;
  }

  goToHome() {
    this.router.navigate(['/']);
  }

  goToLogin() {
    this.store.dispatch(
      new StartLoginAction({
        redirectUri: '/',
      }),
    );
  }

  openTermsAndConditionsModal(specId: number) {
    this.store.dispatch(new GetInquirePartyPrivacyDocumentAction(specId)).subscribe(() => {
      const modalService = this.modal.open(TermsAndConditionsComponent, {
        title: this.partyPrivacyDocument().partyPrivacyDocumentTmpl.name,
        size: ModalSize.SMALL,
        data: {
          text: this.partyPrivacyDocument().partyPrivacyDocumentTmpl.tmplBody,
          onAcceptClick: () => {
            modalService.close();
          },
        },
      });
    });
  }
}
