import { Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject, input } from '@angular/core';
import { Router } from '@angular/router';
import { Cms, CmsCtaSupportComponent } from '@libs/widgets';

@Component({
  selector: 'magic-widget-cms-cta-support',
  templateUrl: './cms-cta-support-magic.component.html',
  imports: [CmsCtaSupportComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class CmsCtaSupportMagicComponent {
  private router = inject(Router);

  title = 'Create Your Own Mobile Plan';
  subtitle = 'Choose your data, calls, and texts your plan, your way!';
  buttons = input<Cms.CtaButton[]>([
    {
      label: 'Build Your Prepaid Plan',
      action: () => {
        this.router.navigate(['/mobile-prepaid']);
      },
    },
    {
      label: 'Build Your Postpaid Plan',
      action: () => {
        this.router.navigate(['/mobile-postpaid']);
      },
    },
  ]);

  links = computed<Cms.CtaButton[]>(() =>
    this.buttons()?.map((button: Cms.CtaButton) => ({
      label: button.label,
      action: button.action,
      appearance: button.appearance || 'primary',
    })),
  );
}
