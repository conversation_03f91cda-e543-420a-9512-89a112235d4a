import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, computed, inject, input } from '@angular/core';
import { MagicConfig } from '@libs/plugins';
import { cmsMixAndMatchResolver } from './cms-mix-and-match.resolver';
import { CmsLayoutContentDefaultComponent, CmsMixAndMatchComponent, CmsMixAndMatchDescription } from '@libs/widgets';
import { select, Store } from '@ngxs/store';
import { BusinessFlowInteractionService, CmsGetMixAndMatchAction, CmsState } from '@libs/bss';
import { CmsMixAndMatchMixer } from '@libs/widgets';
import { CMS, OfferInstanceCharEnum } from '@libs/types';

@Component({
  selector: 'magic-widget-cms-mix-and-match',
  templateUrl: './cms-mix-and-match-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [CmsMixAndMatchComponent, CmsLayoutContentDefaultComponent],
})
@MagicConfig({
  resolve: [cmsMixAndMatchResolver],
})
export class CmsMixAndMatchMagicComponent {
  private store = inject(Store);
  private businessFlowInteractionService = inject(BusinessFlowInteractionService);

  title = input<string>();
  body = input<{ value: string }>();
  plan_benefits = input([]);

  cmsData = select(CmsState.mixAndMatch);

  currency = '€';

  private setVariables(key: string): { unit: string; icon: string } {
    switch (key) {
      case 'data':
        return {
          unit: 'GB',
          icon: 'internet',
        };
      case 'voice':
        return {
          unit: 'min',
          icon: 'calling',
        };
      case 'sms':
        return {
          unit: 'SMS',
          icon: 'message',
        };
      default:
        return {
          unit: key,
          icon: key,
        };
    }
  }

  mixer = computed((): CmsMixAndMatchMixer[] => {
    const data = this.cmsData();
    return data?.filters
      ?.filter((item: CMS.MixAndMatchFilter) => item.id !== OfferInstanceCharEnum.SALE_TYPE)
      .map((item: CMS.MixAndMatchFilter) => {
        const variables = this.setVariables(item.id);
        return {
          label: item.id.charAt(0).toUpperCase() + item.id.slice(1),
          value: parseInt(item.value[0]),
          min: parseInt(item.options[0]),
          max: parseInt(item.options[item.options.length - 1]),
          step: 1,
          customValues: item.options,
          unit: variables.unit,
          icon: variables.icon,
          disabled: false,
          showButtons: true,
          showValue: true,
          unlimitedOffset: 10,
        };
      });
  });

  description = computed<CmsMixAndMatchDescription[]>(() => {
    return [
      {
        title: this.title(),
        description: this.body().value,
        image: (this.plan_benefits().find(Boolean)?.image?.data?.meta?.url as string) ?? '',
      },
    ];
  });

  onJoinNow() {
    this.businessFlowInteractionService.byodInitialize$(Number(this.cmsData().results[0].offerId)).subscribe();
  }

  onSliderChange(label: string, value: number) {
    if (isNaN(value)) {
      return;
    }
    const data = this.cmsData();
    const a = data.filters.map((item: CMS.MixAndMatchFilter) => {
      if (item.id === label.toLowerCase()) {
        return { ...item, value: [value.toString()] };
      }
      return item;
    });
    this.store.dispatch(new CmsGetMixAndMatchAction({ filters: a as CMS.MixAndMatchFilter[] }));
  }
}
