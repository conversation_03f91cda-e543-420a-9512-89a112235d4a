import { CmsGetMixAndMatchAction } from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';
import { ResolveFn, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { CatalogGroupContractType } from '@libs/types';

export const cmsMixAndMatchResolver: ResolveFn<MagicResolverModel[]> = (
  route: ActivatedRouteSnapshot,
  state: RouterStateSnapshot,
) => {
  const contractType = () => {
    if (state.url.includes('mobile-postpaid')) {
      return CatalogGroupContractType.POSTPAID;
    } else if (state.url.includes('mobile-prepaid')) {
      return CatalogGroupContractType.PREPAID;
    }
    return '';
  };

  return [
    {
      selector: false,
      action: () => {
        return new CmsGetMixAndMatchAction({
          filters: [
            { id: 'data', value: ['100'], options: [] },
            { id: 'sms', value: ['unlimited'], options: [] },
            { id: 'voice', value: ['500'], options: [] },
            { id: 'saleType', value: [contractType()], options: [] },
          ],
        });
      },
    },
  ];
};
