import {
  Component,
  computed,
  CUSTOM_ELEMENTS_SCHEMA,
  effect,
  inject,
  input,
  model,
  signal,
  untracked,
  ViewChild,
} from '@angular/core';
import { select, Store } from '@ngxs/store';
import { EmptyStateComponent, PlanUsage, SegmentOptions, UsageSummaryPlan } from '@libs/widgets';
import { MagicConfig, TranslatePipe, TranslateService } from '@libs/plugins';
import {
  CurrentState,
  GeneralStatusData,
  MyProductsState,
  MyServicesGetProductDetailAction,
  MyServicesGetProductListAction,
  ProductData,
  ProductDetailData,
  ProductListData,
  UsageSummaryGetUsageSummaryAction,
  UsageSummaryState,
} from '@libs/bss';
import { usageSummaryResolver } from './usage-summary.resolver';
import { InteractionsMagicComponent } from '../interactions/interactions-magic.component';
import { eBusinessFlow, eProduct, Product, UsageSummary, UsageType } from '@libs/types';
import { SelectiveUsageSummaryComponent } from '../../../../../widgets/src/lib/components/usage';
import { DatePipe } from '@angular/common';
import { PhoneNumberPipe } from '@libs/core';

@Component({
  selector: 'magic-widget-usage-summary',
  templateUrl: './usage-summary-magic.component.html',
  imports: [SelectiveUsageSummaryComponent, TranslatePipe, InteractionsMagicComponent, EmptyStateComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [DatePipe, PhoneNumberPipe],
})
@MagicConfig({
  resolve: [usageSummaryResolver],
})
export class UsageSummaryMagicComponent {
  private store = inject(Store);
  protected datePipe = inject(DatePipe);
  protected translateService = inject(TranslateService);
  protected phoneNumberPipe = inject(PhoneNumberPipe);

  @ViewChild(SelectiveUsageSummaryComponent) usageSummaryComponent: SelectiveUsageSummaryComponent;

  private page = signal(0);

  showMoreButton = input(true);
  lockedBillingAccount = input<number>();
  selectedPlan = model<UsageSummaryPlan>();

  private productsDetailData = select(MyProductsState.products);
  private usageSummary = select(UsageSummaryState.usageSummary);
  private totalProductCount = select(MyProductsState.totalProductsCount);
  private productList = select(MyProductsState.productList);

  product = computed(() => {
    if (this.lockedBillingAccount()) {
      return this.productsDetailData().getProductDataByBillingAccountId(this.lockedBillingAccount());
    }
    return this.productsDetailData().toProductData().find(Boolean);
  });

  showMoreEnabled = computed(() => this.myPlans().length !== this.totalProductCount());

  constructor() {
    effect(() => {
      if (this.selectedPlan() && !this.lockedBillingAccount()) {
        this.getPlanSummary(this.selectedPlan().product.billingAccountId);
      }
    });

    effect(() => {
      if (this.lockedBillingAccount() && !untracked(this.selectedPlan)) {
        this.selectedPlan.set(this.toOption(this.product()));
        return;
      }

      if (!untracked(this.selectedPlan)) {
        this.selectedPlan.set(untracked(this.myPlans)?.[0]);
      }
    });
  }

  currentUsage = computed<PlanUsage>(() => {
    if (!this.selectedPlan()) {
      return {
        usageOptions: [],
        usageSummary: [],
      };
    }
    const usageSummary = this.usageSummary().filter(
      (usage) => usage.billingAccountId === this.selectedPlan().product?.billingAccountId,
    );

    const usageSummaryTabItems: SegmentOptions[] = Array.from(
      new Map(
        usageSummary.map((usage) => [
          usage.usageType,
          {
            text: usage.usageType,
            ariaControls: usage.usageType,
          },
        ]),
      ).values(),
    );

    const orderedUsageTypes = Object.values(UsageType);

    const usageOptions = [...usageSummaryTabItems].sort((unsorted, sorted) => {
      const unsortedValue = orderedUsageTypes.indexOf(unsorted.text.toLowerCase() as UsageType);
      const sortedValue = orderedUsageTypes.indexOf(sorted.text.toLowerCase() as UsageType);

      return unsortedValue - sortedValue;
    });

    return {
      usageOptions,
      usageSummary: usageSummary?.map((usage) => {
        // Find the product detail with matching productId

        const active = usage.productDetail.status.shortCode === eProduct.ProductStatusShortCodes.ACTV;

        const expireDateText = this.getExpirationText(active, usage.productDetail);

        return {
          id: usage.usageType,
          deactivated: !active,
          data: [
            {
              // Use the name from the matching product or fallback to usage type
              label: usage.productDetail?.name || usage.usageType,
              helperText: expireDateText,
              value: usage.usageAmount,
              maxValue: usage.totalAmount,
              valueType: this.findUnit(usage),
              isAnimated: true,
              isUnlimited: usage.isUnlimited,
              type: 'bar',
            },
          ],
        };
      }),
    };
  });

  myPlans = computed<UsageSummaryPlan[]>(() => {
    return this.buildMyPlans(this.productList());
  });

  interactionsLevel = computed(() => {
    return this.lockedBillingAccount()
      ? eBusinessFlow.Levels.PRODUCT_DETAIL_USAGE_SUMMARY
      : eBusinessFlow.Levels.USAGE_SUMMARY;
  });

  protected getExpirationText(active: boolean, matchingProductDetail: Product.ProductDetail): string {
    if (active && matchingProductDetail?.validUntilDate) {
      return this.translateService.translate('expireDate', {
        date: this.formatDate(matchingProductDetail.validUntilDate),
      });
    }

    if (!active && matchingProductDetail?.deactivationDate) {
      return this.translateService.translate('cancelledDate', {
        date: this.formatDate(matchingProductDetail.deactivationDate),
      });
    }

    return '';
  }

  onLoadMorePlans(): void {
    if (this.usageSummaryComponent) {
      this.usageSummaryComponent.setLoading(true);
    }

    this.page.set(this.page() + 1);

    this.store
      .dispatch(
        new MyServicesGetProductListAction({
          customerId: this.store.selectSnapshot(CurrentState.customerId),
          statusCodes: [
            eProduct.ProductStatusShortCodes.ACTV,
            eProduct.ProductStatusShortCodes.PNDG,
            eProduct.ProductStatusShortCodes.SPND,
            //eProduct.ProductStatusShortCodes.CNCL,
          ],
          planBundleSummary: 1,
          page: this.page(),
          size: 5,
        }),
      )
      .subscribe({
        complete: () => {
          if (this.usageSummaryComponent) {
            this.usageSummaryComponent.setLoading(false);
          }
        },
      });
  }

  getPlanSummary(billingAccountId: number) {
    this.store
      .dispatch(
        new MyServicesGetProductDetailAction({
          customerId: this.store.selectSnapshot(CurrentState.customerId),
          billingAccountId: billingAccountId,
          statusCodes: [
            eProduct.ProductStatusShortCodes.ACTV,
            eProduct.ProductStatusShortCodes.PNDG,
            eProduct.ProductStatusShortCodes.SPND,
            // eProduct.ProductStatusShortCodes.CNCL,
          ],
          planBundleSummary: 1,
          productDetailList: 1,
        }),
      )
      .subscribe(() => {
        this.store.dispatch(
          new UsageSummaryGetUsageSummaryAction({
            productBillingAccountId: [billingAccountId],
          }),
        );
      });
  }

  protected findUnit(usage: UsageSummary.UsageSummary): string {
    if (usage.unit) {
      return usage.unit;
    }

    switch (usage.usageType) {
      case 'voice':
        return 'Min';
      case 'sms':
        return 'SMS';
      case 'data':
      default:
        return 'GB';
    }
  }

  private buildMyPlans(productsData: ProductListData) {
    return productsData?.toProductData().map((product, index) => {
      return this.toOption(product, index);
    });
  }

  private findProductById(
    productDetailList: Product.ProductDetail[],
    productId: string | number,
  ): Product.ProductDetail | undefined {
    return productDetailList?.find((product) => product.productId === Number(productId));
  }

  private formatDate(date: number & Date): string {
    return this.datePipe.transform(date) || '';
  }

  private toOption(product: ProductData, index = 0): UsageSummaryPlan {
    const phoneNumber = this.phoneNumberPipe.transform(product.productDetailList().msisdnNumber);
    const labelPart = [product.planBundleSummary?.name];
    if (phoneNumber) {
      labelPart.push(phoneNumber);
    }

    const productDetailListData = new ProductDetailData(product.product.productDetailList);
    const status = new GeneralStatusData(productDetailListData.plan?.status).statusTagAppearance;

    return {
      label: labelPart.join(' - '),
      name: 'optionPackage',
      product: product,
      appearance: status,
      statusDescription: productDetailListData.plan?.status.description,
      value: product.billingAccountId?.toString(),
      isSelected: index === 0,
    };
  }
}
