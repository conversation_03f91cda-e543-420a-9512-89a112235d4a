import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import {
  CurrentState,
  CustomerState,
  InquireCustomerOrdersAction,
  LovGetOrderStatusesAction,
  LovGetOrderTypesAction,
  LovState,
} from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';
import { Store } from '@ngxs/store';
import { CustomerOrder } from '@libs/types';

export const orderAndQuoteResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);
  const customerId = store.selectSnapshot(CurrentState.customerId);

  return [
    {
      selector: store.selectSnapshot(CustomerState.customerOrdersMap(CustomerOrder.SegmentType.ORDER))?.content?.length,
      action: () => {
        return new InquireCustomerOrdersAction({
          customerId,
          searchType: 'order',
          pageName: CustomerOrder.SegmentType.ORDER,
          page: 0,
          size: 5,
        });
      },
    },
    {
      selector: store.selectSnapshot(LovState.orderTypes)?.items?.length,
      action: () => new LovGetOrderTypesAction(),
    },
    {
      selector: store.selectSnapshot(LovState.orderStatuses)?.items?.length,
      action: () => new LovGetOrderStatusesAction(),
    },
  ];
};
