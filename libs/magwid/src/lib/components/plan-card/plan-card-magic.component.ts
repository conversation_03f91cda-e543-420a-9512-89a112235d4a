import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject, signal } from '@angular/core';
import { MagicConfig, TranslatePipe, TranslateService } from '@libs/plugins';
import { <PERSON><PERSON>, DataList, PlanCardComponent as WidgetPlanCardComponent, Tile } from '@libs/widgets';
import { select } from '@ngxs/store';
import { eBusinessFlow, eProduct, OfferInstanceProductTypeEnum } from '@libs/types';
import { CurrentState, MyProductsState, ProductCharListData } from '@libs/bss';
import { getColorByProductStatus, getProductDetailPrice, PhoneNumberPipe } from '@libs/core';
import { CurrencyPipe, DatePipe } from '@angular/common';
import { planCardResolver } from './plan-card.resolver';
import { InteractionsMagicComponent } from '../interactions/interactions-magic.component';

@Component({
  selector: 'magic-widget-plan-card',
  imports: [WidgetPlanCardComponent, TranslatePipe, InteractionsMagicComponent],
  templateUrl: './plan-card-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [PhoneNumberPipe, CurrencyPipe, DatePipe],
})
@MagicConfig({
  resolve: [planCardResolver],
})
export class PlanCardMagicComponent {
  private translate: TranslateService = inject(TranslateService);
  private phoneNumberPipe: PhoneNumberPipe = inject(PhoneNumberPipe);
  private datePipe: DatePipe = inject(DatePipe);
  private currencyPipe: CurrencyPipe = inject(CurrencyPipe);

  products = select(MyProductsState.products);
  billingAccountId = select(CurrentState.currentBillingAccountId);

  currentProduct = computed(() => this.products().getProduct(this.billingAccountId()));
  product = computed(() => this.products().getProduct(this.billingAccountId()));

  dataList = computed<DataList>(() => {
    const product = this.currentProduct().product;
    const productDetailList = this.currentProduct().productDetailList();
    const plan = productDetailList.plan;
    const productChars = new ProductCharListData(productDetailList.nonDeviceAndSecondaryProductChars);
    const discountList = this.currentProduct().getProductDetailByProductType(OfferInstanceProductTypeEnum.DISCOUNT);

    const subscriptionIdentityProductChars = productChars.subscriptionIdentityChars;
    const secondaryDisplayProductChars = productChars.secondaryDisplayChars;

    return {
      itemsSize: 5,
      trim: true,
      expandedText: this.translate.translate('showLess'),
      unexpandedText: this.translate.translate('showMore'),
      items: [
        {
          className: '',
          key: this.translate.translate('planName'),
          value: plan.name,
        },
        plan.installationAddress?.addressLabel && {
          className: '',
          key: this.translate.translate('serviceAddress'),
          value: plan.installationAddress?.addressLabel,
        },
        (product.endUserInformation?.fullName || product.endUserInformation?.firstName) && {
          className: '',
          key: this.translate.translate('endUser'),
          value: this.currentProduct().endUserName,
        },
        ...subscriptionIdentityProductChars.map((char) => ({
          className: '',
          key: char.productCharName,
          value: this.phoneNumberPipe.transform(char.productCharValue, {
            dash: false,
            bracketFormat: false,
          }),
        })),
        {
          className: '',
          key: this.translate.translate('status'),
          value: `<eds-tag part="status" content="${plan.status?.name}" appearance="${getColorByProductStatus(plan.status?.shortCode)}"></eds-tag>`,
        },
        {
          className: '',
          key: this.translate.translate('billingAccountId'),
          value: String(product.billingAccountId),
        },
        {
          className: '',
          key: this.translate.translate('firstActivationDate'),
          value: this.datePipe.transform(plan.firstActivationDate, 'medium'),
        },
        {
          className: '',
          key: this.translate.translate('validUntilDate'),
          value: this.datePipe.transform(plan.validUntilDate),
        },
        ...secondaryDisplayProductChars.map((char) => ({
          className: '',
          key: char.productCharName,
          value: char.productCharValueName,
        })),
        plan.commitment?.commitmentEndDate && {
          className: '',
          key: this.translate.translate('commitmentEndDate'),
          value: this.datePipe.transform(plan.commitment?.commitmentEndDate),
        },
        {
          className: '',
          key: this.translate.translate('price'),
          value: getProductDetailPrice(plan, this.currencyPipe, this.translate),
        },
        ...(discountList?.length
          ? discountList.flatMap((discount, index) => [
              {
                className: '',
                key: `${this.translate.translate('discount')} ${index + 1}`,
                value: discount.name,
              },
              {
                className: '',
                key: this.translate.translate('discountStartDate'),
                value: this.datePipe.transform(discount.startDate),
              },
              {
                className: '',
                key: this.translate.translate('discountEndDate'),
                value: discount.endDate ? this.datePipe.transform(discount.endDate) : '-',
              },
            ])
          : []),
      ].filter(Boolean),
    };
  });

  alert = computed<Alert>(() => {
    const plan = this.currentProduct().productDetailList().plan;

    if (!plan?.commitment?.commitmentEndDate) {
      return null;
    }

    return {
      title: '',
      description: this.translate.translate('commitmentPlanErrorMessage', {
        date: this.datePipe.transform(plan.commitment?.commitmentEndDate),
      }),
      appearance: 'error',
      iconName: 'informationCircle',
      showIcon: true,
    } as Alert;
  });

  tileItems = computed<Tile[]>(() => {
    const productDetailList = this.currentProduct().activeProductDetailList();
    const productChars = new ProductCharListData(productDetailList.nonPrimarySecondaryProductChars);
    const bucketProductChars = productChars.getByViewTypeShortCode(eProduct.ProductConsts.BUCKET);

    const iconMap: Record<string, string> = {
      [eProduct.ProductCharTypes.DATA_AMOUNT]: 'wifi',
      [eProduct.ProductCharTypes.VOICE_AMOUNT]: 'calling',
      [eProduct.ProductCharTypes.SMS_AMOUNT]: 'mail',
    };

    const getIconName = (shortCode: string): string => {
      if (iconMap[shortCode]) {
        return iconMap[shortCode];
      }
      if (shortCode.includes(eProduct.ProductCharTypes.DOWNLOAD_SPEED)) {
        return 'cloudDownload';
      }
      if (shortCode.includes(eProduct.ProductCharTypes.UPLOAD_SPEED)) {
        return 'cloudUpload';
      }
      if (shortCode.includes(eProduct.ProductCharTypes.SPEED)) {
        return 'timer';
      }
      return 'informationCircle';
    };

    return bucketProductChars.map((char) => ({
      iconName: getIconName(char.productCharShortCode),
      title: this.translate.translate(char?.productCharName),
      text: this.translate.translate(char?.productCharName + 'Value', {
        amount: char?.productCharValueName,
      }),
    }));
  });

  interactionsLevel = signal<eBusinessFlow.Levels>(eBusinessFlow.Levels.PRODUCT_DETAIL_PLAN_CARD);
}
