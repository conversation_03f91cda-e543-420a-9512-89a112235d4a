import { Component, CUSTOM_ELEMENTS_SCHEMA, inject } from '@angular/core';
import { TranslateService } from '@libs/plugins';
import { Cms, CmsPlanCardComponent as WidgetCmsPlanCardComponent } from '@libs/widgets';

@Component({
  selector: 'magic-widget-cms-plan-card',
  templateUrl: './cms-plan-card-magic.component.html',
  imports: [WidgetCmsPlanCardComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class CmsPlanCardMagicComponent {
  private translateService = inject(TranslateService);

  title = this.translateService.translate('mobilePostpaidBasicPlan');
  subTitle = this.translateService.translate('whatsIncluded');
  advantages: Cms.PlanAdvantages[] = [
    {
      icon: { value: 'internet', isIcon: true },
      title: this.translateService.translate('internetAmount', { amount: 5 }),
      description: this.translateService.translate('mainInternetTraffic'),
    },
    {
      icon: { value: 'calling', isIcon: true },
      title: this.translateService.translate('callingAmount', { amount: 100 }),
      description: this.translateService.translate('mainVoiceTraffic'),
    },
    {
      icon: { value: 'mail', isIcon: true },
      title: this.translateService.translate('smsAmount', { amount: 500 }),
      description: this.translateService.translate('smsTraffic'),
    },
  ];
  price: Cms.PlanPrice = {
    price: '$9,95',
    discount: 'mth 12,95',
    currency: 'USD',
    description: '30% discounted for 24 months commitment',
  };
  actions: Cms.PlanAction[] = [
    {
      text: this.translateService.translate('orderNow'),
      appearance: 'primary',
    },
    {
      text: this.translateService.translate('allDetails'),
      appearance: 'default',
    },
    {
      text: this.translateService.translate('buyWithDevices'),
      appearance: 'link',
    },
  ];
  tags: Cms.PlanTag[] = [
    {
      text: this.translateService.translate('justForYou'),
      appearance: 'blue',
    },
    {
      text: this.translateService.translate('limitedStock'),
      appearance: 'red',
    },
    {
      text: this.translateService.translate('discountAmount', { amount: 10 }),
      appearance: 'orange',
    },
  ];
}
