<eds-card [title]="'orderItems' | translate">
  <widget-checkout-card [title]="'mobile' | translate" icon="simCard" [isActive]="true" [itemAmount]="plans().length">
    <widget-seperated>
      @for (plan of plans(); track $index) {
        <widget-section padding="none">
          <widget-offer-plan-card
            [orderItemId]="plan?.customerOrderItemId"
            [offerName]="plan?.plan?.offerName"
            [phoneNumber]="plan.findMsisdnNumber()"
            [simType]="plan.getSimType()"
            [showDetails]="true"
            [isOrderSummary]="true"
          ></widget-offer-plan-card>
        </widget-section>

        @if (plan.device) {
          <widget-offer-device-card
            [offerName]="plan.device?.offerName"
            [chars]="plan.deviceChars"
          ></widget-offer-device-card>
        }

        <widget-titled-section [sectionTitle]="'deliveryInvoice' | translate">
          <magic-widget-delivery-invoice-summary-card
            [showDeliveryInfo]="!checkEsimOfferInstance(plan.plan.offerInstances)"
            [customerOrderItemId]="plan?.plan.customerOrderItemId"
          ></magic-widget-delivery-invoice-summary-card>
        </widget-titled-section>
      }
    </widget-seperated>
  </widget-checkout-card>
</eds-card>
