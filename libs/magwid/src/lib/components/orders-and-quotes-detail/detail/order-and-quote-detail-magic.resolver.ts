import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import { CurrentState, QuoteGetQuoteAction, QuoteState } from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';
import { Store } from '@ngxs/store';

export const orderAndQuoteDetailMagicResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);
  const customerOrderId = store.selectSnapshot(CurrentState.currentCustomerOrderId);

  return [
    {
      selector: store.selectSnapshot(QuoteState.quote)?.quote,
      action: () => {
        return new QuoteGetQuoteAction({
          customerOrderId,
        });
      },
    },
  ];
};
