<div class="base">
  @if (computedShowAlert()) {
    <eds-alert
      showIcon="true"
      iconName="informationCircle"
      description="With your consent, a prepaid amount based on equipment value and your credit score is required for monthly installment plans. This will reduce monthly fees, and your cart will update accordingly."
    >
    </eds-alert>
  } @else {
    <widget-payment-credit-check (selectionChange)="onSelectionChange($event)"></widget-payment-credit-check>
    <eds-button appearance="primary" shouldFitContainer [disabled]="disabled()" (button-click)="onSave()">
      {{ 'saveAndContinue' | translate }}
    </eds-button>
  }
</div>
