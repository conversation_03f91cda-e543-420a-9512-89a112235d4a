import {
  ChangeDetectionStrategy,
  Component,
  computed,
  CUSTOM_ELEMENTS_SCHEMA,
  inject,
  input,
  output,
  signal,
} from '@angular/core';
import { PaymentCreditCheckComponent } from '@libs/widgets';
import { TranslatePipe } from '@libs/plugins';
import { BusinessWorkflowStepService, QuoteService } from '@libs/bss';

@Component({
  selector: 'magic-widget-payment-credit-check',
  imports: [PaymentCreditCheckComponent, TranslatePipe],
  styleUrls: ['./payment-credit-check-magic.component.scss'],
  templateUrl: './payment-credit-check-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class PaymentCreditCheckMagicComponent {
  private quoteService = inject(QuoteService);
  private businessWorkflowStepService = inject(BusinessWorkflowStepService);

  showAlert = input<boolean>();

  creditCheck = output<boolean>();

  value = signal<string>('');

  creditChecked = signal<boolean>(false);

  computedShowAlert = computed(() => (this.showAlert() !== undefined ? this.showAlert() : this.creditChecked()));

  disabled = computed(() => this.value() === '');

  onSelectionChange(value: string) {
    this.value.set(value);
  }

  onSave() {
    this.quoteService.updateCreditCheck$(this.value()).subscribe(() => {
      this.businessWorkflowStepService.nextStep();
      this.creditCheck.emit(true);
      this.creditChecked.set(true);
    });
  }
}
