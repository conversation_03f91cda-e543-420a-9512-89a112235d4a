import { ResolveFn } from '@angular/router';
import { OfferGetCatalogAction, OfferState } from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';
import { eCommon } from '@libs/types';
import { Store } from '@ngxs/store';
import { inject } from '@angular/core';

export const EnterSimDetailsResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);

  return [
    {
      selector: store.selectSnapshot(OfferState.simCardOfferCatalog)?.offerCatalog?.length,
      action: () => {
        return new OfferGetCatalogAction(eCommon.CatalogShortCode.SIM_CARD);
      },
    },
  ];
};
