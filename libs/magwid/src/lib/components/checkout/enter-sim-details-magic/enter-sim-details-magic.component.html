<div class="sim-card-selection">
  @if (selectedSimMethod() === SIM_METHOD.HAVE_SIM) {
    <widget-enter-sim-details
      [validationStatus]="phoneValidationStatus()"
      (iccidChange)="onIccidChange($event)"
      [form]="simForm()"
    ></widget-enter-sim-details>
  }
  @if (selectedSimMethod() === SIM_METHOD.DONT_HAVE_SIM) {
    <widget-titled-section [sectionTitle]="'selectSimType' | translate">
      <eds-radio-group name="simType" (radio-change)="simTypeChange($event)">
        @for (simType of simCardOfferCatalogs(); track $index) {
          <eds-radio
            [id]="simType.id"
            [value]="simType.value"
            [name]="simType.name"
            [isChecked]="simType.isChecked"
            [isDisabled]="simType.isDisabled"
          >
            <label [for]="simType.id">{{ simType.label }}</label>
          </eds-radio>
        }
      </eds-radio-group>
    </widget-titled-section>
  }

  @if (validatedPhoneNumber()) {
    <magic-widget-new-number-magic [(phoneNumber)]="validatedPhoneNumber"></magic-widget-new-number-magic>
  }
</div>
