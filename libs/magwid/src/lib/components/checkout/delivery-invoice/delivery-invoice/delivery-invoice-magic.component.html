<div class="base">
  @for (plan of plans(); track $index) {
    @if ($index === 0) {
      @if (saveAndContinueClick()) {
        <magic-widget-delivery-invoice-summary-card
          [cardTitle]="plan.offerName"
          [customerOrderItemId]="plan.customerOrderItemId"
          (editClick)="edit()"
        >
        </magic-widget-delivery-invoice-summary-card>
        <eds-button shouldFitContainer appearance="primary" size="default" (button-click)="continueNextStep()"
          >{{ 'continue' | translate }}
        </eds-button>
      } @else {
        <widget-seperated>
          @if (deliveryVisible()) {
            <!--        Delivery  -->
            <widget-seperated>
              <widget-section padding="none">
                @if (plans().length > 1) {
                  <magic-widget-delivery-address-preference
                    (selectAddressPreference)="handleAddressPreference($event)"
                  ></magic-widget-delivery-address-preference>
                }
                <magic-widget-delivery-method-selection
                  [currentDeliveryMethodId]="deliveryMethodId()"
                  (selectDeliveryMethod)="handleDeliveryMethod($event)"
                ></magic-widget-delivery-method-selection>
                @if (defaultDeliveryAddressVisible() && deliveryMethodId()) {
                  <magic-widget-delivery-address-selection
                    [currentAddress]="deliveryAddress()"
                    (selectAddress)="handleDeliveryAddress($event)"
                  ></magic-widget-delivery-address-selection>

                  @if (deliveryAddress()?.id) {
                    <magic-widget-delivery-option-selection
                      [currentDeliveryMethodId]="deliveryMethodId()"
                      [currentDeliveryOptionId]="deliveryOptionId()"
                      (selectDeliveryOption)="handleDeliveryOption($event)"
                    ></magic-widget-delivery-option-selection>
                  }
                }
              </widget-section>
            </widget-seperated>
          }
          @if (postpaidBundleOffers().length > 0) {
            @if (invoiceVisible()) {
              <magic-widget-invoice-address-selection
                [currentAddress]="invoiceAddress()"
                (selectAddress)="handleInvoiceAddress($event)"
                (selectInvoicePreference)="handleInvoicePreference($event)"
                (isExistingInvoiceAddress)="handleIsExistingInvoiceAddress($event)"
              ></magic-widget-invoice-address-selection>
            } @else if (billingAddressIsShown()) {
              <magic-widget-billing-address-selection
                [currentAddress]="billingAddress()"
                (selectAddress)="handleBillingAddress($event)"
              ></magic-widget-billing-address-selection>
            }
          } @else {
            @if (billingAddressIsShown()) {
              <magic-widget-billing-address-selection
                [currentAddress]="billingAddress()"
                (selectAddress)="handleBillingAddress($event)"
              ></magic-widget-billing-address-selection>
            }
          }
        </widget-seperated>
        @if (plans().length > 1) {
          <magic-widget-delivery-basket></magic-widget-delivery-basket>
        }
        <eds-button
          [disabled]="continueButtonDisabled()"
          shouldFitContainer
          appearance="primary"
          size="default"
          (button-click)="saveAndContinue()"
        >
          {{ 'saveAndContinue' | translate }}
        </eds-button>
      }
    }
  }
</div>
