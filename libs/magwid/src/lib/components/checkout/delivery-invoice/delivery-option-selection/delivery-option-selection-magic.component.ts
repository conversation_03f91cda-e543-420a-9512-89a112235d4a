import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';
import { QuoteState } from '@libs/bss';
import { TranslatePipe } from '@libs/plugins';
import { SelectDeliveryOptionsComponent } from '@libs/widgets';
import { select } from '@ngxs/store';

@Component({
  selector: 'magic-widget-delivery-option-selection',
  templateUrl: './delivery-option-selection-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [TranslatePipe, SelectDeliveryOptionsComponent],
})
export class DeliveryOptionSelectionMagicComponent {
  currentDeliveryMethodId = input<number>(null);
  currentDeliveryOptionId = input<number>(null);

  selectDeliveryOption = output<number>();

  deliveryOptions = select(QuoteState.getOfferDeliverInstallationRetrieveConfig);

  mappedDeliveryOptions = computed(() => {
    return this.deliveryOptions().deliveryOptions(this.currentDeliveryMethodId(), this.currentDeliveryOptionId());
  });
}
