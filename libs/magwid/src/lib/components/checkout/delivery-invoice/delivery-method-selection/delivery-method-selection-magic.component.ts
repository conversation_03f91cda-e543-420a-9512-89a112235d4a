import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';
import { select } from '@ngxs/store';
import { LovState } from '@libs/bss';
import { MagicConfig, TranslatePipe } from '@libs/plugins';
import { SelectDeliveryMethodComponent } from '@libs/widgets';
import { deliveryMethodSelectionResolver } from './delivery-method-selection.resolver';

@Component({
  selector: 'magic-widget-delivery-method-selection',
  templateUrl: './delivery-method-selection-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [SelectDeliveryMethodComponent, TranslatePipe],
})
@MagicConfig({
  resolve: [deliveryMethodSelectionResolver],
})
export class DeliveryMethodSelectionMagicComponent {
  currentDeliveryMethodId = input<number>(null);
  selectDeliveryMethod = output<number>();

  deliveryMethods = select(LovState.deliveryMethods);

  mappedDeliveryMethods = computed(() => {
    return this.deliveryMethods().getMethodOptions(this.currentDeliveryMethodId());
  });
}
