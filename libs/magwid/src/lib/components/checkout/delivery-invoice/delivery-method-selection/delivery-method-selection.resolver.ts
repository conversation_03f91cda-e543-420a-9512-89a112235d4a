import { ResolveFn } from '@angular/router';
import { LovGetDeliveryMethodsAction, LovState } from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';
import { inject } from '@angular/core';
import { Store } from '@ngxs/store';

export const deliveryMethodSelectionResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);

  return [
    {
      selector: store.selectSnapshot(LovState.deliveryMethods).items,
      action: () => {
        return new LovGetDeliveryMethodsAction();
      },
    },
  ];
};
