import { ResolveFn } from '@angular/router';
import { CurrentState, CustomerGetAddressListAction, CustomerState } from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';
import { inject } from '@angular/core';
import { Store } from '@ngxs/store';

export const deliveryAddressSelectionResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);

  return [
    {
      selector: store.selectSnapshot(CustomerState.getAddressList).items,
      action: () => {
        return new CustomerGetAddressListAction(store.selectSnapshot(CurrentState.customerId));
      },
    },
  ];
};
