import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { QuoteState } from '@libs/bss';
import { MagicConfig } from '@libs/plugins';
import { DeliveryBasketComponent } from '@libs/widgets';
import { select } from '@ngxs/store';

@Component({
  selector: 'magic-widget-delivery-basket',
  templateUrl: './delivery-basket-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [DeliveryBasketComponent],
})
@MagicConfig({
  resolve: [],
})
export class DeliveryBasketMagicComponent {
  quote = select(QuoteState.quote);

  basketItems = computed(() => {
    return this.quote()?.rawPlans.map(
      (plan) => plan.plan.offerName + (plan?.device ? ` + ${plan?.device.offerName}` : ''),
    );
  });
}
