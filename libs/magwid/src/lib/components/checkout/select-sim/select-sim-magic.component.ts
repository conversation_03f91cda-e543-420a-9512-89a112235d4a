import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  effect,
  input,
  model,
  signal,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { TranslatePipe } from '@libs/plugins';
import { HaveSimCardEnum } from '@libs/types';
import { Radio, TitledSectionComponent } from '@libs/widgets';
import { EnterSimDetailsMagicComponent } from '../enter-sim-details-magic/enter-sim-details-magic.component';
import { PlanData } from '@libs/bss';

@Component({
  selector: 'magic-widget-select-sim',
  imports: [TitledSectionComponent, FormsModule, EnterSimDetailsMagicComponent, TranslatePipe],
  templateUrl: './select-sim-magic.component.html',
  styleUrl: './select-sim-magic.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class SelectSimMagicComponent {
  activePlan = input<PlanData>(null);
  selectionOptions = input<Radio[]>([]);

  validatedNumber = model<string>(null);
  validatedSimType = model<number>(null);

  selectedSimSelection = signal<HaveSimCardEnum>(null);

  constructor() {
    effect(() => {
      const selected = this.selectionOptions().find((item) => item.isChecked);
      if (!selected) {
        return;
      }
      this.selectedSimSelection.set(selected?.value as HaveSimCardEnum);
      this.validatedSimType.set(selected?.data as number);
    });
  }

  valueChange($event: Event) {
    const eventTargetValue = ($event.target as HTMLInputElement)?.value;
    if (!eventTargetValue) {
      return;
    }

    const selectedRadio = this.selectionOptions().find((radio) => radio.value === eventTargetValue);

    if (selectedRadio && this.selectedSimSelection() !== selectedRadio.value) {
      selectedRadio.data = null;
      this.validatedNumber.set(null);
      this.selectedSimSelection.set(selectedRadio.value as HaveSimCardEnum);
      this.validatedSimType.set(null);
    }
  }
}
