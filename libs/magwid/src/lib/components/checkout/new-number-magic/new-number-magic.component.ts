import { ChangeDetectionStrategy, Component, inject, model } from '@angular/core';
import { ChangeableInfoCardComponent } from '@libs/widgets';
import { ModalService, ModalSize } from '@libs/plugins';
import { ChangeMagicNumberComponent } from '@libs/magwid';
import { PhoneNumberPipe } from '@libs/core';

@Component({
  selector: 'magic-widget-new-number-magic',
  imports: [ChangeableInfoCardComponent, PhoneNumberPipe],
  templateUrl: './new-number-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NewNumberMagicComponent {
  protected modalService = inject(ModalService);

  phoneNumber = model<string>();

  onChange() {
    const modalRef = this.modalService.open(ChangeMagicNumberComponent, {
      title: 'Change number',
      size: ModalSize.MEDIUM,
      data: {
        save: (number: string) => {
          modalRef.close();
          this.phoneNumber.set(number);
        },
      },
    });
  }
}
