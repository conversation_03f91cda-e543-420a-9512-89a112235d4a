import {
  ChangeDetectionStrategy,
  Component,
  computed,
  CUSTOM_ELEMENTS_SCHEMA,
  effect,
  inject,
  signal,
} from '@angular/core';
import { BusinessWorkflowStepService, CurrentState, PlanData, QuoteService, QuoteState } from '@libs/bss';
import { MagicConfig, TranslatePipe } from '@libs/plugins';
import {
  AddActivationOffer,
  eBusinessFlow,
  eCommon,
  HaveSimCardEnum,
  OfferInstanceKeyEnum,
  OrderCharType,
  UpdateOfferInstanceQuoteChar,
} from '@libs/types';
import { CheckoutCardComponent, Radio, SectionComponent, SeperatedComponent } from '@libs/widgets';
import { select, Store } from '@ngxs/store';
import { switchMap } from 'rxjs';
import { OfferDeviceCardMagicComponent } from '../../offer-device-card/offer-device-card-magic.component';
import { OfferPlanCardMagicComponent } from '../../offer-plan-card/offer-plan-card-magic.component';
import { SelectSimMagicComponent } from '../select-sim/select-sim-magic.component';
import { SimSummaryCardMagicComponent } from '../sim-summary-card/sim-summary-card-magic.component';
import { SimConfigurationResolver } from './sim-configuration.resolver';

@Component({
  selector: 'magic-widget-sim-configuration',
  imports: [
    CheckoutCardComponent,
    SelectSimMagicComponent,
    SimSummaryCardMagicComponent,
    TranslatePipe,
    SectionComponent,
    SeperatedComponent,
    OfferPlanCardMagicComponent,
    OfferDeviceCardMagicComponent,
  ],
  templateUrl: './sim-configuration-magic.component.html',
  styleUrls: ['./sim-configuration-magic.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
@MagicConfig({
  resolve: [SimConfigurationResolver],
})
export class SimConfigurationMagicComponent {
  private store = inject(Store);
  private quoteService = inject(QuoteService);
  private businessWorkflowStepService = inject(BusinessWorkflowStepService);

  quote = select(QuoteState.quote);

  validatedNumber = signal<string>(null);
  validatedSimOffer = signal<number>(null);
  activeIndex = signal(0);

  plans = computed(() => {
    return this.quote()?.rawPlans;
  });

  activePlan = computed(() => {
    return this.quote()?.rawPlans[this.activeIndex()];
  });

  completed = computed(() => {
    return this.activeIndex() >= this.plans().length;
  });

  simCardSelectionChars = computed<Radio[]>(() => {
    const physicalSimOfferId = this.activePlan()
      .planOfferInstance(OfferInstanceKeyEnum.PHYSICAL_SIM)
      .find(Boolean)?.offerId;
    const eSimOfferId = this.activePlan().planOfferInstance(OfferInstanceKeyEnum.E_SIM).find(Boolean)?.offerId;
    const customerSimOfferId = this.activePlan()
      .planOfferInstance(OfferInstanceKeyEnum.BYOD_SIM)
      .find(Boolean)?.offerId;

    const simTypes = {
      physical: !!physicalSimOfferId,
      eSim: !!eSimOfferId,
      customer: !!customerSimOfferId,
    };

    const createRadioOption = (type: HaveSimCardEnum, isChecked: boolean, data: number): Radio => ({
      id: type,
      value: type,
      name: type,
      label: type === HaveSimCardEnum.HAVE_SIM ? 'iHaveSimCard' : 'iDontHaveSimCard',
      isChecked,
      isDisabled: type === HaveSimCardEnum.HAVE_SIM, //todo havesim disabled for now
      data,
    });

    return [
      createRadioOption(HaveSimCardEnum.HAVE_SIM, simTypes.customer, customerSimOfferId),
      createRadioOption(
        HaveSimCardEnum.DONT_HAVE_SIM,
        simTypes.physical || simTypes.eSim || true, // TODO default for now
        physicalSimOfferId || eSimOfferId,
      ),
    ];
  });

  existingMsisdnNumber = computed(() => this.activePlan()?.findMsisdnNumber());

  customerOrderId = signal(this.store.selectSnapshot(CurrentState.currentCustomerOrderId));

  updateQuoteChars = new Map<string | number, UpdateOfferInstanceQuoteChar>();
  activationOffers = new Map<string | number, AddActivationOffer>();
  removeQuoteItemIds = new Map<string | number, number>();

  constructor() {
    effect(() => {});
  }

  next() {
    this.activeIndex.set(this.activeIndex() + 1);
    this.validatedNumber.set(null);
  }

  onContinue() {
    this.businessWorkflowStepService.nextStep();
  }

  saveAndContinue(isLast: boolean) {
    const customerOrderItemId = this.activePlan().customerOrderItemId;
    const updateQuoteChars: UpdateOfferInstanceQuoteChar = {
      customerOrderItemId: this.activePlan().msisdnOffer().customerOrderItemId,
      shortCode: eCommon.WellKnownChars.MSISDN,
      value: this.validatedNumber(),
      charTypes: [OrderCharType.OFFER],
    };

    const instantCharValues =
      this.store.selectSnapshot(QuoteState.validateSimCardResponse)?.data?.characteristic?.map((item) => ({
        shortCode: item.name,
        value: item.value,
        charType: OrderCharType.OFFER,
      })) || [];

    const activationOffers: AddActivationOffer = {
      productOfferId: this.validatedSimOffer(),
      relatedCustomerOrderItemIdList: [customerOrderItemId],
      instantCharValues: instantCharValues,
    };
    this.updateQuoteChars.set(this.activePlan().msisdnOffer().customerOrderItemId, updateQuoteChars);
    this.activationOffers.set(customerOrderItemId, activationOffers);

    if (this.activePlan()?.simCardCustomerOrderItemIds) {
      this.removeQuoteItemIds.set(customerOrderItemId, this.activePlan().simCardCustomerOrderItemIds);
    }

    if (!isLast) {
      return this.next();
    }

    if (
      this.validatedNumber() !== this.existingMsisdnNumber() ||
      this.validatedSimOffer() !== this.activePlan().findSimCardOfferId()
    ) {
      this.quoteService
        .updateMsisdn$(
          [...this.updateQuoteChars.values()],
          [...this.activationOffers.values()],
          isLast ? undefined : eBusinessFlow.WorkflowStateType.PRODUCT_CONF,
          [...this.removeQuoteItemIds.values()],
        )
        .pipe(switchMap(() => this.quoteService.inquireQuote(eBusinessFlow.WorkflowStateType.BILLING_ADDRESS)))
        .subscribe(() => {
          this.quoteService.getQuotePriceDetail();
          this.next();
          if (this.plans().length == 1) {
            this.onContinue();
          }
        });
    } else {
      this.next();
    }
  }

  onEdit(plan: PlanData) {
    const planIndex = this.quote()?.rawPlans.findIndex((item) => item.customerOrderItemId === plan.customerOrderItemId);
    this.activeIndex.set(planIndex);
  }
}
