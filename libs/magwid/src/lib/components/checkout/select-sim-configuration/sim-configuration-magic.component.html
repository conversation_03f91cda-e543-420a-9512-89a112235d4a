<widget-section>
  <widget-checkout-card title="Mobile" icon="simCard" [itemAmount]="plans()?.length" [isActive]="true">
    <widget-seperated>
      @for (plan of plans(); track $index) {
        <widget-section padding="none">
          @if ($index === activeIndex()) {
            <magic-widget-offer-plan-card
              [showDelete]="false"
              [planCustomerOrderItemId]="plan.customerOrderItemId"
            ></magic-widget-offer-plan-card>
            @if (plan.device) {
              <magic-widget-offer-device-card
                [planCustomerOrderItemId]="plan.customerOrderItemId"
              ></magic-widget-offer-device-card>
            }

            <magic-widget-select-sim
              [activePlan]="activePlan()"
              [selectionOptions]="simCardSelectionChars()"
              [(validatedNumber)]="validatedNumber"
              [(validatedSimType)]="validatedSimOffer"
            ></magic-widget-select-sim>

            <eds-button
              [disabled]="!validatedNumber()"
              shouldFitContainer
              appearance="primary"
              size="default"
              (button-click)="saveAndContinue($index === plans().length - 1)"
            >
              {{ 'saveAndContinue' | translate }}
            </eds-button>
          } @else {
            <!--      <widget-offer-plan-card [plan]="plan" [showDetails]="false"></widget-offer-plan-card>-->
            <magic-widget-sim-summary-card
              [orderItemId]="plan?.plan.customerOrderItemId"
              [completed]="$index < activeIndex()"
              [isDone]="true"
              (edit)="onEdit($event)"
            ></magic-widget-sim-summary-card>
          }
        </widget-section>
      }
    </widget-seperated>
  </widget-checkout-card>
  @if (completed()) {
    <eds-button shouldFitContainer appearance="primary" size="default" (button-click)="onContinue()"
      >{{ 'continue' | translate }}
    </eds-button>
  }
</widget-section>
