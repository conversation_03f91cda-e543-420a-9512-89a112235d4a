import { ResolveFn } from '@angular/router';
import { MagicResolverModel } from '@libs/plugins';
import {
  CurrentState,
  PaymentGetCustomerPaymentMethodsAction,
  PaymentGetPaymentMethodTypesAction,
  PaymentState,
} from '@libs/bss';
import { inject } from '@angular/core';
import { Store } from '@ngxs/store';
import { eBusinessFlow } from '@libs/types';

export const paymentMethodResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);
  const customerId = store.selectSnapshot(CurrentState.customerId);

  return [
    {
      selector: store.selectSnapshot(PaymentState.customerPaymentMethods)?.paymentMethods?.length > 0,
      action: () => {
        return new PaymentGetCustomerPaymentMethodsAction(customerId);
      },
    },
    {
      selector: store.selectSnapshot(PaymentState.paymentMethodTypes)?.length > 0,
      action: () => {
        return new PaymentGetPaymentMethodTypesAction(eBusinessFlow.Specification.REAL_SALE);
      },
    },
  ];
};
