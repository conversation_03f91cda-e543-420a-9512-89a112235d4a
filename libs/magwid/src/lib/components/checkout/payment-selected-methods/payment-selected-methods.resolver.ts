import { ResolveFn } from '@angular/router';
import { MagicResolverModel } from '@libs/plugins';
import {
  CurrentState,
  PaymentGetCustomerPaymentMethodsAction,
  PaymentState,
  QuoteGetQuoteAction,
  QuoteState,
} from '@libs/bss';
import { inject } from '@angular/core';
import { Store } from '@ngxs/store';
import { eBusinessFlow } from '@libs/types';

export const paymentSelectedMethodResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);
  const customerId = store.selectSnapshot(CurrentState.customerId);

  return [
    {
      selector: store.selectSnapshot(PaymentState.customerPaymentMethods)?.paymentMethods?.length > 0,
      action: () => {
        return new PaymentGetCustomerPaymentMethodsAction(customerId);
      },
    },
    {
      selector: store.selectSnapshot(QuoteState.quote)?.paymentReference,
      action: () => {
        return new QuoteGetQuoteAction({
          customerOrderId: store.selectSnapshot(CurrentState.currentCustomerOrderId),
          currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.PAYMENT_BILLING,
        });
      },
    },
  ];
};
