import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { MagicConfig } from '@libs/plugins';
import {
  PaymentMethodSelectionData,
  PaymentMethodSelectionTypes,
  PaymentSelectedMethodsComponent,
} from '@libs/widgets';
import { paymentSelectedMethodResolver } from './payment-selected-methods.resolver';
import { select } from '@ngxs/store';
import { PaymentState, QuoteState } from '@libs/bss';

@Component({
  selector: 'magic-widget-payment-selected-methods',
  templateUrl: './payment-selected-methods-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [PaymentSelectedMethodsComponent],
})
@MagicConfig({
  resolve: [paymentSelectedMethodResolver],
})
export class PaymentSelectedMethodsMagicComponent {
  quote = select(QuoteState.quote);
  paymentMethods = select(PaymentState.customerPaymentMethods);
  isSelected = input<boolean>(false);

  selectedPaymentMethods = computed<Record<PaymentMethodSelectionTypes, PaymentMethodSelectionData>>(() => {
    const paymentMethod = this.paymentMethods()?.paymentMethods.find(
      (paymentMethod) => paymentMethod.paymentMethodId === Number(this.quote()?.paymentReference?.rowId),
    );

    return {
      [PaymentMethodSelectionTypes.AUTHORIZED]: paymentMethod?.paymentMethodId
        ? {
            type: paymentMethod?.paymentMethodType,
            id: paymentMethod?.paymentMethodId,
            data: paymentMethod,
          }
        : undefined,
      [PaymentMethodSelectionTypes.PAY_NOW]: undefined,
    };
  });
}
