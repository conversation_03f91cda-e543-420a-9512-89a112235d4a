import { ChangeDetectionStrategy, Component, computed, inject, input, output } from '@angular/core';
import { PlanData, QuoteState } from '@libs/bss';
import { MagicConfig } from '@libs/plugins';
import { SimSummaryCardComponent } from '@libs/widgets';
import { select, Store } from '@ngxs/store';
import { CurrentOrderResolver } from '../../../resolvers/current-order.resolver';
import { OfferDeviceCardMagicComponent } from '../../offer-device-card/offer-device-card-magic.component';
import { OfferPlanCardMagicComponent } from '../../offer-plan-card/offer-plan-card-magic.component';

@Component({
  selector: 'magic-widget-sim-summary-card',
  imports: [SimSummaryCardComponent, OfferPlanCardMagicComponent, OfferDeviceCardMagicComponent],
  templateUrl: './sim-summary-card-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
@MagicConfig({
  resolve: [CurrentOrderResolver],
})
export class SimSummaryCardMagicComponent {
  store = inject(Store);

  orderItemId = input<number>();
  completed = input<boolean>(false);
  isDone = input<boolean>(false);
  isOrderSummary = input(false);

  edit = output<PlanData>();

  quote = select(QuoteState.quote);

  plan = computed(() => {
    return this.quote()?.findPlan(this.orderItemId());
  });

  isPlanDone = computed(() => {
    return !!this.plan().findMsisdnNumber();
  });
}
