import { ChangeDetectionStrategy, Component, computed, effect, inject, signal } from '@angular/core';
import { PaymentMethodMagicComponent } from '../payment-method/payment-method-magic.component';
import { PaymentCreditCheckMagicComponent } from '../payment-credit-check/payment-credit-check-magic.component';
import { MagicConfig, TranslateService } from '@libs/plugins';
import { StepperComponent, Stepper } from '@libs/widgets';
import { checkoutPaymentResolver } from './checkout-payment.resolver';
import { BusinessWorkflowStepService } from '@libs/bss';
import { eBusinessFlow } from '@libs/types';

@Component({
  selector: 'magic-widget-checkout-payment',
  templateUrl: './checkout-payment-magic.component.html',
  styleUrls: ['./checkout-payment-magic.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [StepperComponent, PaymentMethodMagicComponent, PaymentCreditCheckMagicComponent],
})
@MagicConfig({
  resolve: [checkoutPaymentResolver],
})
export class CheckoutPaymentMagicComponent {
  private translateService = inject(TranslateService);
  businessWorkflowStepService = inject(BusinessWorkflowStepService);

  creditCheck = signal<boolean>(false);
  paymentMethod = signal<boolean>(false);

  creditCheckStep = signal<Stepper>({
    title: this.translateService.translate('creditCheck'),
    isActive: true,
    inline: true,
  });

  paymentMethodStep = signal<Stepper>({
    title: this.translateService.translate('paymentMethod'),
    isActive: true,
    inline: true,
  });

  creditCheckVisible = computed(() => this.businessWorkflowStepService.hasStep(this.ShortCode.CREDIT_CHECK));

  ShortCode = eBusinessFlow.WorkflowStateType;

  constructor() {
    effect(() => {
      this.creditCheckStep.update((value) => ({ ...value, isCompleted: this.creditCheck() }));
    });
    effect(() => {
      this.paymentMethodStep.update((value) => ({ ...value, isCompleted: this.paymentMethod() }));
    });
  }

  editCreditCheckStep() {
    this.businessWorkflowStepService.previousStep();
    this.creditCheckStep.update((value) => ({ ...value, isCompleted: false }));
    this.creditCheck.set(false);
  }

  editpaymentMethodStep() {
    this.paymentMethodStep.update((value) => ({ ...value, isCompleted: false }));
    this.paymentMethod.set(false);
  }
}
