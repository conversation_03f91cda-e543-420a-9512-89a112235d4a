import { ResolveFn } from '@angular/router';
import { CurrentState, QuoteGetQuoteAction } from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';
import { eBusinessFlow } from '@libs/types';
import { Store } from '@ngxs/store';
import { inject } from '@angular/core';

export const checkoutPaymentResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);

  return [
    {
      selector: false,
      action: () => {
        return new QuoteGetQuoteAction({
          customerOrderId: store.selectSnapshot(CurrentState.currentCustomerOrderId),
          currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.PAYMENT_BILLING,
        });
      },
    },
  ];
};
