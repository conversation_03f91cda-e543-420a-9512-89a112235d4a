import { ResolveFn } from '@angular/router';
import { MagicResolverModel } from '@libs/plugins';
import { LovGetCountryTypesAction, LovState } from '@libs/bss';
import { inject } from '@angular/core';
import { Store } from '@ngxs/store';

export const manageAddressModalResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);

  return [
    {
      selector: store.selectSnapshot(LovState.countryType)?.countryTypes?.length,
      action: () => {
        return new LovGetCountryTypesAction();
      },
    },
  ];
};
