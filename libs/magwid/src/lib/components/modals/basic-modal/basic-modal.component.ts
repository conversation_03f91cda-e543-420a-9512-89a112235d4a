import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, inject, input, output } from '@angular/core';
import { ModalRef, TranslateService } from '@libs/plugins';

@Component({
  selector: 'magic-widget-basic-modal',
  templateUrl: './basic-modal.component.html',
  styleUrls: ['./basic-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class BasicModalComponent {
  modalRef = inject(ModalRef);
  translateService = inject(TranslateService);

  description = input<string>();
  cancelText = input<string>(this.translateService.translate('cancel'));
  confirmText = input<string>(this.translateService.translate('yesDelete'));

  onCancel = output<void>();
  onConfirm = output<void>();

  close(): void {
    this.modalRef.close();
  }

  cancel(): void {
    this.onCancel.emit();
    this.close();
  }

  confirm(): void {
    this.onConfirm.emit();
    this.close();
  }
}
