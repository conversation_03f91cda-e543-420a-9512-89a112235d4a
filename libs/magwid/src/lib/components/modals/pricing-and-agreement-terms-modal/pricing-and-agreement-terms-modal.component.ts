import {
  ChangeDetectionStrategy,
  Component,
  computed,
  CUSTOM_ELEMENTS_SCHEMA,
  inject,
  input,
  OnInit,
  output,
} from '@angular/core';
import { ModalRef, TranslateService } from '@libs/plugins';
import { Terms } from '@libs/types';
import { GetOfferTerms, TermState } from '@libs/bss';
import { select, Store } from '@ngxs/store';
import { SectionComponent } from '@libs/widgets';

@Component({
  selector: 'magic-widget-pricing-and-agreement-terms-modal',
  templateUrl: './pricing-and-agreement-terms-modal.component.html',
  styleUrls: ['./pricing-and-agreement-terms-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [SectionComponent],
})
export class PricingAndAgreementTermsModalComponent implements OnInit {
  modalRef = inject(ModalRef);
  store = inject(Store);
  translateService = inject(TranslateService);

  productOfferId = input<number>();
  productDeviceId = input<number>();

  cancelText = input<string>('');
  confirmText = input<string>(this.translateService.translate('ok'));

  onCancel = output<void>();
  onConfirm = output<void>();

  terms = select(TermState.terms);

  termBody = computed(() => {
    const currentLang = this.translateService.getActiveLang();
    return (
      this.terms()
        .flatMap((term) => term.multiLanguageData.filter((item) => item.language === currentLang))
        .map((item) => item.tmplBody)
        .find(Boolean) || ''
    );
  });

  ngOnInit() {
    this.callGetOfferTerms();
  }

  callGetOfferTerms() {
    const newReq = {
      keyProductOffers: [{ productOfferId: this.productOfferId() }, { productDeviceId: this.productDeviceId() }],
    } as Terms.OfferTermsRequest;

    this.store.dispatch(new GetOfferTerms(newReq));
  }

  close(): void {
    this.modalRef.close();
  }

  cancel(): void {
    this.onCancel.emit();
    this.close();
  }

  confirm(): void {
    this.onConfirm.emit();
    this.close();
  }
}
