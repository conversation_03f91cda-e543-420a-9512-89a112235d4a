<div class="base">
  <div class="body">
    <widget-manage-phone-number-form
      *ngIf="type() === ContactMediumTypeGroupCode.PHONE"
      [phoneNumberForm]="phoneNumberForm"
      [phoneTypeOptions]="phoneTypeOptions()"
      [countryOptionsForPhoneNumber]="countryOptions()"
      [isPrimaryLocked]="isPrimaryLocked()"
      [privacyList]="getInitialPrivacyList()"
      [isEdit]="isEdit()"
      (phoneTypeSelected)="onPhoneTypeSelected($event)"
      (privacyListChange)="onPhonePrivacyListChange($event)"
    ></widget-manage-phone-number-form>
    <widget-manage-email-form
      *ngIf="type() === ContactMediumTypeGroupCode.EMAIL"
      [emailForm]="emailForm"
      [privacyList]="getInitialPrivacyList()"
      [isPrimaryLocked]="isPrimaryLocked()"
      (privacyListChange)="onEmailPrivacyListChange($event)"
    ></widget-manage-email-form>
  </div>

  <div class="actions">
    @if (isEdit()) {
      <eds-button [appearance]="'default'" (button-click)="onClose()">
        {{ 'cancel' | translate }}
      </eds-button>
      <eds-button [appearance]="'primary'" (button-click)="submit()">
        {{ 'save' | translate }}
      </eds-button>
    } @else {
      <eds-button [appearance]="'primary'" (button-click)="submit()">
        {{ 'add' | translate }}
      </eds-button>
    }
  </div>
</div>
