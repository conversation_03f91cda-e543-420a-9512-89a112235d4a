import { Component, computed, inject } from '@angular/core';
import { select } from '@ngxs/store';
import { PageHeadingComponent } from '@libs/widgets';
import { CurrentState, MyProductsState } from '@libs/bss';
import { TranslateService } from '@libs/plugins';
import { CurrencyPipe } from '@angular/common';

@Component({
  selector: 'magic-widget-product-detail-greeting',
  templateUrl: './product-detail-greeting-magic.component.html',
  imports: [PageHeadingComponent],
  providers: [CurrencyPipe],
})
export class ProductDetailGreetingMagicComponent {
  private currencyPipe = inject(CurrencyPipe);
  private translateService = inject(TranslateService);

  products = select(MyProductsState.products);
  billingAccountId = select(CurrentState.currentBillingAccountId);

  plan = computed(() => this.products().getProductDetailListByBillingAccountId(this.billingAccountId())?.plan);
  priceText = computed(() => {
    return `${this.currencyPipe.transform(this.plan().discountAppliedCalculatedPriceValue || this.plan().calculatedPriceValue)} / ${this.translateService.translate('month')}`;
  });
}
