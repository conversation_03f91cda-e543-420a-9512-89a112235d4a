import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject } from '@angular/core';
import { TranslatePipe } from '@libs/plugins';
import { select, Store } from '@ngxs/store';
import { CmsState, QuoteState } from '@libs/bss';
import { OfferPlanCardMagicComponent } from '../../offer-plan-card/offer-plan-card-magic.component';
import { OfferPricingAndAgreementTermsMagicComponent } from '../../offer-pricing-and-agreement-terms/offer-pricing-and-agreement-terms-magic.component';
import { PlanChangeCartSummaryComponent } from '@libs/widgets';
import { CustomerOrderItemActionTypeEnum } from '@libs/types';

@Component({
  selector: 'magic-widget-plan-change-cart-summary',
  templateUrl: './cart-summary-magic.component.html',
  styleUrls: ['./cart-summary-magic.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    OfferPlanCardMagicComponent,
    OfferPricingAndAgreementTermsMagicComponent,
    TranslatePipe,
    PlanChangeCartSummaryComponent,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class PlanChangeCartSummaryMagicComponent {
  store = inject(Store);

  quote = select(QuoteState.quote);
  cmsOffers = select(CmsState.offers);

  date = 'Immediate';

  effectiveDateOptions = [
    { label: 'Immediate', value: 'immediate', isSelected: true, isDisabled: false },
    { label: 'Next Billing Date', value: 'nextBillingDate', isSelected: false, isDisabled: false },
  ];

  onDateChanged(newDate: string) {
    this.date = newDate;
    this.effectiveDateOptions = this.effectiveDateOptions.map((option) => ({
      ...option,
      isSelected: option.label === newDate,
    }));
  }

  currentPlan = computed(() => {
    const quote = this.quote();
    const deactivatedOffer = quote?.bundleOffers?.find(
      (offer) => offer.actionCode === CustomerOrderItemActionTypeEnum.DEACTIVATION,
    );
    return deactivatedOffer?.customerOrderItemId || null;
  });

  newPlan = computed(() => {
    const quote = this.quote();
    const activatedOffer = quote?.bundleOffers?.find(
      (offer) => offer.actionCode === CustomerOrderItemActionTypeEnum.ACTIVATION,
    );
    return activatedOffer?.customerOrderItemId || null;
  });

  getPlanTags(planCustomerOrderItemId: number) {
    const plan = this.quote()?.findPlan(planCustomerOrderItemId);
    const planOfferId = plan?.plan?.offerId?.toString();
    const cmsOffer = this.cmsOffers().getCmsOfferById(planOfferId);
    if (!plan || !planOfferId) {
      return [];
    }

    return cmsOffer?.marketing_tags || [];
  }
}
