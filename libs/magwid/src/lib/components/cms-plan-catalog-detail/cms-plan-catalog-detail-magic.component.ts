import { Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject, input } from '@angular/core';
import { BusinessFlowInteractionService, CmsState } from '@libs/bss';
import {
  Cms,
  CmsPlanCatalogDetailComponent as WidgetCmsPlanCatalogDetailComponent,
  LayoutContentTopNavComponent,
  PageHeadingComponent,
} from '@libs/widgets';
import { select } from '@ngxs/store';
import { cmsPlanCatalogDetailResolver } from './cms-plan-catalog-detail.resolver';
import { MagicConfig, TranslateService } from '@libs/plugins';
import { Location } from '@angular/common';
import { Router } from '@angular/router';

@Component({
  selector: 'magic-widget-cms-plan-catalog-detail',
  templateUrl: './cms-plan-catalog-detail-magic.component.html',
  styleUrls: ['./cms-plan-catalog-detail-magic.component.scss'],
  imports: [WidgetCmsPlanCatalogDetailComponent, LayoutContentTopNavComponent, PageHeadingComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
@MagicConfig({
  resolve: [cmsPlanCatalogDetailResolver],
})
export class CmsPlanCatalogDetailMagicComponent {
  private router = inject(Router);
  private location = inject(Location);
  private translateService = inject(TranslateService);
  private businessFlowInteractionService = inject(BusinessFlowInteractionService);

  offers = select(CmsState.offers);

  offer_id = input<string>();

  offer = computed(() => {
    return this.offers().getOfferById(this.offer_id());
  });

  advantages = computed<Cms.PlanAdvantageTile[]>(() => {
    return this.offer()?.getAdvantages(this.translateService);
  });

  price = computed(() => {
    return this.offer()?.bss_offer.price ?? this.offer()?.bss_offer.discountPrice;
  });

  currency = computed(() => {
    return this.offer()?.bss_offer.currency;
  });

  commitment = computed<Cms.PlanCommitment[]>(() => {
    const bssOffer = this.offer().bss_offer;
    const cmsOffer = this.offer().cms_offer;

    // TODO get variation prices

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return cmsOffer.variations?.map((variation: any) => {
      return {
        title: variation?.meta?.enhanced?.plan_offers?.commitment?.label || 'No commitment',
        price: bssOffer.price,
        discountPrice: bssOffer.discountPrice,
      };
    });
  });

  advantagesTitle = 'What’s included';
  benefitsTitle = 'Benefits';
  informationTitle = 'Information';
  commitmentTitle = 'Commitment';
  priceTitle = 'Amount to be paid';
  orderButton = 'Order Now';
  appsTitle = 'Entertainment Apps';

  back() {
    this.location.back();
  }

  initialize() {
    this.businessFlowInteractionService.byodInitialize$(Number(this.offer().offerId)).subscribe();
  }
}
