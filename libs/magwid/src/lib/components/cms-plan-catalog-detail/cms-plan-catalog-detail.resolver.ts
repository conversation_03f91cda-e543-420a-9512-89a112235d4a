import { ResolveFn } from '@angular/router';
import { inject } from '@angular/core';
import { Store } from '@ngxs/store';
import {
  CmsGetCommerceProductAction,
  CmsState,
  OfferGetExpoGroupsAction,
  OfferPOQWithContractTypeAction,
  OfferState,
} from '@libs/bss';
import { CatalogGroupContractType, eBusinessFlow } from '@libs/types';
import { MagicResolverModel } from '@libs/plugins';

export const cmsPlanCatalogDetailResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);
  const cmsPage = store.selectSnapshot(CmsState.page)?.find((page) => page.data.sale_type);
  const contractType = cmsPage?.data.sale_type ?? CatalogGroupContractType.POSTPAID;
  const serviceType = cmsPage?.data.service_type ?? 'mobile';

  return [
    {
      selector: store.selectSnapshot(OfferState.offers)?.offerTypes === contractType,
      action: () => {
        return [
          new OfferGetExpoGroupsAction({
            businessFlowSpec: eBusinessFlow.Specification.MAIN_ORDER,
            serviceType,
            contractType,
          }),
          new OfferPOQWithContractTypeAction(contractType),
        ];
      },
      next: [
        {
          selector: false,
          action: () => {
            return new CmsGetCommerceProductAction(
              store.selectSnapshot(OfferState.offers).offerIds,
              store.selectSnapshot(OfferState.offers).offers,
            );
          },
        },
      ],
    },
  ];
};
