::ng-deep :host,
::ng-deep :root {
  --font-size-heading-xl: calc(var(--eds-size-multiplier) * 14);
  --font-size-heading-lg: calc(var(--eds-size-multiplier) * 12);
  --font-size-heading-md: calc(var(--eds-size-multiplier) * 8);
  --font-size-heading-sm: calc(var(--eds-size-multiplier) * 6);
  --font-size-heading-xs: calc(var(--eds-size-multiplier) * 4.5);

  --line-height-heading-xl: calc(var(--eds-size-multiplier) * 16);
  --line-height-heading-lg: calc(var(--eds-size-multiplier) * 14);
  --line-height-heading-md: calc(var(--eds-size-multiplier) * 10);
  --line-height-heading-sm: calc(var(--eds-size-multiplier) * 8);
  --line-height-heading-xs: calc(var(--eds-size-multiplier) * 6);

  --button-primary-background-color: var(--eds-colors-primary-default);
  --button-primary-text-color: var(--eds-colors-text-white);
  --button-primary-border-color: var(--eds-colors-primary-default);
  --button-primary-hover-background-color: var(--eds-colors-primary-dark);
  --button-primary-hover-border-color: var(--eds-colors-primary-dark);
  --button-primary-active-background-color: var(--eds-primary-700);
  --button-primary-active-border-color: var(--eds-primary-700);

  --link-text-color: var(--eds-colors-info-default);

  --swiper-pagination-bullet-height: calc(var(--eds-size-multiplier) * 1.5);
  --swiper-pagination-bullet-width: calc(var(--eds-size-multiplier) * 1.5);
  --swiper-pagination-active-bullet-width: calc(var(--eds-size-multiplier) * 6);
}
