import { Component, CUSTOM_ELEMENTS_SCHEMA, inject, signal } from '@angular/core';
import { SearchComponent } from '@libs/widgets';
import { CommonModule } from '@angular/common';
import { TranslatePipe, TranslateService } from '@libs/plugins';

@Component({
  selector: 'magic-widget-search',
  templateUrl: './search-magic.component.html',
  styleUrls: ['./search-magic.component.scss'],
  imports: [SearchComponent, CommonModule, TranslatePipe],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class SearchMagicComponent {
  private translateService = inject(TranslateService);

  searchResults = signal<SearchResult[]>([]);
  isSearching = signal<boolean>(false);
  showResults = signal<boolean>(false);

  onSearchClose(): void {
    this.searchResults.set([]);
    this.showResults.set(false);
  }

  onSearch(query: string) {
    if (!query || query.trim().length < 2) {
      this.searchResults.set([]);
      this.showResults.set(false);
      return;
    }

    this.isSearching.set(true);

    const results = this.searchInPageContent(query);
    this.searchResults.set(results);
    this.isSearching.set(false);
    this.showResults.set(true);
  }

  private searchInPageContent(query: string): SearchResult[] {
    const results: SearchResult[] = [];
    const normalizedQuery = query.toLowerCase();

    // Get all text content from the page
    const textElements = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span, div, a, li');

    textElements.forEach((element, index) => {
      const text = element.textContent || '';
      const normalizedText = text.toLowerCase();
      if (normalizedText.includes(normalizedQuery)) {
        // Create a result with context
        const startIndex = Math.max(0, normalizedText.indexOf(normalizedQuery) - 20);
        const endIndex = Math.min(text.length, normalizedText.indexOf(normalizedQuery) + normalizedQuery.length + 20);
        const context = text.substring(startIndex, endIndex);

        results.push({
          id: `result-${index}`,
          element: element,
          text: text,
          context: this.highlightSearchTerm(context, normalizedQuery),
          score: this.calculateRelevanceScore(text, normalizedQuery),
        });
      }
    });

    // Sort results by relevance score (higher is better)
    return results.sort((a, b) => b.score - a.score);
  }

  private calculateRelevanceScore(text: string, query: string): number {
    const occurrences = (text.toLowerCase().match(new RegExp(query, 'g')) || []).length;
    const lengthFactor = 1000 / Math.max(100, text.length); // Favor shorter texts

    return occurrences * 10 + lengthFactor;
  }

  private highlightSearchTerm(text: string, query: string): string {
    // Replace the query with a highlighted version
    const regex = new RegExp(query, 'gi');
    return text.replace(regex, (match) => `<strong>${match}</strong>`);
  }

  scrollToElement(result: SearchResult): void {
    // Scroll to the element and highlight it temporarily
    result.element.scrollIntoView({ behavior: 'smooth', block: 'center' });

    // Add a temporary highlight class
    result.element.classList.add('search-highlight');

    // Remove the highlight after a few seconds
    setTimeout(() => {
      result.element.classList.remove('search-highlight');
    }, 3000);

    // Close the search results
    this.showResults.set(false);
  }
}

interface SearchResult {
  id: string;
  element: Element;
  text: string;
  context: string;
  score: number;
}
