<widget-search (sendSearch)="onSearch($event)" (searchClose)="onSearchClose()"></widget-search>
<div class="search-container">
  @if (showResults()) {
    <div class="search-results">
      <div class="search-results-header">
        <h3>{{ 'searchResults' | translate }} ({{ searchResults().length }})</h3>
        <eds-button appearance="subtle" iconOnly iconLeading="cancel" (button-click)="onSearchClose()"></eds-button>
      </div>

      @if (isSearching()) {
        <div class="search-loading">
          <p>{{ 'searching' | translate }}...</p>
        </div>
      } @else if (searchResults().length === 0) {
        <div class="search-no-results">
          <p>{{ 'noResultsFound' | translate }}</p>
        </div>
      } @else {
        <div class="search-results-list">
          @for (result of searchResults(); track result.id) {
            <div class="search-result-item" (click)="scrollToElement(result)">
              <p [innerHTML]="result.context"></p>
            </div>
          }
        </div>
      }
    </div>
  }
</div>
