.base {
  padding: var(--eds-spacing-400);

  eds-card {
    &::part(base) {
      gap: var(--eds-spacing-300);

      @media (min-width: 834px) {
        gap: var(--eds-spacing-600);
      }
    }

    .header {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--eds-spacing-200);
    }

    .order-steps {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(0, 1fr));

      .step-container {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;

        &:not(:last-child)::before {
          content: '';
          height: var(--eds-sizing-050);
          width: clamp(calc(var(--eds-size-multiplier) * 5), 6.25dvw, calc(var(--eds-size-multiplier) * 10));
          right: 0;
          position: absolute;
          top: 50%;
          transform: translate(50%, -50%);
          background: var(--eds-border-color-light);
          z-index: 0;
        }
      }

      .step {
        display: flex;
        width: 94px;
        flex-direction: column;
        align-items: center;
        gap: var(--eds-spacing-100);
        font-size: 14px;
        color: var(--eds-colors-text-light);
        z-index: 1;

        span {
          display: none;
          text-align: center;

          @media (min-width: 834px) {
            display: inline-flex;
          }
        }

        &.active {
          color: var(--eds-colors-success-default);

          .step-icon {
            color: var(--eds-colors-success-default);
          }

          span {
            display: inline-flex;
          }
        }

        .step-icon {
          font-size: var(--eds-sizing-600);
          color: var(--eds-colors-icon-light);
        }
      }
    }

    .order-details-card {
      .detail-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: var(--eds-spacing-200);
        flex-direction: column;

        &:not(:last-child) {
          border-bottom: 1px solid var(--eds-border-color-light);
          padding-bottom: var(--eds-spacing-300);

          @media (min-width: 834px) {
            padding-bottom: var(--eds-spacing-600);
          }
        }

        .detail-value {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          gap: var(--eds-spacing-200);
          flex: 1;

          eds-icon {
            flex-shrink: 0;
          }
        }

        @media (min-width: 834px) {
          flex-direction: row;
        }
      }
    }
  }
}
