/* eslint-disable @typescript-eslint/no-explicit-any */
import { Component, computed, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { CmsFaqComponent as WidgetCmsFaqComponent, CmsLayoutContentDefaultComponent, FaqItem } from '@libs/widgets';

@Component({
  selector: 'magic-widget-cms-faq',
  templateUrl: './cms-faq-magic.component.html',
  imports: [WidgetCmsFaqComponent, CmsLayoutContentDefaultComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class CmsFaqMagicComponent {
  title = input('Frequently Asked Questions');
  faq_content = input<any>([]);

  faqs = computed<FaqItem[]>(() =>
    this.faq_content()?.map((faq: any) => ({
      question: faq.detailed_question?.value,
      answer: faq.body?.value ?? '',
      isOpen: false,
    })),
  );
}
