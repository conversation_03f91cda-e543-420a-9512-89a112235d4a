import { Component, computed, inject } from '@angular/core';
import { CustomerProfileService } from '@libs/bss';
import { RouteDataService } from '@libs/core';
import { BrowserTitleService } from '@libs/plugins';
import { AccountPageHeadingComponent } from '@libs/widgets';
import { Store } from '@ngxs/store';

@Component({
  selector: 'magic-widget-account-page-heading',
  templateUrl: './account-page-heading-magic.component.html',
  standalone: true,
  imports: [AccountPageHeadingComponent],
})
export class AccountPageHeadingMagicComponent {
  private store = inject(Store);
  private browserTitleService = inject(BrowserTitleService);
  private routeDataService = inject(RouteDataService);
  private customerProfileService = inject(CustomerProfileService);
  customerStatus = computed(
    () => this.customerProfileService.customerDetails().customerInformation.customerType?.isActive,
  );

  routeData = computed(() => this.routeDataService.data());

  title = computed(() => this.browserTitleService.title() ?? '');
  showCustomerStatus = computed(() => this.routeData()?.['showCustomerStatus'] === true);
}
