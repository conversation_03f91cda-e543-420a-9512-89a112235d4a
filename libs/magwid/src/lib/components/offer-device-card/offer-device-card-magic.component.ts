import {
  ChangeDetectionStrategy,
  Component,
  computed,
  CUSTOM_ELEMENTS_SCHEMA,
  effect,
  inject,
  input,
  signal,
} from '@angular/core';
import { Media, OfferDeviceCardComponent } from '@libs/widgets';
import { select, Store } from '@ngxs/store';
import { CmsGetPhoneCommerceProductVariationsAction, CmsState, QuoteState } from '@libs/bss';
import { TranslateService } from '@libs/plugins';
import { switchMap } from 'rxjs';

@Component({
  selector: 'magic-widget-offer-device-card',
  templateUrl: './offer-device-card-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [OfferDeviceCardComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class OfferDeviceCardMagicComponent {
  private store = inject(Store);
  private translateService = inject(TranslateService);

  quote = select(QuoteState.quote);

  planCustomerOrderItemId = input<number>();

  deleteConfirmationText = input<string>(this.translateService.translate('yesDelete'));

  showPrice = input<boolean>(false);

  showDelete = input<boolean>(false);

  deviceMedia = signal<Media>(null);

  plan = computed(() => {
    return this.quote()?.findPlan(this.planCustomerOrderItemId());
  });

  device = computed(() => {
    return this.plan()?.device;
  });

  chars = computed(() => {
    return this.plan()?.deviceChars;
  });

  constructor() {
    effect(() => {
      this.callGetPhoneVariationByOfferAction();
    });
  }

  callGetPhoneVariationByOfferAction() {
    const deviceOfferId = this.plan()?.device?.offerId?.toString();

    if (deviceOfferId) {
      this.store
        .dispatch(
          new CmsGetPhoneCommerceProductVariationsAction({
            offerId: deviceOfferId,
          }),
        )
        .pipe(switchMap(() => this.store.selectOnce(CmsState.phoneCommerceProductVariation(deviceOfferId))))
        .subscribe((phoneVariation) => {
          const imageInfo = phoneVariation?.data?.[0]?.image;

          this.deviceMedia.set({
            imageSrc: imageInfo,
            imageAlt: '',
            upperText: '',
            text: this.device().offerName,
            description: '',
          });
        });
    }
  }
}
