import { Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { StepperComponent as WidgetStepperComponent } from '@libs/widgets';
import { Stepper } from '@libs/widgets';
@Component({
  selector: 'magic-widget-stepper',
  templateUrl: './stepper-magic.component.html',
  imports: [WidgetStepperComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class StepperMagicComponent {
  steps: Stepper[] = [
    {
      title: 'Step 1',
      isActive: false,
      isCompleted: true,
    },
    {
      title: 'Step 2',
      isActive: true,
      isCompleted: false,
    },
    {
      title: 'Step 3',
      isActive: false,
      isCompleted: false,
    },
  ];
}
