import { Component, CUSTOM_ELEMENTS_SCHEMA, inject } from '@angular/core';
import { FooterComponent as WidgetFooterComponent } from '@libs/widgets';
import { TranslateService, TranslatePipe, MagicConfig } from '@libs/plugins';
import { cmsFooterResolver } from './cms-header.resolver';
import { CmsState } from '@libs/bss';
import { select } from '@ngxs/store';

@Component({
  selector: 'magic-widget-cms-footer',
  templateUrl: './cms-footer-magic.component.html',
  imports: [WidgetFooterComponent, TranslatePipe],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
@MagicConfig({
  resolve: [cmsFooterResolver],
})
export class CmsFooterMagicComponent {
  private translateService = inject(TranslateService);
  footer = select(CmsState.footer);

  footerLinks = [
    {
      name: this.translateService.translate('mobile'),
      heading: this.translateService.translate('mobile'),
      links: [
        {
          name: this.translateService.translate('prepaidPlans'),
          url: '/prepaid-plans',
        },
        {
          name: this.translateService.translate('postpaidPlans'),
          url: '/postpaid-plans',
        },
        {
          name: this.translateService.translate('bundlePlans'),
          url: '/bundle-plans',
        },
      ],
    },
    {
      name: this.translateService.translate('homeInternet'),
      heading: this.translateService.translate('homeInternet'),
      links: [
        {
          name: this.translateService.translate('internetPlans'),
          url: '/internet-plans',
        },
        {
          name: this.translateService.translate('fiberInternetPlans'),
          url: '/fiber-internet-plans',
        },
        {
          name: this.translateService.translate('satelliteInternet'),
          url: '/satellite-internet-plans',
        },
      ],
    },
    {
      name: this.translateService.translate('mobileInternet'),
      heading: this.translateService.translate('mobileInternet'),
      links: [
        {
          name: this.translateService.translate('simOnlyDataPlansPostpaid'),
          url: '/sim-only-data-plans',
        },
        {
          name: this.translateService.translate('mobileBroadbandDevices'),
          url: '/mobile-broadband-devices',
        },
        {
          name: this.translateService.translate('prepaid'),
          url: '/prepaid',
        },
      ],
    },
    {
      name: this.translateService.translate('devices'),
      heading: this.translateService.translate('devices'),
      links: [
        {
          name: this.translateService.translate('smartPhones'),
          url: '/smart-phones',
        },
        {
          name: this.translateService.translate('tablets'),
          url: '/tablets',
        },
        {
          name: this.translateService.translate('modemRouters'),
          url: '/modem-routers',
        },
      ],
    },
    {
      name: this.translateService.translate('help'),
      heading: this.translateService.translate('help'),
      links: [
        {
          name: this.translateService.translate('faq'),
          url: '/faq',
        },
        {
          name: this.translateService.translate('support'),
          url: '/customer-support',
        },
        {
          name: this.translateService.translate('blog'),
          url: '/blog',
        },
      ],
    },
    {
      name: this.translateService.translate('followUs'),
      heading: this.translateService.translate('followUs'),
      links: [
        {
          name: 'Facebook',
          url: '#',
        },
        {
          name: 'Instagram',
          url: '#',
        },
        {
          name: 'X',
          url: '#',
        },
      ],
    },
  ];

  footerItems = [
    {
      text: this.translateService.translate('exclusivePromos'),
      icon: 'shoppingBag',
    },
    {
      text: this.translateService.translate('securePayments'),
      icon: 'checkmarkCircle',
    },
    {
      text: this.translateService.translate('customerSupport24_7'),
      icon: 'calling',
    },
    {
      text: this.translateService.translate('rightOfWithdrawal'),
      icon: 'calendar',
    },
  ];

  getFullYear() {
    return new Date().getFullYear();
  }
}
