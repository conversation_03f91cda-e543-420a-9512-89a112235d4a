import { Component, CUSTOM_ELEMENTS_SCHEMA, inject, input } from '@angular/core';
import { TranslateService } from '@libs/plugins';
import { Interaction, InteractionListCardComponent } from '@libs/widgets';
import { Router } from '@angular/router';

@Component({
  selector: 'magic-widget-other-interactions',
  templateUrl: './other-interactions-magic.component.html',
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [InteractionListCardComponent],
})
export class OtherInteractionsMagicComponent {
  private translateService = inject(TranslateService);
  private router = inject(Router);

  titleText = input<string>(this.translateService.translate('others'));

  otherInteractions = input<Interaction[]>([
    {
      text: this.translateService.translate('notificationSettings'),
    },
  ]);
}
