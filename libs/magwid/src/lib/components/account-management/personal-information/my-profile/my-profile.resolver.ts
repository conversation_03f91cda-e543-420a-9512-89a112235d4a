import { ResolveFn } from '@angular/router';
import { MagicResolverModel } from '@libs/plugins';
import {
  CurrentState,
  CustomerState,
  InquireCustomerInformationAction,
  InquireIndividualCustomerFormLovContentAction,
  LovGetLanguageTypeAction,
  LovState,
  PermissionLoadAction,
  PermissionState,
  UtilityGetPublicGeneralParameterAction,
  UtilityState,
} from '@libs/bss';
import { Store } from '@ngxs/store';
import { inject } from '@angular/core';
import { eCommon, GeneralParameterEnum } from '@libs/types';

export const myProfileResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);

  const customerId = store.selectSnapshot(CurrentState.customerId);

  return [
    {
      selector: false,
      action: () => {
        return new InquireCustomerInformationAction({
          customerId,
        });
      },
    },
    {
      selector: store.selectSnapshot(LovState.languageType)?.languageTypes?.length,
      action: () => {
        return new LovGetLanguageTypeAction();
      },
    },
    {
      selector: store.selectSnapshot(UtilityState.generalParameter),
      action: () => {
        return new UtilityGetPublicGeneralParameterAction(GeneralParameterEnum.CUSTOMER_MIN_AGE);
      },
    },
    {
      selector: store.selectSnapshot(CustomerState.getIndividualCustomerFormLovContent),
      action: () => {
        return new InquireIndividualCustomerFormLovContentAction(
          eCommon.BsnInterSpecShortCode.CUSTOMER_DEMOGRAPHIC_INFO,
        );
      },
    },
    {
      selector: store.selectSnapshot(PermissionState.getPermission(eCommon.BsnInterSpecShortCode.CUSTOMER_PROFILE)),
      action: () => {
        return new PermissionLoadAction(eCommon.BsnInterSpecShortCode.CUSTOMER_PROFILE);
      },
    },
  ];
};
