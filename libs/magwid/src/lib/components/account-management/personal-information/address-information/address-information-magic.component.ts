import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, inject, OnDestroy } from '@angular/core';
import { MagicConfig, ModalService, ModalSize, ToasterService, TranslatePipe, TranslateService } from '@libs/plugins';
import { InformationCardComponent, InformationCardItemComponent } from '@libs/widgets';
import { Store } from '@ngxs/store';
import { CurrentState, CustomerProfileService } from '@libs/bss';
import { addressInformationResolver } from './address-information.resolver';
import { Address, CreateAddressWrapper, eBusinessFlow, eCommon } from '@libs/types';
import { CommonModule } from '@angular/common';
import { FormatAddressPipe } from '@libs/core';
import { Subject, takeUntil } from 'rxjs';
import { ManageAddressModalComponent } from '../../../modals/manage-address-modal/manage-address-modal.component';

@Component({
  selector: 'magic-widget-address-information',
  imports: [InformationCardComponent, InformationCardItemComponent, CommonModule, TranslatePipe, FormatAddressPipe],
  standalone: true,
  templateUrl: './address-information-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
@MagicConfig({
  resolve: [addressInformationResolver],
})
export class AddressInformationMagicComponent implements OnDestroy {
  private modalService = inject(ModalService);
  private translateService = inject(TranslateService);
  private store = inject(Store);
  private toasterService = inject(ToasterService);
  private customerProfileService = inject(CustomerProfileService);
  private destroy$ = new Subject<void>();

  addresses = this.customerProfileService.addresses;

  hasPermissionToEditAddress = this.customerProfileService.hasPermissionFor(
    eBusinessFlow.Levels.CUST_ADDRESS,
    eCommon.BsnInterSpecShortCode.ADDR_UPDATE,
  );

  hasPermissionToDeleteAddress = this.customerProfileService.hasPermissionFor(
    eBusinessFlow.Levels.CUST_ADDRESS,
    eCommon.BsnInterSpecShortCode.ADDR_REMOVE,
  );

  hasPermissionToCreateAddress = this.customerProfileService.hasPermissionFor(
    eBusinessFlow.Levels.CUST_ADDRESS,
    eCommon.BsnInterSpecShortCode.ADDR_CREATE,
  );

  handleDelete(address: Address) {
    this.customerProfileService
      .deleteAddress(+address.id)
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this._handleSuccess();
      });
  }

  handleEdit(address: Address) {
    const modalRef = this.modalService.open(ManageAddressModalComponent, {
      title: this.translateService.translate('editAddress'),
      size: ModalSize.MEDIUM,
      data: {
        address: address,
        isPrimaryLocked: this.addresses().length === 1 || address.isPrimary,
        closeModal: () => {
          modalRef.close();
        },
        onSubmit: (addressData: CreateAddressWrapper | Address) => {
          this.customerProfileService
            .updateAddress({
              customerId: this.store.selectSnapshot(CurrentState.customerId),
              address: addressData as Address,
            })
            .pipe(takeUntil(this.destroy$))
            .subscribe(() => {
              this._handleSuccess();
              modalRef.close();
            });
        },
      },
    });
  }

  handleAdd() {
    const modalRef = this.modalService.open(ManageAddressModalComponent, {
      title: this.translateService.translate('addNewAddress'),
      size: ModalSize.MEDIUM,
      data: {
        isPrimaryLocked: this.addresses().length < 1,
        closeModal: () => {
          modalRef.close();
        },
        onSubmit: (addressData: CreateAddressWrapper | Address) => {
          this.customerProfileService
            .createAddress(addressData as CreateAddressWrapper)
            .pipe(takeUntil(this.destroy$))
            .subscribe(() => {
              this._handleSuccess();
              modalRef.close();
            });
        },
      },
    });
  }

  private _handleSuccess(): void {
    this.customerProfileService
      .refreshAddressList()
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.toasterService.success({
          title: this.translateService.translate('toast.SUCCESS_TITLE'),
          description: this.translateService.translate('toast.OPERATION_SUCCESS'),
        });
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
