import { Component, computed, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import {
  type CmsBlogCard,
  CmsLatestArticlesComponent as WidgetCmsLatestArticlesComponent,
  CmsLayoutContentDefaultComponent,
} from '@libs/widgets';

@Component({
  selector: 'magic-widget-cms-latest-articles',
  templateUrl: './cms-latest-articles-magic.component.html',
  imports: [WidgetCmsLatestArticlesComponent, CmsLayoutContentDefaultComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class CmsLatestArticlesMagicComponent {
  info = input<string>();
  helperText = input<string>();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  article = input<any[]>();
  items = computed<CmsBlogCard[]>(() => {
    return this.article().map((item) => {
      return {
        title: item.title,
        helperText: item.body?.value,
        image: item.background_image?.data?.meta?.url,
        badgeType: item.tags?.find(Boolean)?.badgeType?.value || 'green',
        badgeLabel: item.tags?.find(Boolean)?.value || 'Hot deals',
      };
    });
  });
}
