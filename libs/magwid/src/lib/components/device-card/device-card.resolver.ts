import { ResolveFn } from '@angular/router';
import { MagicResolverModel } from '@libs/plugins';
import { myProductDetailResolverBase } from '../../resolvers/my-product-detail.resolver';
import { Store } from '@ngxs/store';
import { inject } from '@angular/core';
import { CurrentState, GetInquireInstallmentAction } from '@libs/bss';

export const deviceCardResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);
  const billingAccountId = store.selectSnapshot(CurrentState.currentBillingAccountId);

  return [
    ...myProductDetailResolverBase(),
    {
      selector: false,
      action: () => {
        return new GetInquireInstallmentAction(store.selectSnapshot(CurrentState.customerId), billingAccountId);
      },
    },
  ];
};
