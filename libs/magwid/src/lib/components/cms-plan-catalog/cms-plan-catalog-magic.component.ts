import { Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject } from '@angular/core';
import { MagicConfig, TranslateService } from '@libs/plugins';
import {
  Cms,
  CmsLayoutContentDefaultComponent,
  CmsPlanCatalogComponent as WidgetCmsPlanCatalogComponent,
} from '@libs/widgets';
import { select, Store } from '@ngxs/store';
import {
  BusinessFlowInteractionService,
  CMSOfferData,
  CmsState,
  CurrentState,
  OfferState,
  QuoteState,
} from '@libs/bss';
import { ActivatedRoute, Router } from '@angular/router';
import { cmsPlanCatalogResolver } from './cms-plan-catalog.resolver';
import { KnownLocalStorageKeys, LocalStorageService } from '@libs/core';
import { CatalogGroupContractType, eBusinessFlow } from '@libs/types';
import { ButtonAppearanceValues } from '@eds/components';

@Component({
  selector: 'magic-widget-cms-plan-catalog',
  templateUrl: './cms-plan-catalog-magic.component.html',
  styleUrls: ['./cms-plan-catalog-magic.component.scss'],
  imports: [WidgetCmsPlanCatalogComponent, CmsLayoutContentDefaultComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
@MagicConfig({
  resolve: [cmsPlanCatalogResolver],
})
export class CmsPlanCatalogMagicComponent {
  private router = inject(Router);
  private store = inject(Store);
  private localStorageService = inject(LocalStorageService);
  private translateService = inject(TranslateService);
  private businessFlowInteractionService = inject(BusinessFlowInteractionService);
  private route = inject(ActivatedRoute);

  expoGroups = select(OfferState.expoGroups);
  offers = select(CmsState.offers);
  quote = select(QuoteState.quote);
  currentFlow = select(CurrentState.currentFlow);

  tabs = computed<Cms.PlansTab[]>(() => {
    const EXCLUDED_EXPOGROUP_SHORTCODES = ['chooseYourOwnPlans', 'mixAndMatchPlansPrepaid'];

    return this.expoGroups()
      .filter((expoGroup) => !EXCLUDED_EXPOGROUP_SHORTCODES.includes(expoGroup.shortCode))
      .map((expoGroup) => ({
        title: expoGroup.name,
        plans: this.offers()
          .getOffersByExpoGroupId(expoGroup.expoGroupId.toString())
          .map((offer) => this.buildOffer(offer)),
      }))
      .filter((tab) => tab.plans.length > 0);
  });

  buildOffer(offer: CMSOfferData): Cms.Plan {
    return {
      title: offer.name,
      subTitle: this.translateService.translate('whatsIncluded'),
      advantages: offer.getAdvantages(this.translateService),
      price: {
        price: offer.bss_offer.price,
        discount: offer.bss_offer.discountPrice,
        currency: offer.bss_offer.currency,
        description: offer.bss_offer.commitmentDescription,
      },
      appsTitle: this.translateService.translate('entertainmentApps'),
      apps: offer.entertainmentApps,
      actions: [
        {
          text: this.translateService.translate('orderNow'),
          appearance: 'primary',
          action: () => {
            this.businessFlowInteractionService.byodInitialize$(Number(offer.offerId)).subscribe();
          },
        },
        {
          text: this.translateService.translate('allDetails'),
          appearance: 'default',
          action: () => {
            switch (this.route.snapshot.queryParams.flow) {
              case eBusinessFlow.Specification.PACKAGE_CHANGE:
                return this.router.navigate([offer.detailPath], {
                  queryParams: {
                    flow: this.quote().currentBusinessFlowSpecShortCode,
                    customerOrderId: this.quote().customerOrderId,
                  },
                });
              default:
                return this.router.navigate([offer.detailPath]);
            }
          },
        },
        ...(this.currentFlow() === eBusinessFlow.Specification.PACKAGE_CHANGE
          ? []
          : [
              {
                text: this.translateService.translate('buyWithDevices'),
                appearance: 'link' as ButtonAppearanceValues,
                action: () => {
                  const cmsPage = this.store
                    .selectSnapshot(CmsState.page)
                    ?.find((page) => page.selector === 'widget-cms-plan-catalog');
                  this.localStorageService.set(KnownLocalStorageKeys.BUY_WITH_DEVICES, {
                    offerId: offer.offerId,
                    type: cmsPage?.data.widget_plan_type ?? CatalogGroupContractType.POSTPAID,
                  });
                  this.router.navigate([`buy-with-devices`]);
                },
              },
            ]),
      ],
      tags: offer.marketing_tags,
    };
  }
}
