import { ActivatedRoute, ResolveFn } from '@angular/router';
import { inject } from '@angular/core';
import { Store } from '@ngxs/store';
import {
  CmsGetCommerceProductAction,
  CmsState,
  OfferGetExpoGroupsAction,
  OfferPOQWithContractTypeAction,
  OfferState,
  QuoteGetQuoteAction,
  QuoteState,
} from '@libs/bss';
import { CatalogGroupContractType, eBusinessFlow } from '@libs/types';
import { MagicResolverModel } from '@libs/plugins';

export const cmsPlanCatalogResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);
  const route = inject(ActivatedRoute);
  const cmsPage = store.selectSnapshot(CmsState.page)?.find((page) => page.selector === 'widget-cms-plan-catalog');
  const contractType = cmsPage?.data.widget_plan_type ?? CatalogGroupContractType.POSTPAID;
  const serviceType = cmsPage?.data.service_type ?? 'mobile';

  const businessFlowSpec = route.snapshot.queryParams.flow ?? eBusinessFlow.Specification.MAIN_ORDER;
  const customerOrderId = route.snapshot.queryParams.customerOrderId;

  return [
    {
      selector: false,
      action: () => {
        return customerOrderId
          ? [
              new QuoteGetQuoteAction({
                customerOrderId,
                currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.CART_SUMMARY,
              }),
            ]
          : [];
      },
      next: [
        {
          selector: store.selectSnapshot(OfferState.offers)?.offerTypes === contractType,
          action: () => {
            const quote = store.selectSnapshot(QuoteState.quote);
            return [
              new OfferGetExpoGroupsAction({
                businessFlowSpec,
                serviceType,
                contractType,
              }),
              new OfferPOQWithContractTypeAction(
                contractType,
                businessFlowSpec,
                quote?.deactivationBundleOffer?.offerId,
              ),
            ];
          },
          next: [
            {
              selector: false,
              action: () => {
                return new CmsGetCommerceProductAction(
                  store.selectSnapshot(OfferState.offers).offerIds,
                  store.selectSnapshot(OfferState.offers).offers,
                );
              },
            },
          ],
        },
      ],
    },
  ];
};
