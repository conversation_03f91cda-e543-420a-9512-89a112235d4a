/* eslint-disable @typescript-eslint/no-explicit-any */
import { Component, computed, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { CmsHomeBanner, CmsLayoutContentDefaultComponent, HomeBannerComponent } from '@libs/widgets';
import { getLink } from '@libs/bss';

@Component({
  selector: 'magic-widget-cms-home-banner',
  templateUrl: './cms-home-banner-magic.component.html',
  imports: [CmsLayoutContentDefaultComponent, HomeBannerComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class CmsHomeBannerMagicComponent {
  banner_item = input<any[]>([]);

  banners = computed<CmsHomeBanner[]>(() => {
    return this.banner_item().map((item) => {
      return {
        title: item.title,
        superTitle: item.super_title,
        subtitle: item.body?.value,
        image: item.desktop_image?.data?.meta?.url,
        buttons: item?.button?.map((button: any) => {
          return {
            text: button.title,
            link: getLink(button.url?.uri),
          };
        }),
      };
    });
  });
}
