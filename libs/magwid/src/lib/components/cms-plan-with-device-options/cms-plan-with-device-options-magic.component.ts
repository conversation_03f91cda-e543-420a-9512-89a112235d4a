import { Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject } from '@angular/core';
import { MagicConfig, TranslateService } from '@libs/plugins';
import {
  CmsPlanPreviewCardComponent,
  CmsPlanDeviceOptionsComponent,
  CmsLayoutContentDefaultComponent,
} from '@libs/widgets';
import { Cms } from '@libs/widgets';
import { cmsPlanWithDeviceOptionsResolver } from './cms-plan-with-device-options.resolver';
import { BusinessFlowInteractionService, CmsState } from '@libs/bss';
import { select } from '@ngxs/store';
import { KnownLocalStorageKeys, LocalStorageService } from '@libs/core';
import { Router } from '@angular/router';
import { CurrencyPipe, Location } from '@angular/common';
import { buyWithDevicesLocalStorage } from '@libs/types';
@Component({
  selector: 'magic-widget-cms-plan-with-device-options',
  templateUrl: './cms-plan-with-device-options-magic.component.html',
  styleUrls: ['./cms-plan-with-device-options-magic.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [CurrencyPipe],
  imports: [CmsPlanPreviewCardComponent, CmsPlanDeviceOptionsComponent, CmsLayoutContentDefaultComponent],
})
@MagicConfig({
  resolve: [cmsPlanWithDeviceOptionsResolver],
})
export class CmsPlanWithDeviceOptionsMagicComponent {
  private router = inject(Router);
  private translateService = inject(TranslateService);
  private localStorageService = inject(LocalStorageService);
  private businessFlowInteractionService = inject(BusinessFlowInteractionService);
  private currencyPipe = inject(CurrencyPipe);
  private location = inject(Location);

  phoneVariations = select(CmsState.phoneVariations);
  offers = select(CmsState.offers);

  selectedOffer = computed(() =>
    this.offers().getOfferById(
      this.localStorageService.get<buyWithDevicesLocalStorage>(KnownLocalStorageKeys.BUY_WITH_DEVICES)?.offerId,
    ),
  );

  devices = computed<Cms.Device[]>(() =>
    this.phoneVariations().data.map((phone) => ({
      id: phone.id,
      brand: 'Apple',
      model: phone.title,
      price: this.currencyPipe.transform(phone.price),
      deviceImage: phone.image,
      oldPrice: phone.regularPrice === phone.price ? undefined : this.currencyPipe.transform(phone.regularPrice),
    })),
  );

  itemAmount = computed(() =>
    this.translateService.translate('deviceListDeviceAmount', { amount: this.devices().length }),
  );

  previewCard = computed<Cms.PlanPreviewCard>(() => ({
    title: this.translateService.translate('yourPlanIsReadyNowChooseYourSmartphone'),
    plan: this.selectedOffer()?.name ?? '',
    icon: 'simCard',
    properties: [
      { label: this.translateService.translate('data'), value: this.selectedOffer()?.dataAmount ?? '' },
      { label: this.translateService.translate('voice'), value: this.selectedOffer()?.voiceAmount ?? '' },
      { label: this.translateService.translate('sms'), value: this.selectedOffer()?.smsAmount ?? '' },
    ],
    price:
      this.currencyPipe.transform(
        this.selectedOffer()?.bss_offer.discountPrice ?? this.selectedOffer()?.bss_offer.price,
      ) + '/month',
  }));

  continueWithoutDevice() {
    this.businessFlowInteractionService
      .byodInitialize$(
        Number(
          this.localStorageService.get<buyWithDevicesLocalStorage>(KnownLocalStorageKeys.BUY_WITH_DEVICES)?.offerId,
        ),
      )
      .subscribe();
  }

  selectDevice(deviceId: string) {
    this.businessFlowInteractionService
      .deviceInitalize$(
        Number(
          this.localStorageService.get<buyWithDevicesLocalStorage>(KnownLocalStorageKeys.BUY_WITH_DEVICES)?.offerId,
        ),
        Number(deviceId),
      )
      .subscribe();
  }

  back() {
    this.location.back();
  }
}
