import { ResolveFn } from '@angular/router';
import { inject } from '@angular/core';
import {
  CmsGetCommerceProductAction,
  CmsGetPhoneVariationWithPoqAction,
  OfferPOQWithContractTypeAction,
  OfferState,
} from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';
import { KnownLocalStorageKeys, LocalStorageService } from '@libs/core';
import { Store } from '@ngxs/store';
import { buyWithDevicesLocalStorage } from '@libs/types';

export const cmsPlanWithDeviceOptionsResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);
  const buyWithDevices = inject(LocalStorageService).get<buyWithDevicesLocalStorage>(
    KnownLocalStorageKeys.BUY_WITH_DEVICES,
  );
  return [
    {
      selector: false,
      action: () => {
        return new CmsGetPhoneVariationWithPoqAction({
          offerId: buyWithDevices.offerId,
        });
      },
    },
    {
      selector: false,
      action: () => {
        return new OfferPOQWithContractTypeAction(buyWithDevices.type);
      },
      next: [
        {
          selector: false,
          action: () => {
            return new CmsGetCommerceProductAction(
              [buyWithDevices.offerId],
              store.selectSnapshot(OfferState.offers).offers,
            );
          },
        },
      ],
    },
  ];
};
