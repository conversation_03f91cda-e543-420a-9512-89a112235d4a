import { Component, inject } from '@angular/core';
import { loggedInUserResolver } from '@libs/magwid';
import { MagicConfig, TranslatePipe, TranslateService } from '@libs/plugins';
import { OfferCarousel, OfferCarouselComponent } from '@libs/widgets';

@Component({
  selector: 'magic-widget-offers',
  templateUrl: './offers-magic.component.html',
  imports: [OfferCarouselComponent, TranslatePipe],
})
@MagicConfig({
  resolve: [loggedInUserResolver],
})
export class OffersMagicComponent {
  private translateService = inject(TranslateService);

  offersCarouselItems: OfferCarousel[] = [
    {
      title: this.translateService.translate('mobileHappyHours'),
      description: this.translateService.translate('unlimitedSurfingOffer'),
      linkText: this.translateService.translate('explore'),
      linkSrc: '/',
      image: '',
      fallbackImage: 'assets/images/offer-1.png',
      headingSize: 'md',
      descriptionMaxLines: 2,
    },
    {
      title: this.translateService.translate('mobileHappyHours'),
      description: this.translateService.translate('unlimitedSurfingOffer'),
      linkText: this.translateService.translate('explore'),
      linkSrc: '',
      image: '',
      fallbackImage: 'assets/images/offer-2.png',
      headingSize: 'md',
      descriptionMaxLines: 2,
    },
    {
      title: this.translateService.translate('mobileHappyHours'),
      description: this.translateService.translate('unlimitedSurfingOffer'),
      linkText: this.translateService.translate('explore'),
      linkSrc: '',
      image: '',
      fallbackImage: 'assets/images/offer-3.png',
      headingSize: 'md',
      descriptionMaxLines: 2,
    },
  ];
}
