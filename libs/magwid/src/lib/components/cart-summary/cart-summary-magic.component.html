<eds-card [title]="'cart-summary' | translate">
  @if (plans()?.length) {
    <widget-checkout-card
      icon="simCard"
      [title]="familyCategoryName() | titlecase"
      [isActive]="true"
      [itemAmount]="plans().length"
    >
      <widget-seperated>
        @for (plan of plans(); track plan.customerOrderItemId) {
          <div class="section">
            <magic-widget-offer-plan-card
              [planCustomerOrderItemId]="plan.customerOrderItemId"
              [showDetails]="true"
              [showPrice]="true"
              [tags]="getPlanTags(plan.customerOrderItemId)"
            ></magic-widget-offer-plan-card>

            @if (quote()?.deviceOffer(plan.customerOrderItemId)) {
              <magic-widget-offer-device-card
                [planCustomerOrderItemId]="plan.customerOrderItemId"
                [showPrice]="true"
              ></magic-widget-offer-device-card>
            }
            <magic-widget-offer-pricing-and-agreement-terms
              [planCustomerOrderItemId]="plan.customerOrderItemId"
            ></magic-widget-offer-pricing-and-agreement-terms>
          </div>
        }
      </widget-seperated>
    </widget-checkout-card>
    <widget-item-delete-action
      class="card-action"
      slot="actionButton"
      [buttonText]="'clearCart' | translate"
      [deleteConfirmationText]="'yesDelete' | translate"
      (deleteItem)="clearShoppingCart()"
    ></widget-item-delete-action>
  } @else {
    <widget-empty-card></widget-empty-card>
  }
</eds-card>
