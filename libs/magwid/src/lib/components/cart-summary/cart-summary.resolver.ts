import { ActivatedRouteSnapshot, ResolveFn, RouterStateSnapshot } from '@angular/router';
import { MagicResolverModel } from '@libs/plugins';
import { getQuoteResolver } from '../../resolvers/get-quote.resolver';
import { BusinessWorkFlowSetAction } from '@libs/bss';
import { eBusinessFlow } from '@libs/types';
import { businessWorkflowConfigResolver } from '@libs/magwid';

export const cartSummaryResolver: ResolveFn<MagicResolverModel[]> = (
  route: ActivatedRouteSnapshot,
  state: RouterStateSnapshot,
) => {
  const resolvers = getQuoteResolver(route, state) as MagicResolverModel[];

  resolvers[0].next[1].next.push((businessWorkflowConfigResolver(route, state) as MagicResolverModel[])[0]);

  resolvers.unshift({
    selector: false,
    action: () => {
      return new BusinessWorkFlowSetAction(eBusinessFlow.WorkflowStateType.PRE_VALIDATION);
    },
  });

  return resolvers;
};
