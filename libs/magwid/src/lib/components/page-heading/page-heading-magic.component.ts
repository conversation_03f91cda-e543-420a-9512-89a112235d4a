import { Component, computed, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { UserState } from '@libs/bss';
import { RouteDataService } from '@libs/core';
import { BrowserTitleService } from '@libs/plugins';
import { PageHeadingComponent as WidgetPageHeadingComponent } from '@libs/widgets';
import { Store } from '@ngxs/store';

@Component({
  selector: 'magic-widget-page-heading',
  templateUrl: './page-heading-magic.component.html',
  standalone: true,
  imports: [WidgetPageHeadingComponent],
})
export class PageHeadingMagicComponent {
  private store = inject(Store);
  private browserTitleService = inject(BrowserTitleService);
  private routeDataService = inject(RouteDataService);

  routeData = computed(() => this.routeDataService.data());

  private loggedInUser = toSignal(this.store.select(UserState.getLoggedInUser));

  title = computed(() => {
    const page = this.routeData()?.['page'];

    switch (page) {
      case 'accountManagement':
        return this.loggedInUser()?.name ?? '';
      default:
        return this.browserTitleService.title() ?? '';
    }
  });
}
