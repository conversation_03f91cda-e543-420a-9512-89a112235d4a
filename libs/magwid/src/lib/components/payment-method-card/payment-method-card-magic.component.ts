import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject, signal } from '@angular/core';
import { MagicConfig, TranslatePipe, TranslateService } from '@libs/plugins';
import { Media, PaymentMethodCardComponent as WidgetPaymentMethodCardComponent } from '@libs/widgets';
import { paymentMethodCardResolver } from './payment-method-card.resolver';
import { select, Store } from '@ngxs/store';
import { CurrentState, InvoiceState, PaymentState } from '@libs/bss';
import { InteractionsMagicComponent } from '../interactions/interactions-magic.component';
import { eBusinessFlow } from '@libs/types';

@Component({
  selector: 'magic-widget-payment-method-card',
  templateUrl: './payment-method-card-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [WidgetPaymentMethodCardComponent, TranslatePipe, InteractionsMagicComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
@MagicConfig({
  resolve: [paymentMethodCardResolver],
})
export class PaymentMethodCardMagicComponent {
  private translateService = inject(TranslateService);
  private store = inject(Store);

  billingAccountId = select(CurrentState.currentBillingAccountId);

  paymentMethods = select(PaymentState.customerPaymentMethods);

  currentPaymentMethod = computed(() => this.paymentMethods().paymentMethodByBillingAccountId(this.billingAccountId()));

  paymentMethodMedia = computed<Media>(() => {
    const daysRemainingForPayment =
      this.store.selectSnapshot(InvoiceState.invoicingBillingAccounts)?.daysRemainingForPayment || '0';
    const key = Number(daysRemainingForPayment) > 0 ? 'paymentIn' : 'paymentHasBeenReceived';
    const params = Number(daysRemainingForPayment) > 0 ? { days: daysRemainingForPayment } : undefined;
    return {
      imageSrc: this.currentPaymentMethod().paymentMethodLogo,
      imageAlt: '',
      text: '',
      upperText: daysRemainingForPayment && this.translateService.translate(key, params),
      description: this.currentPaymentMethod().formatedLastFourDigit,
    };
  });
  interactionsLevel = signal<eBusinessFlow.Levels>(eBusinessFlow.Levels.BILL_ACCT);
}
