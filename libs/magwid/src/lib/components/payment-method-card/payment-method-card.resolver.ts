import { ResolveFn } from '@angular/router';
import { MagicResolverModel } from '@libs/plugins';
import {
  CurrentState,
  GetInvoicingBillingAccount,
  InvoiceState,
  PaymentGetCustomerPaymentMethodsAction,
  PaymentState,
} from '@libs/bss';
import { inject } from '@angular/core';
import { Store } from '@ngxs/store';

export const paymentMethodCardResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);
  const customerId = store.selectSnapshot(CurrentState.customerId);

  return [
    {
      selector: store.selectSnapshot(PaymentState.customerPaymentMethods)?.paymentMethods?.length,
      action: () => {
        return new PaymentGetCustomerPaymentMethodsAction(customerId);
      },
      next: [
        {
          selector:
            !store.selectSnapshot(CurrentState.currentBillingAccountId) &&
            store.selectSnapshot(InvoiceState.invoicingBillingAccounts)?.invoicingBillingAccounts?.length,
          action: () => {
            const billingAccountId = store.selectSnapshot(CurrentState.currentBillingAccountId);
            return new GetInvoicingBillingAccount({ billingAccountId });
          },
        },
      ],
    },
  ];
};
