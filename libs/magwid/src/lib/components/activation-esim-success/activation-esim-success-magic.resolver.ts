import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, ResolveFn } from '@angular/router';
import { CurrentState, SetCurrentBillingAccountIdAction } from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';
import { Store } from '@ngxs/store';
import { myProductDetailResolverBase } from '@libs/magwid';

export const activationEsimSuccessMagicResolver: ResolveFn<MagicResolverModel[]> = (route: ActivatedRouteSnapshot) => {
  const store = inject(Store);
  const productBillingAccountId =
    store.selectSnapshot(CurrentState.currentBillingAccountId) || route.params.billingAccountId;
  store.dispatch(new SetCurrentBillingAccountIdAction(route.params.billingAccountId));

  return myProductDetailResolverBase(productBillingAccountId);
};
