import { Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CmsBlogBadgeTypes, CmsBlogCardComponent as WidgetCmsBlogCardComponent } from '@libs/widgets';

@Component({
  selector: 'magic-widget-cms-blog-card',
  templateUrl: './cms-blog-card.component.html',
  imports: [WidgetCmsBlogCardComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class CmsBlogCardComponent {
  title = 'Title';
  helperText = 'helper text';
  image = 'https://picsum.photos/200/300';
  badgeType: CmsBlogBadgeTypes = 'green';
  badgeLabel = 'badge label';
}
