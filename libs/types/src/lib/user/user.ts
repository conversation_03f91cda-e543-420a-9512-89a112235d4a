import { Auth } from '../auth';

export namespace User {
  export interface LoggedInUser {
    username: string;
    name: string;
    userType: string;
    userId: number;
    customerId: number;
    saleChannelShortCode: string;
    saleChannelName: string;
    employeeNumber: string;
  }

  export interface SimpleUser {
    id: number;
    jobTitleName?: string;
    name: string;
    label: string;
    preferredCollationName: string;
    preferredCollation?: string;
    saleCnlId: number;
    saleCnlName: string;
    uname: string;
    userId: number;
    userType: string;
    userScreenName?: string;
    confirmationKey: string;
    customerId: number;
    employeeId: number;
    employeeName: string;
    employeeNumber: string;
    endUserId: number;
    generalParameterList: unknown;
    rawValue: string;
    firstName?: string;
    lastName?: string;
    screenName?: string;
  }

  export type AdditionalUserData = Partial<
    {
      confirmationKey: string;
      customerId: number;
      customerScreenName: string;
      employeeNumber?: string;
      endUserId: number;
      employeeFirstName: string;
      employeeId: string;
      employeeName: string;
      saleCnlShortCode: string;
      loginPage: string;
      employeeType: string;
      credentialType: string;
    } & SimpleUser
  >;

  export type Profile = AdditionalUserData & LoggedInUser & Auth.Keycloak.Profile;
}
