export namespace eEmployee {
  export enum Employee {
    USER_MANAGEMENT = 'user-management',
    EMPLOYEE_UPDATE = 'employeeUpdate',
    EMPLOYEE_VIEW = 'employeeView',
    FIRST_NAME = 'firstName',
    LAST_NAME = 'lastName',
    BIRTH_DATE = 'birthDate',
    STATUS = 'status',
    EMAIL = 'email',
    COUNTRY_CALL_CODE = 'countryCallCode',
    MOBILE_PHONE = 'mobilePhone',
    TIME_ZONE = 'timeZone',
    SCREEN_NAME = 'screenName',
    ROLES = 'roles',
    EMPLOYEE_NUMBER = 'employeeNumber',
    SALES_CHANNEL = 'salesChannel',
    LANGUAGE_PREFERENCE = 'languagePreference',
    NTF_LANGUAGE_PREFERENCE = 'ntfLanguagePreference',
    JOB_TITLE = 'jobTitle',
    USER_GROUP = 'userGroup',
  }

  export enum UIPermission {
    ORG_CUST_SEARCH_FORM = 'org-customer-search-form',
    ORG_HOMEPAGE_FORM = 'org-homepage-form',
    ORG_GENERALINFO_FORM = 'org-generalInfo-form',
    ORG_BILL_COMM_INFO = 'org-bill-comm-info',
    ORG_BILL_BASIC_INFO = 'org-bill-basic-info',
    ORG_CUST_COMMUNICATION_INFO = 'org-cust-communication-info',
    ORG_CUST_REGISTRATION_INFO = 'org-cust-registration-info',
    ORG_CUST_DEMOGRAPHIC_INFO = 'org-cust-demographic-info',
    BILLING_ACCOUNT_RELATED_CONTACT = 'billing-account-related-contact',
    CUSTOMER_RELATED_CONTACT = 'customer-related-contact',
    ORG_CUST_BASIC_INFO = 'org-cust-basic-info',
    ORG_USER_INFO_FORM = 'org-user_info-form',
    CONTACT_COMM_INFO = 'contact-comm-info',
    PERSONAL_COMM_INFO = 'personal-comm-info',
    ADD_EMAIL = 'add-email',
    ADD_ADDRESS = 'add-address',
    ADD_PHONE_NUMBER = 'add-phone-number',
    ADD_TAX_EXEMPTION = 'add-tax-exemption',
    ADD_PERSONAL_ADDRESS = 'addPersonalAddress',
    ADD_PERSONAL_EMAIL = 'addPersonalEmail',
    ADD_PERSONAL_PHONE_NUMBER = 'addPersonalPhoneNumber',
    ADD_CONTACT_ADDRESS = 'addAddress',
    ADD_CONTACT_EMAIL = 'addEmail',
    ADD_CONTACT_PHONE_NUMBER = 'addPhoneNumber',
    SEARCH_ORG_CUSTOMER_CONTACT = 'search-org-customer-contact',
    ORG_CONTRACT_DETAILS = 'org-contract-details',
    VIEW_VERSION_HISTORY = 'view-version-history',
    VIEW_VERSION_HISTORY_BUTTON = 'view-version-history-button',
    ORG_CONTRACT_DETAILS_HISTORY = 'org-contract-details-history',
    MANAGE = 'manage',
    MANAGE_BUTTON = 'manage-button',
    ADD_SOCIAL_MEDIA_BUTTON = 'add-social_media',
    ADD_WEBSITE_BUTTON = 'add-website',
    ORG_DEACTV_BILL_ACCT_PAGE = 'org-deactvBillAcct-page',
    ORG_LINE_DEACTIVATION = 'org-line-deactivation',
    ORG_PLAN_SELECTION = 'org-plan-selection',
    WSC_MENU = 'wsc-menu',
  }
}
