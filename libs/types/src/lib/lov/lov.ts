import { Language } from './language';
import { Response } from '../http';
import { ContactMediumType } from './contact-medium-type';
import { Country } from './country';
import { SearchResourceQualification } from './search-resource-qualification';
import { State } from './state';
import { City } from './city';

export namespace Lov {
  export type CriteriaRequest = Partial<{
    page: number;
    size: number;
    predicates: CriteriaFilter;
  }>;

  export type CriteriaFilter = Partial<{
    id: ValueType;
    groupTypeCode: ValueType;
    isActive: ValueType;
    shortCode: ValueType;
    countryId: ValueType;
    stateId: ValueType;

    // add if necessary
    // organizationTypeId: ValueType;
    // entityCodeName: ValueType;
    // entityName: ValueType;
    // taxGeographyPlaceId: ValueType;
  }>;

  export type ValueType = Partial<{
    value: number | string;
  }>;

  export type PredicatesBuilder = Partial<{
    [K in keyof CriteriaFilter]: number | string;
  }>;

  export type LovLanguageResponse = Response.Result<Language.Language>;

  export type LovContactMediumResponse = Response.Result<ContactMediumType.ContactMediumType>;

  export type LovCountryResponse = Response.Result<Country.Country>;

  export type LovStateResponse = Response.Result<State.State>;

  export type LovCityResponse = Response.Result<City.City>;

  export interface LovResult {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    generalParameterList: any[];
    lovItemId: number;
    name: string;
    shortCode: string;
    parent: boolean | number | string;
  }

  export type LovNpaXXResponse = Partial<Lov>;

  export interface Lov {
    lovId: number;
    name: string;
    shortCode: string;
    description: string;
    extShortCode: string;
    items: LovItem[];
  }

  export interface LovItem {
    lovItemId?: number;
    name: string;
    shortCode?: string;
    description?: string;
    val: string;
    parentLovItemId?: number;
    parent: boolean;
  }

  export type LovSearchMsisdnRequest = SearchResourceQualification.SearchResourceQualificationRequest;

  export type LovSearchMsisdnResponse = SearchResourceQualification.SearchResourceQualificationInfo[];
}
