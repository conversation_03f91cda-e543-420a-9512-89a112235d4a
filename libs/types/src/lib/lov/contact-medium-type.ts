import { GeneralParameterContent } from '../dto';
import { CapturedPartyPrivacy } from '../privacy-specification';

export namespace ContactMediumType {
  export interface ContactMediumType {
    id: number;
    shortCode: ContactMediumTypeShortCode | string;
    hasExtension: boolean;
    groupTypeCode?: ContactMediumTypeGroupCode;
    name: string;
    externalShortCode?: string;
    privExternalShortCode?: string;
  }

  export type ContactMediumDTO = Partial<{
    id: number;
    contactData: string;
    contactDataExtension: string;
    shortCode: ContactMediumTypeShortCode;
    isPrimary: boolean;
    contactMediumType: Partial<ContactMediumType>;
    contactMediumTypeId: number;
    owner: Owner;
    rowId: number;
    isValid: boolean;
    dataType: string;
    contactDataPrefix: string;
    // TODO add if necessary
    // statusId: number;
    // createdDate: string;
    // isMarketingPermission: boolean;
    // callStartHour: string;
    // callEndHour: string;
    // isActive: boolean;
    // mergedContactData?: string;
  }>;

  export enum ContactMediumTypeShortCode {
    GSM = 'GSM',
    HOME_PHONE = 'HOME_PHONE',
    WORK_PHONE = 'WORK_PHONE',
    EMAIL = 'EMAIL',
    SOCIAL_MEDIA = 'SOCIAL_MEDIA',
    CO_WEB_SITE = 'CO_WEB_SITE',
  }

  export enum ContactMediumTypeGroupCode {
    PHONE = 'PHONE',
    EMAIL = 'EMAIL',
    SOCIAL_MEDIA = 'SOCIAL_MEDIA',
    WEBSITE = 'WEBSITE',
  }

  export type CreateCustomerContactMediumRequest = Partial<{
    customerId: number;
    contactMedium: ContactMediumDTO;
    consentEmail: boolean;
    consentSms: boolean;
    privacy: PartyPrivacyUpdateRequest;
  }>;

  export interface PartyPrivacyUpdateRequest {
    privacyList: CapturedPartyPrivacy.Content[];
    customerUpdate?: boolean;
  }

  export type Owner = GeneralParameterContent<{
    dataType?: string;
    dataTypeId: number;
    rowId: number;
  }>;
}
