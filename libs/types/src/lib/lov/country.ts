export namespace Country {
  export interface Country {
    id: number;
    countryCode: string;
    uiOrdId: number;
    resourceKey?: string;
    callCode: string;
    identity?: string;
    isShipmentApplicable: number;
    isCamel?: boolean;
    isoCode: string;
    isActive: number;
    name: string;
    multiLanguageData?: CountryLanguage[];
  }

  export interface CountryLanguage {
    id: string;
    name: string;
    description: string;
    language: string;
    isActive: number;
  }
}
