export namespace State {
  export interface State {
    id: string;
    name: string;
    stateCode?: string;
    countryId?: number;
    versionNo?: number;
    provisionStateId?: number;
    versionTs?: string;
    identity: string;
    isShipmentApplicable: number;
    isActive: number;
    multiLanguageData: StateLanguage[];
  }

  export interface StateLanguage {
    name: string;
    description: string;
    srchName: string;
  }
}
