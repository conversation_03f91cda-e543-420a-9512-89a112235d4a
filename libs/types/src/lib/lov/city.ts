export namespace City {
  export interface City {
    id: string;
    name: string;
    description: string;
    countryId: number;
    stateId: number;
    oldName: string;
    versionNo: number;
    provisionCityId: number;
    versionTs: string;
    identity: string;
    sortId: number;
    multiLanguageData: CityLanguage[];
  }

  export interface CityLanguage {
    name: string;
    nameNorm: string;
    oldName: string;
    description: string;
    language: string;
    isActive: number;
  }
}
