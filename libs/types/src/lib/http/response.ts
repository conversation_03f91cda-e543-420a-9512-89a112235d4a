export namespace Response {
  export type Result<T> = Partial<{
    content: T[];
    totalElements: number;
    totalPages: number;
    last: boolean;
    numberOfElements: number;
    first: boolean;
    empty: boolean;
    size: number;
    number: number;
    sort?: Sort;
    pageable?: Pageable;
  }>;

  export interface Sort {
    sorted: boolean;
    unsorted: boolean;
    empty: boolean;
  }

  export interface Pageable {
    sort: Sort;
    offset: number;
    pageSize: number;
    pageNumber: number;
    paged: boolean;
    unpaged: boolean;
  }

  export interface Counts {
    totalpage: number;
    totalcount: number;
    size: number;
    count: number;
    page: number;
    firstpage: boolean;
    lastpage: boolean;
  }

  export interface Paging {
    totalElements?: number;
    totalPages: number;
    size: number;
    page: number;
  }
}
