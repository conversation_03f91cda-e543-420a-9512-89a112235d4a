import { HttpHeaders, HttpParams } from '@angular/common/http';
import { GenericError, ResponseTypeEnum } from './generic-response';
import { EndpointKey } from '../common';

export type RestObserve = 'body' | 'events' | 'response';

export type RestParams =
  | HttpParams
  | {
      [param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>;
    };

export type RestHeaders =
  | HttpHeaders
  | {
      [header: string]: string | string[];
    };

export interface RestConfig {
  baseUrl?: string;
  desc?: string;
  headers?: RestHeaders;
  autoErrorHandling?: boolean;
  observe?: RestObserve;
  queryParams?: RestParams;
  withBrandCode?: boolean;
  withCache?: number | boolean;
  responseType?: ResponseTypeEnum;
  service?: EndpointKey;
}

export interface RestRequest<T = null> {
  body?: T;
  headers?: RestHeaders;
  method: 'GET' | 'HEAD' | 'POST' | 'PUT' | 'PATCH' | 'DELETE' | 'CONNECT' | 'TRACE' | 'OPTIONS';
  params?: RestParams;
  observe?: 'body' | 'events' | 'response';
  reportProgress?: boolean;
  responseType?: ResponseTypeEnum;
  withCredentials?: boolean;
  url: string;
}

export interface RestError {
  autoErrorHandling: boolean;
  response: GenericError;
  status: number;
  type: string;
}
