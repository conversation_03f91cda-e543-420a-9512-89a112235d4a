import { GeneralParameterContent, GeneralParameterList } from '../dto';
import { GeneralStatus } from '../common';
import { ContactMediumType } from '../lov';
import { GeneralType } from '../common';
import { Notification } from '../notification';

export namespace CapturedPartyPrivacy {
  export type Content = GeneralParameterContent<
    Partial<{
      description: string;
      items: Item[];
      name: string;
      partyPrivacyId?: number;
      partyPrivacySpecId: number;
      id: number;
      shortCode: string;
      sortId: string | number;
      hasAdditionalDocument: boolean;
      authorizedRequiredToPerformInteraction: boolean;
    }>
  >;

  export type ConsentItemStatus = 'AUTHORIZED' | 'UNAUTHORIZED' | 'UNKNOWN';

  export type Item = GeneralParameterContent<
    Partial<{
      partyPrivacyId?: number; // ui tarafında eklenmiştir.
      authorizedFlag: boolean;
      canOptOut: boolean;
      contactDataExtension: string;
      contactMediumData: string;
      contactDataPrefix: string;
      contactMediumId: number;
      contactMediumType: string;
      externalNotificationChannelType: string;
      consentItemStatus: ConsentItemStatus;
      consentAcquisitionType: string;
      notificationChannelType: Notification.NotificationChannelTypes;
      partyPrivacyItemId: number;
      startDate: Date | number;
      mergedContactData: string;
    }>
  >;

  // export interface PartyPrivacyDefaultRequest {
  //   partyRoleId?: number;
  //   keyCustomer?: BSS.KeyCustomer;
  // }

  export interface CustomerOrderBsnInterSpec {
    bsnInterSpecId: number;
    shortCode: string;
    name: string;
    description: string;
    isVsbl: number;
  }

  export interface PartyPrivacySpecValidStrategy {
    partyPrivSpecVldStrgyId: number;
    partyPrivSpecId: number;
    partyPrivSpec: PartyPrivacySpec;
    bsnInterSpecId: number;
    bsnInterSpec: CustomerOrderBsnInterSpec;
    operShortCode: string;
    expireDateStId: number;
    validForStrategy: GeneralType;
    vldForStrgyId: number;
    vldForStrgyVal: string;
  }

  export interface PartyPrivacySpecContactMediumTypeRelation {
    id: number;
    partyPrivacySpecId: number;
    contactMediumTypeId: number;
    contactMediumTypeShortCode: string;
  }

  export interface PartyPrivacySpecNotificationTopicRelation {
    id: number;
    partyPrivacySpecId: number;
    notificationTopicId: number;
    notificationTopicShortCode: string;
  }

  export interface PartyPrivacySpecPartyRoleTypeRelation {
    id: number;
    partyPrivacySpecId: number;
    partyRoleTypeId: number;
    partyRoleTypeShortCode: string;
    defaultDefinitionAllowed: number;
  }

  export interface PartyPrivacySpecNotificationChannelConfig {
    id: number;
    notificationChannel: Notification.KeyNotificationChannel;
    defaultStatus: GeneralStatus;
    contactMediumRelated: boolean;
    canOptOut: boolean;
    canUnsub: boolean;
    boundedContactMediumTypeIdList: number[];
    partyPrivacySpecValidStrategyList: PartyPrivacySpecValidStrategy[];
    partyPrivSpecId: number;
  }

  export interface PartyPrivacySpecCharVal extends GeneralParameterList {
    partyPrivacySpecCharValId: number;
    charId: number;
    charValId: number;
    charShortCode: string;
    charValShortCode: string;
    charName: string;
    charValName: string;
    val: string;
  }

  export interface PartyPrivacySpecProductOfferRelation {
    id: number;
    partyPrivacySpecId: number;
    productOfferId: number;
  }

  export interface PartyPrivacySpecCustomerTypeRelation {
    id: number;
    partyPrivacySpecId: number;
    customerTypeId: number;
    customerTypeShortCode: string;
  }

  export interface PartyPrivacySpecChar extends GeneralParameterList {
    partyPrivacySpecCharId: number;
    charId: string;
    name: string;
    shortCode: string;
    charValList: PartyPrivacySpecCharVal;
  }

  export interface PartyPrivacySpec extends GeneralParameterList {
    id?: number;
    partyPrivacySpecId: number;
    levelType: GeneralType;
    partyPrivacyType: GeneralType;
    partyPrivacyUsageType: GeneralType;
    shortCode: string;
    defaultStatus: GeneralStatus;
    sortId: number;
    name: string;
    description: string;
    defaultDefinitionAllowed: boolean;
    charList: PartyPrivacySpecChar[];
    notificationTopicRelationList: PartyPrivacySpecNotificationTopicRelation[];
    notificationChannelConfigList: PartyPrivacySpecNotificationChannelConfig[];
    contactMediumTypeRelationList: PartyPrivacySpecContactMediumTypeRelation[];
    partyRoleTypeRelationList: PartyPrivacySpecPartyRoleTypeRelation[];
    customerTypeRelationList: PartyPrivacySpecCustomerTypeRelation[];
    productOfferRelationList: PartyPrivacySpecProductOfferRelation[];
    contactMediumRelated: boolean;
    manageableAtNotificationChannelLevel: boolean;
  }

  export interface PartyPrivacyDefault extends GeneralParameterList {
    partyPrivacySpec: PartyPrivacySpec;
    contactMediumType: ContactMediumType.ContactMediumType;
    status: GeneralStatus;
    authorizedFlag: boolean;
    consentItemStatus: ConsentItemStatus; // added for ui
  }

  // export interface PartyPrivacyDefaultSaveRequest extends PartyPrivacyDefaultRequest {
  //   partyPrivacyDefaultList: PartyPrivacyDefault[];
  // }

  export interface Result {
    // customerId is pathVariable
    customerId?: number;
    customerTypeId: number;
    partyRoleId: number;
    dataType?: string;
    partyRoleTypeId: number;
    dataRowId?: number;
    privacyList: Content[];
    businessInteractionShortCode?: string;
    // token is pathVariable
    token?: string;
  }
}
