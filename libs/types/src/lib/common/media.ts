export type Media = {
  facetName: string;
  mediaUrl: string;
  name: string;
  priority: number;
  targetUrl: string;
  type: MediaType;
  multiLanguageData?: MultiLanguageData[];
};

export const enum MediaType {
  PICTURE = 'PICTURE',
  VIDEO = 'VIDEO',
  MULTI_VIEW = 'MULTI_VIEW',
}

export type MultiLanguageData = {
  id: number;
  language: string;
  isActive: number;
  mediaId: number;
  name: string;
  mediaUrl: string;
  parentId: number;
};
