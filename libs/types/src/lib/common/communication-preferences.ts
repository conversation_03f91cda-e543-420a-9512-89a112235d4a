import { CapturedPartyPrivacy } from '../privacy-specification';

export namespace CommunicationPreference {
  export interface State {
    commPref: CapturedPartyPrivacy.Result;
    subscription: CapturedPartyPrivacy.Result;
    unsubscription: CapturedPartyPrivacy.Result;
    partyRoleDefaultPartyPrivacies: CapturedPartyPrivacy.PartyPrivacyDefault[];
    customerContactMediumConsent: CapturedPartyPrivacy.Result;
  }

  export interface CustomerContactMediumConsentRequest {
    mediumType: string;
    customerId: number;
  }

  export type PartyPrivacySearchRequestForCrud = Partial<{
    contactMediumIdList: number[];
    notificationChannelShortCodeList: string[];
    usageTypeShortCodeList: string[];
    onlyListOptOutAllowed: boolean;
    willBeUsedByUnsubscription: boolean;
    customerTypeId: number;
    partyRoleTypeId: number;
    targetPartyRoleTypeId: number;
    dataRowId: number;
    dataType: number;
    partyRoleId: number;
    ownerCustomerId: number;
    filterDefaultDefinitionAllowed: boolean;
  }>;
}
