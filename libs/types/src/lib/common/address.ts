export type Address = Partial<{
  id: number;
  rowId: number;
  addressType: keyof typeof AddressType;
  addressLabel: string;
  countryId: number;
  countryName: string;
  countryCode: string;
  stateId: number;
  stateName: string;
  stateCode: string;
  cityId: number;
  cityName: string;
  postalCode: string;
  phoneNumber: string;
  addressContact: string;
  addressDescription: string;
  extMarketingRegionId: string;

  addressTypeId: number;
  allowDelete: boolean;
  allowUpdate: boolean;
  createDate: string;
  createdBy: number;
  dataTypeId: number;
  isActive: boolean;
  isPrimary: boolean;
  legalAddress: boolean;

  /*cdate: Date | number; // TODO add if necessary
  edate: Date | number;
  sdate: Date | number;
  udate: Date | number;
  isActv: number;
  isMigrated: number;
  isPrm: number;
  isVld: number;
  addrId: number;
  addrLat: number;
  addrLong: number;
  addrPrflId: number;
  addrTpId: number;
  bldgId: number;
  bldgNo: number;
  cntryId: number;
  condoUnitId: number;
  createModTpId: number;
  cuser: number;
  dataTpId: number;
  domesticId: number;
  dstrctId: number;
  ngbrhdId: number;
  strtId: number;
  strtTpId: number;
  subPrvncId: number;
  usgTpId: number;
  uuser: number;
  vlgId: number;
  addrCntc: string;
  addrDesc: string;
  addrDesc2: string;
  addrDesc3: string;
  addressDescription1: string;
  addressDescription2: string;
  addrFreeField: string;
  addrLbl: string;
  addrLblFr: string;
  alias: string;
  avenueName: string;
  bldgName: string;
  civicNumber: string;
  cntryCode: string;
  cntryName: string;
  dataLvl: string;
  doorNum: string;
  dstrctName: string;
  extAddrType: string;
  extCrtcRegionId: string;
  extPostAddrId: string;
  extSimsubRegionId: string;
  floorNum: string;
  houseName: string;
  houseNum: string;
  legalAddress: boolean;
  mainStrtName: string;
  muniName: string;
  ngbrhdName: string;
  orientation: string;
  poBoxNo: string;
  primaryAddress: boolean | number;
  locality: string;
  subAddrTp: string;
  lclCod: string;
  lclNum: string;
  phnNmbr: string;
  pstlCode: string;
  siteName: string;
  streetNrSuffix: string;
  streetSuffix: string;
  strtDir: string;
  strtName: string;
  strtNum: string;
  strtTpName: string;
  subAddrId: string;
  subBldg: string;
  subBldgNo: string;
  subPrvncCode: string;
  subPrvncName: string;
  suffix: string;
  uniqueAddrId: string;
  unitNo: string;
  vlgName: string;
  zoneUsageType: string;
  usageType: string;
  isVisible: boolean;
  addressOwnerContactName: string;
  id: number;
  subProvinceId: number;
  subProvinceName: string;
  addressDescription3: string;
  dataTypeId: number;*/
}>;

export enum AddressType {
  Installation = 'Installation',
  Customer = 'Customer',
  Billing = 'Billing',
  Shipment = 'Shipment',
  Consumer = 'Consumer',
  Contact = 'Contact',
  Party = 'Party',
  CustomerPrimaryAddress = 'CustomerPrimaryAddress',
  CustomerNonPrimaryAddress = 'CustomerNonPrimaryAddress',
}
