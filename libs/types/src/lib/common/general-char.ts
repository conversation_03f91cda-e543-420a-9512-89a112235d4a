import { GeneralCharValue } from './general-char-value';
import { SimpleGeneralChar } from './simple-general-char';
import { CharValueTypeEnum } from './char-value-type.enum';
import { CharDisplayTypeEnum } from './char-display-type.enum';
import { CharTypeEnum } from './char-type.enum';
import { ProdOfferCharGroup } from '../offer';

export interface GeneralChar extends SimpleGeneralChar {
  autoValidate: boolean;
  billingChar: boolean;
  charId: number;
  charName: string;
  charType: CharTypeEnum;
  currentValue: GeneralCharValue;
  description: string;
  displayOrder: number;
  displayType: CharDisplayTypeEnum;
  displayTypeEntCodeName: string;
  editable: boolean;
  effectsPrice: boolean;
  identityValue1: boolean;
  identityValue2: boolean;
  identityValue3: boolean;
  locationDepend: boolean;
  lov: boolean;
  optional: boolean;
  prime: boolean;
  provisionChar: boolean;
  resourceIndicator: boolean;
  searchable: boolean;
  transferable: boolean;
  valueList: GeneralCharValue[];
  valueType: CharValueTypeEnum;
  visible: boolean;
  prodOfferCharGroupList: ProdOfferCharGroup[];
}
