import { UnitOfMeasurement } from './unit-of-measurement';

export interface GeneralCharValue {
  _default: boolean;
  charId: number;
  charShortCode: string;
  charValue: string;
  charValueDescription: string;
  charValueDisplayHintCode: string;
  charValueId: number;
  charValueLabel: string;
  disabled: boolean;
  disabledReason: string;
  maxValue: number;
  minValue: number;
  scrOrd: number;
  shortCode: string;
  unitOfMeasurement: UnitOfMeasurement;
  valueIncrement: string;
  visible: boolean;
}

export interface OfferCharValue extends GeneralCharValue {
  val: string;
}
