import { OperationResult, Response } from '..';

export namespace Notification {
  export interface State {
    notificationResponse: NotificationResponse;
    updateNotificationsResponse: OperationResult;
  }

  export interface Notification {
    userNotificationId: number;
    read: boolean;
    title: string;
    keyNotificationChannel: KeyNotificationChannel;
    message: string;
    notificationDate: string;
  }

  export interface NotificationResponse {
    userNotificationList: Response.Result<Notification>;
    unreadMessageCount: number;
  }

  export interface CustomerOrderShipmentInfo {
    customerOrderId: number;
    customerOrderItemId: number;
    requestDeliveryDate: number;
    actualDeliveryDate: number;
    shipmentDate: number;
    status: ShipmentStatus;
    trackingLink: string;
    trackingNumber: string;
  }

  export interface NotificationRequest {
    customerId: number;
    page?: number;
    size?: number;
  }

  export interface UpdateNotificationRequest {
    read: number;
    notificationHistoryId: number;
  }

  export interface UserNotification {
    userNotificationId: number;
  }

  export const enum ShipmentStatus {
    SHIPPED = 'SHIPPED',
    DELIVERED = 'DELIVERED',
  }

  export interface KeyNotificationChannel {
    notificationChannelId: number;
    shortCode: NotificationChannelTypes;
    externalShortCode: string;
    name: string;
  }

  export const enum NotificationChannelTypes {
    EMAIL = 'EMAIL',
    SMS = 'SMS',
    TELEMARKETING = 'TELEMARKETING',
    DIRECT_MAIL = 'DIRECT_MAIL',
  }
}
