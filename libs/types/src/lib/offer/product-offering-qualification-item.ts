import { Category } from '../common';
import { AlternateProductOfferingProposal } from './alternate-product-offering-proposal';
import { ProductOffering } from './product-offering';

export interface ProductOfferingQualificationItem {
  id: string;
  category: Category;
  alternateProductOfferingProposal: AlternateProductOfferingProposal[];
  contractType: string;
  serviceType: string;
  expoGroup?: Category[];
}

export interface ProductOfferingQualificationItemFlatten {
  alternateProductOffering: ProductOffering;
  category: Category;
}
