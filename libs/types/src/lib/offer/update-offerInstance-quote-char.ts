export interface UpdateOfferInstanceQuoteChar {
  customerOrderItemId?: number | string;
  shortCode: string;
  value: string;
  charTypes: OrderCharType[];
}

/**
 * Enum representing information about which char in the Quote object will be updated.
 */
export enum OrderCharType {
  /**
   * Represents a product character, corresponding to the 'productChars' field.
   */
  OFFER = 'OFFER',

  /**
   * Represents an offer instance character, corresponding to the 'offerInstanceChars' field.
   */
  OFFER_INSTANCE = 'OFFER_INSTANCE',
}
