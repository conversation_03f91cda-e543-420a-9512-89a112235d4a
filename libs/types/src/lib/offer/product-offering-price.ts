import { UnitOfMeasurement } from '../common';
import { Price, PriceItem, ProdSpecCharValueUse } from '../product';
import { ProductOfferingTerm } from './product-offering-term';

export interface ProductOfferingPrice {
  unitOfMeasure: UnitOfMeasurement;
  price: Price;
  prodSpecCharValueUse: ProdSpecCharValueUse[];
  productOfferingTerm: ProductOfferingTerm[];
  priceItem: PriceItem[];
  id: string;
  priceType: string;
  lifecycleStatus: string;
  recurringChargePeriodType: string;
}
