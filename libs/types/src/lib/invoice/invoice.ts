export namespace Invoice {
  // todo: names (interface, enum, action vs.) will refactored after be dev
  export interface CustomerInvoiceDetail {
    billingAccountId: number;
    accountName: string;
    amount: number;
    paymentStatus: string;
    invoiceDate: Date;
    dueDate: Date;
    currencyCode: string;
  }

  export interface GetInvoiceInfoQueryParams {
    billingAccountId: number;
    startDate?: string;
    startDueDate?: string;
    endDate?: string;
    endDueDate?: string;
    paymentStatus?: string;
    status?: string;
    paymentNumber?: number;
    invoiceNumber?: number;
    isOrganizationalCustomer?: boolean;
  }

  export interface GetInvoiceInfoResponse {
    invoiceInfos: InvoiceInfoType[];
  }

  export interface InvoiceInfoType {
    invoiceDefinition: InvoiceDefinitionType[];
    summary: InvoiceInfoSummaryType;
  }

  export interface InvoiceDefinitionType {
    billingAccountId: number;
    billingAccountName: string;
    billingPeriod: number;
    invoiceNumber: number;
    invoiceDate: string;
    dueAmount: number;
    dueDate: string;
    openAmount: number;
    totalPayableAmount: number;
    currencyCode: string;
    paymentStatus: string;
  }

  export interface InvoiceInfoSummaryType {
    totalDueAmount: number;
    totalOpenAmount: number;
    balance: number;
  }
}
