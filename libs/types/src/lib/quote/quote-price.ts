import { Currency, eOffer, KeyCustomerOrderId, KeyProductOffer, TaxInfo } from '..';

export namespace QuotePrice {
  export type PriceDetailRequest = KeyCustomerOrderId;

  export interface QuotePriceDetail {
    subscriptionDetailList: SubscriptionDetail[];
    firstInvoiceTotal: number;
    dueNowTotal: number;
    total: number;
    lateOutstandingBalance: LateOutstandingBalance;
  }

  export type SubscriptionDetail = Partial<{
    planPriceDetail: PriceDetail; //
    primaryDevicePriceDetail: DevicePriceDetail; //
    subscriptionTotal: number; //
  }>;

  export interface PriceDetail extends KeyProductOffer {
    offerName: string;
    name?: string;
    calculatedOfferPrice: CalculatedPrices;
    discounts: DiscountDetail[];
    installmentInfo: InstallmentInfo;
    displayInCheckoutShoppingCart: boolean;
    customerOrderItemId: number;
  }

  export type CalculatedPrices = {
    currency: Currency;
    delivery: number;
    // installmentPlan: BSS.InstallmentPlan;
    discountAppliedEipTaxRecurring: number;
    discountAppliedOneTime: number;
    discountAppliedRecurring: number;
    discountAppliedEipRecurring: number;
    discountAppliedTaxOneTime: number;
    discountAppliedTaxRecurring: number;
    discountAppliedTaxTotal: number;
    discountAppliedTaxUsage: number;
    discountAppliedTotal: number;
    discountAppliedUsage: number;
    nextRecurringTaxInfos: PriceTaxInfo[];
    oneTime: number;
    eipRecurring: number;
    oneTimeFeeSection?: OneTimeFeeSection;
    oneTimeSubItems: PriceSubItem[];
    oneTimeTaxInfos: PriceTaxInfo[];
    recurring: number;
    recurringSubItems: PriceSubItem[];
    recurringTaxInfos: PriceTaxInfo[];
    taxInfos: TaxInfo[];
    taxOneTime: number;
    taxRecurring: number;
    taxTotal: number;
    taxUsage: number;
    taxPaymentType: string;
    total: number;
    usage: number;
    usageSubItems: PriceSubItem[];
    usageTaxInfos: PriceTaxInfo[];
    vat: number;
    voucherDiscount: number;
    monthlyTotalWithoutFirstMonthDisc: number;
    recurringEipTaxInfos: PriceTaxInfo[];
    offerChargeType: eOffer.OfferChargeTypeShortCode;
  };

  export interface DevicePriceDetail extends PriceDetail {
    firstInvoice: boolean;
    quantity?: number;
    standaloneDevice: boolean;
  }

  export interface DiscountDetail extends KeyProductOffer {
    offerName: string;
    discountValue: number;
    endDate: Date;
  }

  export type PriceTaxInfo = {
    taxTypeName: string;
    taxTypeShortCode: string;
    taxValue: number;
  };

  export interface OneTimeFeeSection {
    oneTimeFeeItemList: OneTimeFeeItemList[];
    discountAppliedCalculated: number;
    discountAppliedTaxCalculated: number;
    totalCalculated: number;
    totalTaxCalculated: number;
  }

  export interface OneTimeFeeItemList {
    discountAppliedCalculated: number;
    discountAppliedTaxCalculated: number;
    totalCalculated: number;
    totalTaxCalculated: number;
    itemName: string;
    quantity: number;
    unitPrice: number;
  }

  export type PriceSubItem = {
    value: number;
    taxValue?: number;
    discountAppliedValue: number;
    discountAppliedTaxValue?: number;
    name: string;
    shortCode: string;
    situation?: string;
    description?: string;
  };
  export type LateOutstandingBalance = {
    totalAmount: number;
    items: LateOutstandingBalanceItem[];
    lateOutstandingBalanceByPass: boolean;
  };

  export interface LateOutstandingBalanceItem extends PaymentPriceItemBase {
    billAcctId: number;
  }
  export type PaymentPriceItemBase = {
    amount: number;
    amountWithEipTax: number;
    seqNumber: string;
  };

  export interface InstallmentInfo {
    amount: number;
    currentInstallmentNumber: number;
    installmentPlanName: string;
    paidAmount: number;
    remainingAmount: number;
    totalInstallmentNumber: number;
  }
}
