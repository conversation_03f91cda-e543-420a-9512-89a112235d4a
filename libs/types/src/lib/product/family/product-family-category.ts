import { ProductFamilyLanguageData } from './product-family-language-data';

export interface ProductFamilyCategory {
  shortCode: string;
  isActive: number;
  isRfs: number;
  productSpecificationFamilyId: number;
  id: number;
  multiLanguageData: ProductFamilyLanguageData[];
  subCategoryList: ProductFamilySubCategory[];
  name: string;
}

export interface ProductFamilySubCategory {
  productSpecificationFamilySubCategoryId: number;
  shortCode: string;
  isPrimary: number;
  isSec: number;
  isActive: number;
  typeId: number;
  isCustVsbl: number;
  multiLanguageData: ProductFamilyLanguageData[];
  name: string;
}
