import { ProductSpecificationFamily } from './product-specification-family';
import { ProductFamilyLanguageData } from './product-family-language-data';

export interface ProductSpecFamilySubcategory {
  productSpecificationFamilySubCategoryId: number;
  shortCode: string;
  isPrimary: number;
  isSec: number;
  isActive: number;
  typeId: number;
  multiLanguageData: ProductFamilyLanguageData[];
  prodSpecFmlyProdSpecEntities: ProductSpecificationFamily.ProdSpecEntity[];
}
