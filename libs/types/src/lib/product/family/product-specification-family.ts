import { ProductFamilyCategory } from './product-family-category';

export namespace ProductSpecificationFamily {
  export interface Response {
    productSpecificationFamilyId: number;
    shortCode: string;
    name?: string; // for Fe development
    isActive: number;
    sortId: number;
    isSalesRprstAssg: number;
    categoryList: ProductFamilyCategory[];
    multiLanguageData: FamilySpecLang[];
  }

  export interface FamilySpecLang {
    productSpecificationFamilyLanguageId: number;
    lang: string;
    name: string;
    description: string;
    isActive: number;
  }

  export interface ProdSpecEntity {
    productSpecificationFamilyProductSpecificationId: number;
    productSpecificationFamilySubCategoryId: number;
    prodSpecId: number;
    isActive: number;
  }
}
