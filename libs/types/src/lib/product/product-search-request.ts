import { eProduct } from './product.enum';
import { SimplePageRequest } from '../common';

export type ProductSearchRequest = Partial<
  {
    customerId: number;
    statusCodes: (keyof typeof eProduct.ProductStatusShortCodes)[];
    billingAccountId: number;
    msisdn: number;
    planBundleSummary: number;
    productSummary: number;
    productSummaryTypes: string[];
    productDetailList: number;
  } & SimplePageRequest
>;
