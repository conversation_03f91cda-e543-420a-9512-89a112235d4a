import { Address, GeneralStatus, GeneralType, Media, NoModelYet } from '../common';
import { eOffer } from '../offer';
import { Discount } from './discount';
import { ProductChar } from './product-char';
import { BillingAccount } from '../billing';
import { Response } from '../http';

export namespace Product {
  export interface ProductResponse {
    inquireProductDataItemList: Response.Result<Product>;
    planSummaryList: PlanSummary[];
    customerName?: string;
  }

  export interface Product {
    billingAccountId: number;
    billingAccountNumber: string;
    status: GeneralStatus;
    productDetailList: ProductDetail[];
    invoicingBillingAccount?: BillingAccount.InvoicingBillingAccount;
    appointmentList?: NoModelYet[];
    billingAddress?: Address;
    installationAddress?: Address;
    parentBillingAccount?: BillingAccount.BillingAccount;
    forwardStartDate?: number;
    balance?: Balance;
    paymentType?: GeneralType;
    endUserInformation: EndUserInformation;
    postParent?: boolean;
    count?: number;
    accountSubType?: string;
    productType?: string;
    productOfferId?: number;
    planBundleSummary: PlanBundleSummary;
    productSummaryList: ProductSummaryList;
  }

  export interface EndUserInformation {
    customerId: number;
    firstName: string;
    fullName: string;
    id: number;
    lastName: string;
    userName: string;
  }

  export interface PlanBundleSummary {
    name: string;
    productId: number;
    productType: string;
    productFamilyCategoryName: string;
    productFamilyCategoryCode: string;
    statusShortCode: string;
    subscriptionIdentityMap: SubscriptionIdentityMap;
  }

  export interface ProductSummaryList {
    name: string;
    productId: number;
    productType: string;
    productFamilyCategoryName: string;
    productFamilyCategoryCode: string;
    statusShortCode: string;
  }

  export interface PlanSummary {
    status: GeneralStatus;
    count: number;
  }

  export type SubscriptionIdentityMap = unknown;

  export interface ProductDetail {
    productId: number;
    productNumber: string;
    productSerialNumber: string;
    parentProductId: number;
    calculatedPriceValue: number;
    discountAppliedCalculatedPriceValue: number;
    status: GeneralStatus;
    name: string;
    firstActivationDate: number & Date;
    activationDate: number & Date;
    deactivationDate: number & Date;
    productOfferId: number;
    sortDate: Date & number;
    startDate: number;
    endDate: number;
    pairProdStatus: PairProdStatus;
    validUntilDate: number & Date;
    primary: boolean;
    commitment: Commitment;
    secondary: boolean;
    sortId: number;
    productType: ProductType;
    webName: string;
    offerChargeType: eOffer.OfferChargeType | string;
    productInstallmentInfo: InstallmentInfo;
    familyInfo: FamilyInfo;
    discountList: Discount[];
    productCharList: ProductChar[];
    productRelationList: NoModelYet[];
    medias: Media[];
    installationAddress: Address;
    futureDatedInteractionList: NoModelYet[];
  }

  export interface Commitment {
    name?: string;
    commitmentStartDate: number;
    commitmentEndDate: number;
  }

  export interface InstallmentInfo {
    productId: number;
    installmentPlanName: string;
    amount: number;
    totalAmount: number;
    remainingAmount: number;
    currentInstallmentNumber: number;
    totalInstallmentNumber: number;
    remainingInstallmentNumber: number;
    planStartDate: Date;
    planEndDate: Date;
    currency: NoModelYet;
    installmentStartDate: Date;
    installmentEndDate: Date;
    priceValue: number;
    initialAmount: number;
    paidAmount?: number;
    taxDetail: NoModelYet[];
    totalAmountWithoutTax: number;
  }

  export interface FamilyInfo {
    family: string;
    familyName: string;
    familyCategory: string;
    familyCategoryName: string;
    familySubCategory: string;
    familySubCategoryName: string;
    sortId: number;
    customerVisible: boolean;
  }

  export type ProductType =
    | 'plan'
    | 'msisdn'
    | 'subOffer'
    | 'device'
    | 'mobileByod'
    | 'addOn'
    | 'planBundle'
    | 'subscriptionIdentity'
    | 'resourceFacingService'
    | 'discount'
    | 'bucket';

  export interface Balance {
    dataAmount: number;
    voiceAmount: number;
    smsAmount: number;
    monetary: string;
  }

  export interface PairProdStatus {
    pendingActivation: boolean;
    pendingDeactivation: boolean;
  }
}
