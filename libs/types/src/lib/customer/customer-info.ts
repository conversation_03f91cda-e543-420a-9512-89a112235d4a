import { Address } from '../common';

export namespace CustomerInfo {
  export interface InquireCustomerInformationRequest {
    customerId: number;
  }

  export interface InquireCustomerInformationResponse {
    customerCreditScores: CustomerCreditScores[];
    customerInformation: CustomerInformation;
    partyInformation: PartyInformation;
    partyRoleInformation: PartyRoleInformation;
    userInformation: UserInformation;
  }

  export interface CustomerInformation {
    custSince: string;
    customerId: number;
    customerType: CustomerType;
  }

  export interface CustomerCreditScores {
    name: string;
    creditScoreName: string;
  }

  export interface CustomerType {
    description: string;
    externalShortCode: string;
    id: number;
    name: string;
    partyTypeId: number;
    shortCode: string;
    isActive?: boolean;
  }

  export interface PartyInformation {
    birthDate: string;
    email: string;
    firstName: string;
    fullName: string;
    lastName: string;
    partyId: number;
    occupation?: Occupation;
  }

  export interface Occupation {
    id: number;
    name: string;
    description: string;
    shortCode: string;
    entityCodeName: string;
    entityName: string;
    resourceKey: string;
    sortId: number;
    externalShortCode: string;
    externalShortCodeWithRollbackValue: string;
  }

  export interface PartyRoleInformation {
    partyRoleId: number;
    partyRoleTypeExternalShortCode: string;
  }

  export interface UserInformation {
    uname: string;
    language: CustomerLanguageInfo;
  }

  export interface CustomerLanguageInfo {
    collation: string;
    id: number;
    isDefault: number;
    name: string;
    shortCode: string;
  }

  export type InquireCustomerContactMediumRequest = Partial<{
    customerId: number;
    rowId: number;
    dataTpId: number;
    typeShortCode: string;
  }>;

  export interface InquireIndividualCustomerFormLovContentResponse {
    customerTypeList: CustomerTypeList[];
    languageList: LanguageList[];
    genderTypeList: GenderList[];
    occupationTypeList: OccupationList[];
    phoneType: PhoneType[];
    countryPhoneCodeList: CountryPhoneCodeList[];
  }

  export interface CustomerTypeList {
    customerTypeId: number;
    shortCode: string;
    name: string;
    description: string;
  }

  export interface LanguageList {
    languageId: number;
    name: string;
    shortCode: string;
    defaultLanguage: boolean;
  }

  export interface GenderList {
    genderId: number;
    shortCode: string;
    name: string;
    description: string;
  }

  export interface OccupationList {
    occupationId: number;
    shortCode: string;
    name: string;
    description: string;
  }

  export interface PhoneType {
    id: number;
    shortCode: string;
    name: string;
  }

  export interface CountryPhoneCodeList {
    countryPhoneCodeId: number;
    countryCode: string;
    countryName: string;
    countryPhoneCode: string;
  }

  export interface UpdateCustomerDemographicInfo {
    customerId: number;
    firstName: string;
    lastName: string;
    occupationShortCode?: string;
    langShortCode?: string;
    titleShortCode?: string;
    birthDate: number;
  }

  export interface UpdateCustomerAddressRequest {
    address: Address;
    bsnInterReasonShortCode?: string;
    dataTypeId?: number;
    bsnInterRowId?: number;
    customerId: number;
  }
}
