import { eBusinessFlow } from './business-flow.enum';
import { WorkflowRequest } from '../dto';

export namespace BusinessFlow {
  export interface ApplicableInteractionsRequest {
    level: eBusinessFlow.Levels;
    additionalParameters?: Record<string, string>;
    billingAccountId?: number;
    customerId: number;
  }

  export interface ApplicableInteractions {
    shortCode: string;
    name: string;
    flows: ApplicableInteractionFlow[];
  }

  export type ApplicableInteractionsResponse = ApplicableInteractions[];

  export interface ApplicableInteractionsFilterRequest extends ApplicableInteractionsRequest {
    filter?: ApplicableBusinessInteractionsFilter;
  }

  export interface ApplicableBusinessInteractionsFilter {
    ids: number[];
    type: string;
  }

  export interface ApplicableBusinessInteractionsFilterResponse {
    applicableInteractions: ApplicableInteractions[];
    id: number;
  }

  export interface ApplicableInteractionFlow {
    shortCode: string;
    name: string;
  }

  export interface BusinessFlowRequest extends WorkflowRequest {
    businessInteractionId: number;
    businessFlowSpecShortCode: string;
    businessInteractionShortCode: string;
    customerId: number;
    billingAccountId: number;
    productId: number;
    additionalParameters?: Record<string, string>;
  }

  export interface BusinessFlowResponse {
    businessInteraction: BusinessInteractionDetail;
    additionalInfo?: Record<string, string>;
  }

  export interface BusinessInteractionDetail {
    id: number;
    specificationId: number;
    customerId: number;
    saleChannelId: number;
    dataTypeId: number;
    rowId: number;
    status: BusinessInteractionStatus;
    reason: BusinessInteractionReason;
    charValues: BusinessInteractionCharValueDetail[];
  }

  export interface BusinessInteractionCharValueDetail {
    shortCode: string;
    value: string;
    name: string;
    description: string;
  }

  export interface BusinessInteractionStatus {
    id: number;
    shortCode: string;
    name: string;
    description: string;
  }

  export interface BusinessInteractionReason {
    shortCode: string;
    multiLanguage: Record<string, string>;
  }
}
