import { ePayment } from './ePayment';

export namespace PaymentMethod {
  export type SavePaymentMethod = Partial<{
    customerId: number;
    paymentMethodTypeId: number;
    name: string;
    description: string;
    shortCode: string;
    priority: number;
    paymentMethodCharacteristics: PaymentMethodTypeCharacteristics[];
    paymentMethodType: PaymentMethodDetails;
  }>;

  export interface PaymentMethods {
    paymentMethodId: number;
    paymentMethodTypeId?: number;
    customerId: number;
    name: string;
    billingAccountId: number;
    isDefault: number | boolean;
    createDate: string | Date;
    isPreAuthorized: number | boolean;
    isRemovable?: boolean;
    paymentMethodType: Partial<SimplePaymentMethodType>;
    paymentMethodCharacteristics: PaymentMethod.PaymentMethodChar[];
  }

  export type PaymentMethodDetails = Partial<{
    id: number;
    paymentMethodTypeId: number;
    paymentMethodId: number;
    name: string;
    canPap: number;
    description: string;
    shortCode: string;
    priority: number;
    paymentMethodTypeCharacteristics: PaymentMethod.PaymentMethodDetailTypeCharacteristics[];
    isActive: boolean;
  }>;

  export interface PaymentMethodChar {
    charId: number;
    name: string;
    description: string;
    shortCode: string;
    visible: number;
    paymentMethodId: number;
    isSecure: number;
    encTpId: number;
    priority: number;
    characteristicValue: Partial<PaymentMethodCharVal>;
  }

  export interface PaymentMethodCharVal {
    charValueId: number;
    shortCode: string;
    value: string;
    valueLabel: string;
  }

  export interface PaymentMethodTypeCharUse {
    charId: number;
    name: string;
    description: string;
    visible: number;
    optional: number;
    editable: number;
    sortId: number;
    regularExpression: SimpleRegExpr;
    characteristicValue: PaymentMethodTypeCharValUse;
  }

  export interface PaymentMethodTypeCharValUse {
    charValueId: number;
    shortCode: string;
    value: string;
    valueLabel: string;
    isDflt: number;
    sortId: number;
  }

  export interface SimplePaymentMethodType {
    paymentMethodTypeId: number;
    name: string;
    description: string;
    shortCode: string;
    priority: number;
    canDueNow: number;
    canPap: number;
    canInstantiable: number;
    paymentMethodTypeCharacteristics: Partial<PaymentMethodTypeCharUse[]>;
  }

  export interface SimpleRegExpr {
    regExprId: number;
    shrtCode: string;
    expr: string;
  }

  export type PaymentMethodTypeCharacteristics = Partial<{
    charId: number;
    name: string;
    description: string;
    visible: number;
    optional: number;
    editable: number;
    sortId: number;
    shortCode: ePayment.PaymentMethodCharType;
    characteristicValue: PaymentMethodTypeCharValUse;
  }>;

  export type PaymentMethodDetailTypeCharacteristics = Omit<PaymentMethodTypeCharacteristics, 'characteristicValue'> & {
    characteristicValue: PaymentMethodTypeCharValUse[];
  };
}
