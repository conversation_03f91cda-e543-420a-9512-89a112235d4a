export namespace ePayment {
  export enum PaymentMethodType {
    CREDIT_CARD = 'CREDIT_CARD',
    PAY_IN_STORE = 'PAY_IN_STORE',
    BANK_ACCT = 'BANK_ACCT',
    PYMNT_METH = 'PYMNT_METH',
    OTHERS = 'OTHERS',
    PYMNT_METH_TP = 'PYMNT_METH_TP',
    WALLET = 'WALLET',
    PAYPAL = 'paypal',
  }

  export enum CreditCardType {
    MASTERCARD = 'mastercard',
    VISA = 'visa',
    AMERICANEXPRESS = 'americanexpress',
  }

  export enum PaymentMethodCharType {
    CARD_TYPE = 'cardType',
    LAST_FOUR_DIGIT = 'lastFourDigit',
    CARD_HOLDER = 'cardHolder',
    EXPR_DATE = 'exprDate',
    EXT_CARD_REF_ID = 'extCardRefId',
    BANK_NAME = 'bankName',
    IBAN = 'IBAN',
    BANK_ACCOUNT_NUMBER_DISPLAY = 'bankAccountNumberDisplay',
    NAME_ON_BANK_ACCOUNT = 'nameOnBankAccount',
  }
}
