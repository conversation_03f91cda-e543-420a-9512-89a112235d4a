export interface GeneralParameter {
  id: string;
  shortCode: string;
  name: string;
  description: string;
  value: string;
  valueResourceKey: string;
  valueClassName: string;
  validatorInterfaceName: string;
  valueTypeId: number;
  isUpdatable: number;
  startDate: Date;
  endDate: Date;
}

export interface GeneralParameterList {
  generalParameterList?: GeneralParameter[];
}

export type GeneralParameterContent<T> = T & GeneralParameterList;
