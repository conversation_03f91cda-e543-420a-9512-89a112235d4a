{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "cli": {"packageManager": "yarn", "analytics": false}, "newProjectRoot": "apps", "schematics": {"@schematics/angular:component": {"style": "none", "changeDetection": "OnPush", "skipTests": true}, "@schematics/angular:directive": {"skipTests": true}, "@schematics/angular:service": {"skipTests": true}, "@schematics/angular:pipe": {"skipTests": true}, "@schematics/angular:class": {"skipTests": true}, "@schematics/angular:guard": {"skipTests": true}, "@schematics/angular:resolver": {"skipTests": true}}, "projects": {"wsc": {"projectType": "application", "root": "apps/wsc", "sourceRoot": "apps/wsc/src", "prefix": "wsc", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/wsc", "index": "apps/wsc/src/index.html", "browser": "apps/wsc/src/main.ts", "tsConfig": "apps/wsc/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "apps/wsc/public"}], "styles": ["apps/wsc/src/styles.scss", "node_modules/@eds/components/dist/index.css"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "1MB", "maximumError": "1.5MB"}, {"type": "anyComponentStyle", "maximumWarning": "8kB", "maximumError": "16kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "wsc:build:production"}, "development": {"buildTarget": "wsc:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["apps/wsc/src/**/*.ts", "apps/wsc/src/**/*.html"], "eslintConfig": "apps/wsc/eslint.config.js"}}}}, "widgets": {"projectType": "library", "root": "libs/widgets", "sourceRoot": "libs/widgets/src", "prefix": "widget", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "libs/widgets/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/widgets/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/widgets/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["libs/widgets/src/**/*.ts", "libs/widgets/src/**/*.html"], "eslintConfig": "libs/widgets/eslint.config.js"}}}}, "widgets-web": {"projectType": "library", "root": "libs/widgets", "sourceRoot": "libs/widgets/src", "prefix": "widget", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/widget-web", "index": "apps/wsc/src/index.html", "browser": "libs/widgets/src/widgets.main.ts", "tsConfig": "libs/widgets/tsconfig.lib.json", "inlineStyleLanguage": "scss", "assets": ["libs/widgets/src/assets"], "styles": [], "scripts": []}, "configurations": {"production": {"tsConfig": "libs/widgets/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/widgets/tsconfig.lib.json"}}, "defaultConfiguration": "production"}}}, "types": {"projectType": "library", "root": "libs/types", "sourceRoot": "libs/types/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "libs/types/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/types/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/types/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["libs/types/src/**/*.ts", "libs/types/src/**/*.html"]}}}}, "core": {"projectType": "library", "root": "libs/core", "sourceRoot": "libs/core/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "libs/core/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/core/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/core/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["libs/core/src/**/*.ts", "libs/core/src/**/*.html"]}}}}, "plugins": {"projectType": "library", "root": "libs/plugins", "sourceRoot": "libs/plugins/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "libs/plugins/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/plugins/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/plugins/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["libs/plugins/src/**/*.ts", "libs/plugins/src/**/*.html"]}}}}, "magwid": {"projectType": "library", "root": "libs/magwid", "sourceRoot": "libs/magwid/src", "prefix": "magic-widget", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "libs/magwid/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/magwid/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/magwid/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["libs/magwid/src/**/*.ts", "libs/magwid/src/**/*.html"], "eslintConfig": "libs/magwid/eslint.config.js"}}}}, "bss": {"projectType": "library", "root": "libs/bss", "sourceRoot": "libs/bss/src", "prefix": "bss", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "libs/bss/ng-package.json"}, "configurations": {"production": {"tsConfig": "libs/bss/tsconfig.lib.prod.json"}, "development": {"tsConfig": "libs/bss/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["libs/bss/src/**/*.ts", "libs/bss/src/**/*.html"]}}}}}}