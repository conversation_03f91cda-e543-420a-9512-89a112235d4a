{"name": "bstp-wsc", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "lint": "ng lint", "format": "prettier --write \"./**/*.{ts,mts,js,mjs,html,scss,json}\"", "postinstall": "husky", "start:wsc": "ng serve", "build:wsc": "ng build wsc", "build:wsc:new-ui": "ng build wsc --base-href=/wsc-new-ui/"}, "private": true, "lint-staged": {"*.{html,ts}": "eslint --cache --cache-location=node_modules/.eslintcache", "*.{html,json,md,scss,ts}": "yarn prettier --write", "public/assets/i18n/*.json": "transloco-validator"}, "validate-branch-name": {"pattern": "(^(feature|fix)\\/((BSTP|EM)[0-9]?-)[/[0-9]+]?$)|(^(external|revert|cherry_pick|merge|release)\\/([A-Za-z0-9].*)$)", "errorMsg": "Branch name not allowed. Check out \"Branching examples\" section in CONTRIBUTING.md"}, "config": {"commit-message-validator": {"pattern": "^(build|ci|docs|feat|fix|perf|refactor|test|external)\\([^\\s()]+\\): .{1,500}$", "errorMessage": "The commit message is invalid. Please consult the \"Commit message examples\" section in CONTRIBUTING.md for proper formatting."}}, "dependencies": {"@angular/animations": "19.2.9", "@angular/cdk": "19.2.14", "@angular/common": "19.2.9", "@angular/compiler": "19.2.9", "@angular/core": "19.2.9", "@angular/elements": "19.2.9", "@angular/forms": "19.2.9", "@angular/platform-browser": "19.2.9", "@angular/platform-browser-dynamic": "19.2.9", "@angular/router": "19.2.9", "@eds/components": "0.3.91", "@grafana/faro-web-sdk": "^1.18.2", "@grafana/faro-web-tracing": "^1.18.2", "@jsverse/transloco": "7.6.1", "@ngxs/store": "19.0.0", "angularx-qrcode": "19.0.0", "date-fns": "4.1.0", "imask": "7.6.1", "keycloak-js": "25.0.6", "rxjs": "7.8.2", "tslib": "2.8.1"}, "devDependencies": {"@angular-devkit/build-angular": "19.2.10", "@angular-eslint/builder": "19.3.0", "@angular-eslint/eslint-plugin": "19.3.0", "@angular-eslint/eslint-plugin-template": "19.3.0", "@angular-eslint/schematics": "19.3.0", "@angular-eslint/template-parser": "19.3.0", "@angular/cli": "19.2.10", "@angular/compiler-cli": "19.2.9", "@typescript-eslint/eslint-plugin": "8.31.1", "@typescript-eslint/parser": "8.31.1", "angular-eslint": "19.3.0", "commit-message-validator": "1.0.2", "eslint": "9.25.1", "eslint-config-prettier": "10.1.2", "husky": "9.1.7", "lint-staged": "15.5.1", "lit": "3.3.0", "ng-packagr": "19.2.2", "prettier": "3.5.3", "typescript": "5.8.3", "validate-branch-name": "1.3.2"}}