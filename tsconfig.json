/* To learn more about Typescript configuration file: https://www.typescriptlang.org/docs/handbook/tsconfig-json.html. */
/* To learn more about Angular compiler options: https://angular.dev/reference/configs/angular-compiler-options. */
{
  "compileOnSave": false,
  "compilerOptions": {
    "outDir": "./dist/out-tsc",
    "strict": true,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": false,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "skipLibCheck": true,
    "isolatedModules": true,
    "esModuleInterop": true,
    "experimentalDecorators": true,
    "moduleResolution": "node",
    "importHelpers": true,
    "target": "ESNext",
    "module": "ESNext",
    "strictNullChecks": false,
    "paths": {
      "widgets": ["./dist/widgets"],
      "types": ["./dist/types"],
      "plugins": ["./dist/plugins"],
      "magwid": ["./dist/magwid"],
      "core": ["./dist/core"],
      "bss": ["./dist/bss"],
      "@libs/widgets": ["./libs/widgets/src/public-api.ts"],
      "@libs/types": ["./libs/types/src/public-api.ts"],
      "@libs/plugins": ["./libs/plugins/src/public-api.ts"],
      "@libs/magwid": ["./libs/magwid/src/public-api.ts"],
      "@libs/core": ["./libs/core/src/public-api.ts"],
      "@libs/bss": ["./libs/bss/src/public-api.ts"]
    }
  },
  "angularCompilerOptions": {
    "enableI18nLegacyMessageIdFormat": false,
    "strictInjectionParameters": true,
    "strictInputAccessModifiers": true,
    "strictTemplates": true
  }
}
