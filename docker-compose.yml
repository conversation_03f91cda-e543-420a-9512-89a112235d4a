version: '3'
services:
  wsc:
    image : oci-docker-registry.etiya.com/bstp/dev/bstp-bss-wsc-new-ui:0.0.1
    build:
      context: .
      args:
        APP_TYPE: wsc
        APP_FOLDER: dist/wsc
    hostname : wsc.local
    container_name : wsc
    volumes:
      - '../config/wsc/frontend.conf:/etc/nginx/conf.d/frontend.conf'
      - '../config/wsc/environment.json:/usr/share/nginx/html/assets/config/environment.json'
    ports:
      - "80:8080"
    tty: true
