# Contributing to bpug-bss-ui

- [Submission Guidelines](#submit)
- [Coding Rules](#rules)
- [Branch Naming Convention](#branching)
- [Commit Message Guidelines](#commit)

## <a name="submit"></a> Submission Guidelines

### <a name="submit-pr"></a> Submitting a Pull Request (PR)

Before you submit your Pull Request (PR) consider the following guidelines:

1. In your local repository, make your changes in a new git branch:

   ```shell
   git checkout -b <YOUR_BRANCH_NAME>
   ```

   > `YOUR_BRANCH_NAME` must follow the rules defined in [Branch Naming Convention](#branching)

2. Create your patch, **including appropriate test cases**.

3. Follow our [Coding Rules](#rules).

4. Commit your changes using a descriptive commit message that follows our [commit message conventions](#commit).
   Adherence to these conventions is necessary because release notes are automatically generated from these messages.

5. Push your branch to Bitbucket:

   ```shell
   git push origin <YOUR_BRANCH_NAME>
   ```

6. In Bitbucket, send a pull request to `develop` branch.

### Reviewing a Pull Request

The frontend team will review the pull request before merge. All tasks from code review feedback must be applied only to origin/<YOUR_BRANCH_NAME>. All questions asked in pull request must be replied by the contributer. No pull request must not be allowed to be merged before all questions answered and all tasks completed by the contributer.

#### Addressing review feedback

If we ask for changes via code reviews then:

1. Make the required updates to the code.
2. Re-run test suites to ensure tests are still passing.
3. Create a fixup commit and push to your Bitbucket repository (this will update your Pull Request). You will have two options to accomplish this:

   1. You can create a new commit for your fix

      ```shell
      git commit -m <YOUR_COMMIT_MESSAGE>
      git push
      ```

   2. Or you can create a fixup commit (For more info on working with fixup commits see [here](https://github.com/angular/angular/blob/master/docs/FIXUP_COMMITS.md)).

      ```shell
      git commit --all --fixup HEAD
      git push
      ```

The difference between two is that option 1 will take in place as whole diff commit standard while git fixup commits will not.

##### Updating the commit message

A reviewer might suggest changes to a commit message (for example, to add more context for a change or adhere to our [commit message guidelines](#commit)).
In order to update the commit message of the last commit on your branch:

1. Check out your branch:

   ```shell
   git checkout my-fix-branch
   ```

2. Amend the last commit and modify the commit message:

   ```shell
   git commit --amend
   ```

3. Push to your GitHub repository:

   ```shell
   git push --force-with-lease
   ```

> NOTE:<br />
> If you need to update the commit message of an earlier commit, you can use `git rebase` in interactive mode.
> See the [git docs](https://git-scm.com/docs/git-rebase#_interactive_mode) for more details.

#### After your pull request is merged

After your pull request is merged, **you can safely delete your branch** and pull the changes from the main (upstream) repository:

- Delete the remote branch on Bitbucket either through the Bitbucket web UI or your local shell as follows:

  ```shell
  git push origin --delete my-fix-branch
  ```

- Check out the default branch:

  ```shell
  git checkout develop -f
  ```

- Delete the local branch:

  ```shell
  git branch -D my-fix-branch
  ```

- Update your local base branch with the latest upstream version:

  ```shell
  git pull --ff upstream develop
  ```

## <a name="rules"></a> Coding Rules

To ensure consistency throughout the source code, keep these rules in mind as you are working:

- All features or bug fixes **must be tested** by one or more specs (unit-tests).
- All public API methods (shared directives, pipes, etc.) **must be documented**.
- We follow [Google's JavaScript Style Guide][js-style-guide] and [Etiya BPUG Frontend Standards](bpug-development-guide)

## <a name="branching"></a> Branch Naming Convention

We use the prefixes below as part of the branch names to categorize them:

- `fix/` => Typically used for fixing bugs against develop branch
- `feature/` => Used for specific feature work. Typically, this branches from and merges back into the development branch.
- `external/` => Used for uncategorized tasks to maintain develop branch.
- `revert/` => Used for reverted a merge commit.
- `cherry_pick/` => Holds cherry-picked commits to be merged into develop or release branches.
- `merge/` => Used for equalizing release branches after resolved conflicts locally.
- `release/` => Used for release tasks and long-term maintenance. Typically, this branches from the development branch to master branch and changes are merged back into the development branch.

### Branching examples

```text
fix/BPUG-2342
feature/BPUG-2342
external/update/dev-dependency
revert/BPUG-2342
cherry_pick/BPUG-2342
merge/bpug-test_to_bpug-master
release/2_3
```

If you named a branch incorrectly, you can rename it correctly in

**local**:

```shell
git branch -m <wrong-branch-name> <correct-branch-name>
```

**remote**:

```shell
git checkout <wrong-branch-name>
git branch -m <wrong-branch-name> <correct-branch-name>
git push origin -u <correct-branch-name>
git push origin --delete <wrong-branch-name>
```

### Emergency production

Some fixes are needed to be merged directly into the production base.

- `hotfix/` => Typically used to quickly fix the production branch

> Also note branch naming convention is protected by `validate-branch-name`. See [package.json](package.json#86) for regex pattern

## <a name="commit"></a> Commit Message Format

_This specification is inspired by and supersedes the [AngularJS commit message format][commit-message-format]._

We have very precise rules over how our Git commit messages must be formatted.
This format leads to **easier to read commit history**.

Each commit message consists of a **header**, a **body**, and a **footer**.

```text
<header>
<BLANK LINE>
<body>
<BLANK LINE>
<footer>
```

The `header` is **mandatory** and must conform to the [Commit Message Header](#commit-header) format.

The `body` is **optional** for all commits except for those of type "docs".
When the body is present it must be at least 20 characters long and must conform to the [Commit Message Body](#commit-body) format.

The `footer` is **optional**. The [Commit Message Footer](#commit-footer) format describes what the footer is used for and the structure it must have.

Any line of the commit message cannot be longer than 100 characters.

#### <a name="commit-header"></a>Commit Message Header

```text
<type>(<scope>): <short summary>
  │       │             │
  │       │             └─: Summary in present tense. Not capitalized. No period at the end.
  │       │
  │       └─: Commit Scope: <TASK_ID>|<PROJECT_SCOPE>|external|infra
  │
  └─: Commit Type: build|ci|docs|feat|fix|perf|refactor|test
```

The `<type>` and `<summary>` fields are mandatory, the `(<scope>)` field is only optional when there is not a `<TASK_ID>`.

> Also note that commit header is protected by `commit-message-validator`. See [package.json](package.json#91) for regex pattern

See the examples in [Commit message examples](final-example-commit)

##### Type

Must be one of the following:

- **build**: Changes that affect the build system or external dependencies (scopes: nx, npm)
- **ci**: Changes to our CI configuration files and scripts (scopes: jenkins, jest)
- **docs**: Documentation only changes
- **feat**: A new feature
- **fix**: A bug fix
- **perf**: A code change that improves performance
- **refactor**: A code change that neither fixes a bug nor adds a feature
- **test**: Adding missing tests or correcting existing tests

##### Scope

The scope should be `<TASK_ID>` or `<PROJECT_SCOPE>`

`<TASK_ID>` examples: `BPUG-2345`, `BPUG-1182`, etc.
`<PROJECT_SCOPE>` examples: `csr`, `wsc`, `core`, `bss`, etc.

##### Summary

Use the summary field to provide a succinct description of the change:

- use the imperative, present tense: "change" not "changed" nor "changes"
- don't capitalize the first letter unless it is not a `<TASK_ID>`
- no dot (.) at the end

#### <a name="commit-body"></a>Commit Message Body

Just as in the summary, use the imperative, present tense: "fix" not "fixed" nor "fixes".

Explain the motivation for the change in the commit message body. This commit message should explain _why_ you are making the change.
You can include a comparison of the previous behavior with the new behavior in order to illustrate the impact of the change.

#### <a name="commit-footer"></a>Commit Message Footer

The footer can contain information about breaking changes and is also the place to reference Jira issues, Jira tickets, and other PRs that this commit closes or is related to.

```text
BREAKING CHANGE: <breaking change summary>
<BLANK LINE>
<breaking change description + migration instructions>
<BLANK LINE>
<BLANK LINE>
Fixes #<TASK_ID>
```

Breaking Change section should start with the phrase "BREAKING CHANGE: " followed by a summary of the breaking change, a blank line, and a detailed description of the breaking change that also includes migration instructions.

##### <a name="final-example-commit"></a>Commit message examples

###### <a name="final-example-commit-only-mandatory"></a>A commit message with only mandatory fields

```text
feat(BPUG-1234): lets all user activities be handled by a route guard
```

###### <a name="final-example-commit-with-optional-field"></a>A commit message with optional field

```text
fix(BPUG-1298): adds conditional before processing form control

- see also: #BPUG-2341 for side effects
```

###### <a name="final-example-commit-all"></a>A commit message with all fields

```text
refactor(csr): removes duplicate models

- renamed dependent files and definitions for actions and services
- also created model based enums to keep singularity

BREAKING CHANGE: combined similar types into one

interface CustomerSection and type SectionCust combined as CustSection and
wrapped with Partial utility. All extensions using it may not define id field now

Fixes #BPUG-5432
```

### Revert commits

If the commit reverts a previous commit, it should begin with `revert: `, followed by the header of the reverted commit.

The content of the commit message body should contain:

- information about the SHA of the commit being reverted in the following format: `This reverts commit <SHA>`,
- a clear description of the reason for reverting the commit message.

Reverting commits must be took merge commits as a source:

```shell
git revert --no-edit <MERGE_COMMIT_ID> -m <PARENT_NUMBER>
```

### Cherry-pick commits

If the commit must be cherry-picked, in other words, if only specific commits must be equalized to target branch, a new `cherry_pick/` prefixed branch must be branched from target branch and include the commits:

commit to cherry-picked 1: 3f530fda4
commit to cherry-picked 2: af4890ca2
base branch: cherry_pick/BPUG-2343
target branch: bpug-test

```shell
git checkout bpug-test
git checkout -b cherry_pick/BPUG-2343
git cherry-pick 3f530fda4 af4890ca2
git push origin cherry_pick/BPUG-2343
```

### Merge Commits

Normally, equalizing HEAD between release branches is done by VCS on bitbucket. In case of conflicts appeared in a release branch, for merging between develop and release branches in local, below pattern can be followed:

base branch: bpug-test
target branch: bpug-master
swap branch: merge/bpug-test_to_bpug-master

1. Fetch all commits in a swap branch

   ```shell
   git checkout bpug-master                       // Switch to the target branch
   git checkout -b merge/bpug-test_to_bpug-master // Branch to the swap branch
   git pull origin bpug-test                      // Fetch all commits to equalize
   ```

2. Resolve all conflicts and commit with a merge commit generated by git-client in local system

3. Push resolved results with the merge commit into swap branch

   ```shell
   git push origin merge/bpug-test_to_bpug-master
   ```

4. Open a pull request for merging from merge/bpug-test_to_bpug-master to bpug-master

[commit-message-format]: https://docs.google.com/document/d/1QrDFcIiPjSLDn3EL15IJygNPiHORgU1_OOAqWjiDU5Y/edit#
[js-style-guide]: https://google.github.io/styleguide/jsguide.html
[bpug-development-guide]: https://etiyawiki.atlassian.net/wiki/spaces/BPPM/pages/**********/Standartlar
