import { ApplicationConfig, provideExperimentalZonelessChangeDetection } from '@angular/core';
import { provideRouter } from '@angular/router';
import { routes } from './app.routes';
import { ApiInterceptor, ErrorInterceptor, provideCoreStates, provideWscCore, provideTokens } from '@libs/core';
import {
  provideBrowserTitle,
  provideDate,
  provideIframe,
  provideKeycloak,
  provideMagicWidget,
  provideTranslate,
} from '@libs/plugins';
import { provideHttpClient, withInterceptors } from '@angular/common/http';
import { provideBssStates } from '@libs/bss';
import { analyticsProvider } from '@libs/plugins';

export const appConfig: ApplicationConfig = {
  providers: [
    provideHttpClient(withInterceptors([ApiInterceptor, ErrorInterceptor])),
    provideExperimentalZonelessChangeDetection(),
    provideRouter(routes),
    provideKeycloak(),
    provideMagicWidget(),
    provideCoreStates(),
    provideBssStates(),
    analyticsProvider(),
    provideTranslate(),
    provideIframe(),
    provideBrowserTitle(),
    provideTokens(),
    provideWscCore(),
    provideDate(),
  ],
};
