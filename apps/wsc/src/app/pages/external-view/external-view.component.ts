import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  effect,
  ElementRef,
  inject,
  OnDestroy,
  OnInit,
  signal,
  viewChild,
} from '@angular/core';
import { Store } from '@ngxs/store';
import { ConfigState, IframeManagerService, injectDebugMode } from '@libs/plugins';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { toSignal } from '@angular/core/rxjs-interop';
import { LoaderRemoveAction, StartLoginAction } from '@libs/core';
import { Auth, RemoteBI } from '@libs/types';

@Component({
  selector: 'wsc-external-view',
  templateUrl: './external-view.component.html',
  styleUrl: './external-view.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class ExternalViewComponent implements OnInit, OnDestroy {
  private store = inject(Store);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private domSanitizer = inject(DomSanitizer);
  private iframeManagerService = inject(IframeManagerService);
  debugMode = injectDebugMode();

  appUrl = signal<SafeResourceUrl>('');
  waitingBI = toSignal(this.iframeManagerService.BIList$);
  url = toSignal(this.route.url);
  oldWscOrigin: string;

  oldUI = viewChild<ElementRef>('oldUI');
  readyToListen = signal(false);

  constructor() {
    this.oldWscOrigin = this.store.selectSnapshot(ConfigState.getDeep('url.old-wsc'));

    effect(() => {
      const waitingBI = this.waitingBI();
      const readyToListen = this.readyToListen();
      const oldUI = this.oldUI();

      if (!oldUI || !waitingBI || !readyToListen) {
        return;
      }

      this.sendMessageToOldUI(this.waitingBI());
    });
    if (this.oldWscOrigin) {
      effect(() => {
        const path = this.url()?.join('/') || '';

        const fullUrl = `${this.oldWscOrigin}/${path}`;
        this.appUrl.set(this.domSanitizer.bypassSecurityTrustResourceUrl(fullUrl));
      });
    }
  }

  private messageListener: (event: MessageEvent) => void;

  ngOnInit(): void {
    if (this.oldWscOrigin) {
      this.listenForMessages();
    }
  }

  listenForMessages(): void {
    this.messageListener = (event: MessageEvent) => {
      if (this.debugMode()) {
        console.debug('Getting message from OLD UI', event);
      }

      if (event.origin !== new URL(this.oldWscOrigin).origin) {
        return;
      }

      if (event.data && typeof event.data === 'object' && event.data.type) {
        switch (event.data.type) {
          case 'READY_TO_LISTEN':
            this.readyToListen.set(true);
            break;
          case 'PAGE_NOT_FOUND':
            this.returnToWsc(event.data.url);
            break;
          case 'RESIZE_IFRAME':
            this.resizeIframe(event.data);
            break;
          case 'NAVIGATE':
            this.navigate(event.data);
            break;
          case 'LOGIN':
            this.login(event.data);
            break;
        }
      } else {
        console.warn('Invalid message format:', event.data);
      }
    };

    window.addEventListener('message', this.messageListener);
  }

  resizeIframe(payload: { height: number }) {
    this.oldUI().nativeElement.style.height = payload.height + 'px';
  }

  returnToWsc(url = '/'): void {
    this.router.navigateByUrl(url);
  }

  sendMessageToOldUI(payload: RemoteBI) {
    if (this.debugMode()) {
      console.debug('sending message TO OLD UI', payload);
    }

    setTimeout(() => {
      this.store.dispatch(new LoaderRemoveAction('REMOTE_BI_START_' + payload.flow));
    }, 1500);

    this.oldUI().nativeElement?.contentWindow.postMessage(payload, '*');
    this.iframeManagerService.BIList$.next(null);
  }

  ngOnDestroy(): void {
    if (this.messageListener) {
      window.removeEventListener('message', this.messageListener);
    }
  }

  private navigate(data: { url: string }) {
    this.router.navigate([data.url]);
  }

  private login(data: { data: Auth.LoginOptions }) {
    this.store.dispatch(
      new StartLoginAction({
        ...data?.data,
        redirectUri: '',
      }),
    );
  }
}
