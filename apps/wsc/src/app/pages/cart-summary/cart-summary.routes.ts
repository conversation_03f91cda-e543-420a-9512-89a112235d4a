import { Routes } from '@angular/router';
import { eBusinessFlow, LayoutTypeEnum } from '@libs/types';
import { CartSummaryComponent } from './cart-summary.component';
import { customerOrderIdResolver } from '../../resolvers';

export const cartSummaryRoutes: Routes = [
  {
    path: '',
    component: CartSummaryComponent,
    resolve: [customerOrderIdResolver],
    data: {
      title: 'cart-summary',
      layout: LayoutTypeEnum.CART,
      stepShortCode: eBusinessFlow.WorkflowStateType.CART_SUMMARY,
    },
  },
];
