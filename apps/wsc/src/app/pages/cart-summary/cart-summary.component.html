<layout-content-with-aside>
  <main>
    @if (isPlanChange()) {
      <magic-widget-plan-change-cart-summary
        [plans]="plans()"
        [familyCategoryName]="familyCategoryName()"
        (clearButton)="clearShoppingCart()"
      ></magic-widget-plan-change-cart-summary>
    } @else {
      <magic-widget-cart-summary
        [plans]="plans()"
        [familyCategoryName]="familyCategoryName()"
        (clearButton)="clearShoppingCart()"
      ></magic-widget-cart-summary>
    }
  </main>
  <aside>
    <div class="sticky">
      <magic-widget-shopping-card
        [actionButtonText]="isPlanChange() ? 'Complete Order' : 'Proceed To Checkout'"
        (onAction)="onCheckout()"
      ></magic-widget-shopping-card>
    </div>
  </aside>
</layout-content-with-aside>
