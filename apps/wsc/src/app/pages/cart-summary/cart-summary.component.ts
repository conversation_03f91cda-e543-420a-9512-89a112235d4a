import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject } from '@angular/core';
import {
  BusinessWorkflowStepService,
  CurrentState,
  GetQuotePriceDetailAction,
  QuoteCancelQuoteAction,
  QuoteClearQuoteAction,
  QuoteGetQuoteAction,
  QuoteService,
  QuoteState,
} from '@libs/bss';
import { checkoutSteps, PlanChangeCartSummaryMagicComponent, ShoppingCardMagicComponent } from '@libs/magwid';
import { ToasterService, TranslateService } from '@libs/plugins';
import { LayoutContentWithAsideComponent } from '@libs/widgets';
import { select, Store } from '@ngxs/store';
import { takeUntil } from 'rxjs';
import { injectDestroy } from '@libs/core';
import { Router } from '@angular/router';
import { switchMap } from 'rxjs/operators';
import { eBusinessFlow } from '@libs/types';
import { CartSummaryMagicComponent } from '@libs/magwid';

@Component({
  selector: 'wsc-cart-summary',
  templateUrl: './cart-summary.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    LayoutContentWithAsideComponent,
    ShoppingCardMagicComponent,
    PlanChangeCartSummaryMagicComponent,
    CartSummaryMagicComponent,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class CartSummaryComponent {
  private store = inject(Store);
  private router = inject(Router);
  private businessWorkflowStepService = inject(BusinessWorkflowStepService);
  private toasterService = inject(ToasterService);
  private translateService = inject(TranslateService);
  private quoteService = inject(QuoteService);
  private destroy$ = injectDestroy();

  quote = select(QuoteState.quote);

  plans = computed(() => {
    return this.quote()?.bundleOffers;
  });

  familyCategoryName = computed(() => {
    return this.quote()?.familyCategoryName;
  });

  isPlanChange = computed(() => {
    return this.quote()?.currentBusinessFlowSpecShortCode === eBusinessFlow.Specification.PACKAGE_CHANGE;
  });

  constructor() {
    this.businessWorkflowStepService.checkoutSteps.set(checkoutSteps);
    this.businessWorkflowStepService.detectStepFromPath('/cart');
  }

  onCheckout() {
    // this.quoteService.callNextState(
    //   eBusinessFlow.WorkflowStateType.PRE_VALIDATION,
    //   eBusinessFlow.WorkflowStateType.PRODUCT_CONF,
    // );
    if (this.isPlanChange()) {
      this.quoteService.submitPlanChangeQuote$().subscribe(() => {
        this.router.navigate(['/checkout/order-success']);
      });
    } else {
      this.businessWorkflowStepService.nextStep();
    }
  }

  clearShoppingCart(): void {
    const customerOrderId = this.store.selectSnapshot(CurrentState.currentCustomerOrderId);

    if (this.plans()?.length && customerOrderId) {
      this.store
        .dispatch(new QuoteClearQuoteAction(customerOrderId))
        .pipe(
          takeUntil(this.destroy$),
          switchMap(() => this.store.selectOnce(QuoteState.clearQuoteResponse)),
        )
        .subscribe({
          next: (result) => {
            if (result) {
              this.toasterService.success({
                title: 'Success',
                description: this.translateService.translate('toast.OPERATION_SUCCESS'),
              });
              this.store.dispatch([
                new QuoteGetQuoteAction({
                  customerOrderId: result.customerOrderId,
                  currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.CART_SUMMARY,
                }),
                new GetQuotePriceDetailAction({ customerOrderId: result.customerOrderId }),
                new QuoteCancelQuoteAction({ customerOrderId: result.customerOrderId }),
              ]);
            }
          },
          error: () => {
            this.toasterService.error({
              title: 'Error',
              description: this.translateService.translate('toast.BAD_REQUEST'),
            });
          },
        });
    }
  }
}
