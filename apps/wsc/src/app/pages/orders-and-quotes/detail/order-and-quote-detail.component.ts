import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { OrdersAndQuotesDetailMagicComponent, SubLayoutMagicComponent } from '@libs/magwid';

@Component({
  selector: 'wsc-order-and-quote-detail',
  templateUrl: './order-and-quote-detail.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [SubLayoutMagicComponent, OrdersAndQuotesDetailMagicComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class OrderAndQuoteDetailComponent {}
