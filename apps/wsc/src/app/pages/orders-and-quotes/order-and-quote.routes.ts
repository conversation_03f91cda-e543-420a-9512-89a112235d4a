import { Routes } from '@angular/router';
import { OrderAndQuoteListComponent } from './list';
import { OrderAndQuoteDetailComponent } from './detail';
import { orderAndQuoteDetailResolver } from './detail/order-and-quote-detail.resolver';

export const orderAndQuoteRoutes: Routes = [
  {
    path: '',
    component: OrderAndQuoteListComponent,
    data: {
      title: 'orderAndQuote',
    },
  },
  {
    path: ':customerOrderId',
    component: OrderAndQuoteDetailComponent,
    resolve: [orderAndQuoteDetailResolver],
    data: {
      title: 'orderAndQuote',
    },
  },
];
