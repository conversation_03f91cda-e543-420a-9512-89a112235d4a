import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { OrderAndQuoteMagicComponent, SubLayoutMagicComponent } from '@libs/magwid';

@Component({
  selector: 'wsc-order-and-quote-list',
  templateUrl: './order-and-quote-list.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [OrderAndQuoteMagicComponent, SubLayoutMagicComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class OrderAndQuoteListComponent {}
