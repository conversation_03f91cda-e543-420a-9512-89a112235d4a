import { Routes } from '@angular/router';
import { LayoutTypeEnum } from '@libs/types';
import { CheckoutWrapperComponent } from './checkout-wrapper';
import { OrderSuccessMagicComponent } from '@libs/magwid';
import { customerOrderIdResolver } from '../../resolvers';

export const checkoutRoutes: Routes = [
  {
    path: 'order-success',
    component: OrderSuccessMagicComponent,
    resolve: [customerOrderIdResolver],
    data: {
      layout: LayoutTypeEnum.CHECKOUT,
    },
  },
  {
    path: ':step',
    component: CheckoutWrapperComponent,
    resolve: [customerOrderIdResolver],
    data: {
      layout: LayoutTypeEnum.CHECKOUT,
    },
  },
];
