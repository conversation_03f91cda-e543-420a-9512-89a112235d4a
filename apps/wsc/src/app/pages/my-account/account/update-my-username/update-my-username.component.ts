import { ChangeDetectionStrategy, Component } from '@angular/core';
import { SubLayoutMagicComponent, UpdateMyEmailMagicComponent } from '@libs/magwid';

@Component({
  selector: 'wsc-update-my-username',
  templateUrl: './update-my-username.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [SubLayoutMagicComponent, UpdateMyEmailMagicComponent],
})
export class UpdateMyUsernameComponent {}
