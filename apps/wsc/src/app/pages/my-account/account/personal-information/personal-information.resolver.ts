import { inject } from '@angular/core';
import { Store } from '@ngxs/store';
import { GetApplicableInteractionsAction, InquireCustomerContactMediumAction } from '@libs/bss';
import { CurrentState } from '@libs/bss';
import { eBusinessFlow } from '@libs/types';

export const personalInformationResolver = () => {
  const store = inject(Store);
  const customerId = store.selectSnapshot(CurrentState.customerId);

  return store.dispatch([
    new InquireCustomerContactMediumAction({ customerId: customerId }),
    new GetApplicableInteractionsAction({
      level: eBusinessFlow.Levels.CUST_CONTACT_MEDIUM,
      customerId: customerId,
    }),
  ]);
};
