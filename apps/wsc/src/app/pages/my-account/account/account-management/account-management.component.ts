import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import {
  SubLayoutMagicComponent,
  ProfileInteractionsMagicComponent,
  AccountInteractionsMagicComponent,
  OtherInteractionsMagicComponent,
  MainInteractionsMagicComponent,
} from '@libs/magwid';

@Component({
  selector: 'wsc-account-management',
  templateUrl: './account-management.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [
    SubLayoutMagicComponent,
    ProfileInteractionsMagicComponent,
    AccountInteractionsMagicComponent,
    OtherInteractionsMagicComponent,
    MainInteractionsMagicComponent,
  ],
})
export class AccountManagementComponent {}
