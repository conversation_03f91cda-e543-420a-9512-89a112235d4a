import { Routes } from '@angular/router';
import { LayoutTypeEnum } from '@libs/types';
import { AccountManagementComponent } from './account-management/account-management.component';
import { PersonalInformationComponent } from './personal-information';
import { personalInformationResolver } from './personal-information/personal-information.resolver';
import { UpdateMyUsernameComponent } from './update-my-username';

export const accountRoutes: Routes = [
  {
    path: '',
    component: AccountManagementComponent,
    data: {
      page: 'accountManagement',
    },
  },
  {
    path: 'personal-information',
    data: {
      title: 'personalInformation',
      layout: LayoutTypeEnum.ACCOUNT,
      showCustomerStatus: true,
    },
    component: PersonalInformationComponent,
    resolve: [personalInformationResolver],
  },
  {
    path: 'update-username',
    data: {
      title: 'updateMyUsername',
      layout: LayoutTypeEnum.MEMBERSHIP,
    },
    component: UpdateMyUsernameComponent,
  },
];
