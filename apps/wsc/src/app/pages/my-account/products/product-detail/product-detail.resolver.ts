import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, ResolveFn } from '@angular/router';
import { SetCurrentBillingAccountIdAction } from '@libs/bss';
import { Store } from '@ngxs/store';

export const ProductDetailResolver: ResolveFn<unknown> = (route: ActivatedRouteSnapshot) => {
  const store = inject(Store);

  return store.dispatch(new SetCurrentBillingAccountIdAction(route.params.billingAccountId));
};
