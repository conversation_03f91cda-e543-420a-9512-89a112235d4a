import { ChangeDetectionStrategy, Component, computed } from '@angular/core';
import {
  DiscoverMagicComponent,
  ProductListMagicComponent as MagicWidgetProductListComponent,
  SubLayoutMagicComponent,
} from '@libs/magwid';
import { select } from '@ngxs/store';
import { MyProductsState } from '@libs/bss';

@Component({
  selector: 'wsc-product-list',
  templateUrl: './product-list.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [MagicWidgetProductListComponent, SubLayoutMagicComponent, DiscoverMagicComponent],
})
export class ProductListComponent {
  products = select(MyProductsState.products);

  isExistProducts = computed(() => {
    return (this.products().products ?? []).length > 0;
  });
}
