import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ActivationESimSuccessMagicComponent } from '@libs/magwid';
import { LayoutContentTopNavComponent } from '@libs/widgets';

@Component({
  selector: 'wsc-activation-esim-success',
  templateUrl: './activation-esim-success.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [ActivationESimSuccessMagicComponent, LayoutContentTopNavComponent],
})
export class ActivationESimSuccessComponent {}
