import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, inject } from '@angular/core';
import { ActivationESimMagicComponent } from '@libs/magwid';
import { LayoutContentTopNavComponent } from '@libs/widgets';
import { TranslatePipe } from '@libs/plugins';
import { Router } from '@angular/router';
import { Store } from '@ngxs/store';
import { CurrentState } from '@libs/bss';

@Component({
  selector: 'wsc-activation-esim',
  templateUrl: './activation-esim.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ActivationESimMagicComponent, LayoutContentTopNavComponent, TranslatePipe],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class ActivationESimComponent {
  private store = inject(Store);
  private router = inject(Router);

  backToProductDetail() {
    const billingAccountId = this.store.selectSnapshot(CurrentState.currentBillingAccountId);
    this.router.navigate([`my/products/${billingAccountId}`]);
  }
}
