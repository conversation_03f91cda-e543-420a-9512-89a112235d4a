@if (loader()) {
  <div class="full-page-loader backdrop" [ngClass]="{ backdrop: loaderBackdrop() }">
    <svg width="85" height="112" viewBox="0 0 85 112" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M84.5016 0H56.3327V23.2591H84.5016V0Z" fill="#FF7A00" />
      <path d="M84.5016 44.0157H56.3327V67.2748H84.5016V44.0157Z" fill="#FF7A00" />
      <path d="M84.5016 88.7409H56.3327V112H84.5016V88.7409Z" fill="#FF7A00" />
      <path opacity="0.6" d="M56.3328 88.7409H28.1639V112H56.3328V88.7409Z" fill="#FF7A00" />
      <path opacity="0.6" d="M56.3328 44.0157H28.1639V67.2748H56.3328V44.0157Z" fill="#FF7A00" />
      <path opacity="0.3" d="M28.1616 88.725H0V111.966H28.1616V88.725Z" fill="#FF7A00" />
      <path opacity="0.3" d="M28.1616 67.2438H0V88.725H28.1616V67.2438Z" fill="#FF7A00" />
    </svg>
  </div>
}
