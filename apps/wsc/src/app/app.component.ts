import { Component, DoChe<PERSON>, inject } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import '@eds/components';
import { PageLoaderComponent } from './components/page-loader/page-loader.component';
import { ChatbotService } from '../../../../libs/core/src/lib/services/chatbot.service';

@Component({
  selector: 'wsc-root',
  imports: [RouterOutlet, PageLoaderComponent],
  templateUrl: './app.component.html',
})
export class AppComponent implements DoCheck {
  private chatbotService = inject(ChatbotService);
  constructor() {
    this.chatbotService.initializeChatbot();
  }

  ngDoCheck() {
    this.chatbotService.checkChatBotExistence();
  }

  title = 'wsc';
}
