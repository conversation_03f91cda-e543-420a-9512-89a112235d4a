import { appConfig } from './app/app.config';
import { AppComponent } from './app/app.component';
import { environment } from './app/environments/environment';
import { Environment } from '@libs/types';
import { bootstrap, getExternalEnvironment } from '@libs/core';

const externalEnvironment = getExternalEnvironment(environment as Environment.Model);

bootstrap(externalEnvironment, environment, AppComponent, appConfig);
