const rootConfig = require('../../eslint.config.js');
const tsParser = require('@typescript-eslint/parser');

module.exports = [
  ...rootConfig,
  {
    files: ['**/*.ts'],
    languageOptions: {
      parser: tsParser,
      parserOptions: {
        project: ['tsconfig.app.json'],
        tsconfigRootDir: __dirname,
      },
    },
    rules: {
      '@angular-eslint/component-selector': [
        'error',
        {
          type: 'element',
          prefix: 'wsc',
          style: 'kebab-case',
        },
      ],
    },
  },
];
